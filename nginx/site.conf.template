server {
  include mime.types;
  default_type application/octet-stream;
  keepalive_timeout 65;

  gzip on;
  gzip_min_length 1k;
  gzip_types text/plain application/javascript text/css application/font-woff;
  gzip_disable "MSIE [1-6]\."
  gzip_buffers 32 4k;
  gzip_comp_level 1;

  listen 8080;
  root /www/dist;
  client_max_body_size 20m;


  location / {
    try_files $uri /index.html;
  }

  location /index.html {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
  }

  location /health {
    add_header Content-Type text/plain;
    return 200 "ok";
  }
}
