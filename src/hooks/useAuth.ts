import { checkExternalAuth } from '@/services/auth';
import { getRobotList } from '@/services/ce';
import { FetchSessionByTicket, FetchUserInfoBySession } from '@/utils/sso';
import { history, useDispatch } from '@magi/magi';
import { message } from 'antd';
import { useEffect, useState } from 'react';

interface IProps {
  tenantNo: string;
  channel?: string;
  token?: string;
  hideNav?: boolean;
}

const useAuth = (options: IProps) => {
  const { tenantNo, channel, token, hideNav } = options || {};
  const [init, setInit] = useState(false);
  const dispatch = useDispatch();

  const afterLogin = (userInfo: any) => {
    setInit(true);
    localStorage.setItem('userinfo', JSON.stringify(userInfo));
    dispatch({
      type: 'global/save',
      payload: {
        userInfo,
        currentTenantNo: tenantNo,
      },
    });
    !hideNav &&
      getRobotList()
        .then((res: any) => {
          if (!res?.data?.value?.find((item: any) => item.botNo === tenantNo)) {
            history.replace('/landing');
            return;
          }
          if (res.data.success) {
            dispatch({
              type: 'global/save',
              payload: {
                robotList: res.data.value,
              },
            });
          }
        })
        .catch(() => {
          history.replace('/landing');
        });
  };

  const fetchSsoSession = () => {
    const sessionId = localStorage.getItem('ATLANTIS_SESSION_ID');
    // sessionId存在的场合
    if (sessionId) {
      FetchUserInfoBySession(null, (result: any) => {
        afterLogin(result);
      });
    } else {
      FetchSessionByTicket((result: any) => {
        afterLogin(result);
      });
    }
  };

  // 嵌入的三方页面时的鉴权判断
  const fetchCheckExternalAuth = () => {
    if (
      !(tenantNo && channel && token) ||
      tenantNo === 'undefined' ||
      channel === 'undefined' ||
      token === 'undefined'
    ) {
      return;
    }
    checkExternalAuth({ tenantNo, channel, token }).then((res) => {
      if (res.data.success) {
        if (res.data.value) {
          afterLogin({});
        } else {
          message.error(res.data.errorMsg || '登录失效');
        }
      }
    });
  };

  useEffect(() => {
    if (channel) {
      fetchCheckExternalAuth();
    } else {
      fetchSsoSession();
    }
  }, [tenantNo, channel, token]);

  return { init };
};

export default useAuth;
