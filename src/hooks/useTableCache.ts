interface IProps {
  cacheKey: string;
}
const useTableCache = (props: IProps) => {
  const { cacheKey } = props;
  const getCache = () => {
    const cache = JSON.parse(window.sessionStorage.getItem(cacheKey) || '{}');
    return cache;
  };

  const setCache = (cache: any) => {
    window.sessionStorage.setItem(cacheKey, JSON.stringify(cache));
  };

  return {
    getCache,
    setCache,
  };
};

export default useTableCache;
