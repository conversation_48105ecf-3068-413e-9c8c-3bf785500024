import { useSelector } from '@magi/magi';
import { useMemo } from 'react';

const useRobotData = () => {
  const { currentTenantNo, robotList } = useSelector((state: { global: any }) => state.global);

  const currentData = useMemo(() => {
    return robotList?.find((item: any) => item.botNo === currentTenantNo) || {};
  }, [currentTenantNo, robotList]);

  const applicationList = useMemo(() => {
    return robotList?.filter((item: any) => item.isRedLine !== '1') || [];
  }, [robotList]);

  return {
    robotData: currentData,
    applicationList,
    isRedLineRobot: currentData.isRedLine === '1',
  };
};

export default useRobotData;
