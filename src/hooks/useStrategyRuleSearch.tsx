import { useWatch } from '@/components/Form';
import { getStraConfigList as handleStraConfigList } from '@/services/ce';
import { getAllTreeList, getFirstLevelKeys, selectSearch } from '@/utils';
import { SESSION_TYPE_OPTIONS } from '@/utils/constants';
import { useSelector } from '@magi/magi';
import { Form, FormInstance, Select, TreeSelect } from 'antd';
import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

const { SHOW_CHILD } = TreeSelect;

export const timeOptions = [
  {
    label: '会话开始时间',
    value: '1',
  },
  {
    label: '质检完成时间',
    value: '2',
  },
];

export const initialValues = {
  time: [dayjs().add(-7, 'd'), dayjs()],
  timeType: timeOptions[0].value,
};

const getGroupNames = (groupId: string, list: any[], data: any[]) => {
  list?.forEach((item) => {
    if (groupId === item.value) {
      data.push({ label: item.title, value: item.value });
    } else if (item.children?.length) {
      getGroupNames(groupId, item.children, data);
    }
  });
};

export const getRuleList = (strategyIds: any[], strategyListData: any[]) => {
  if (!strategyIds?.length) return [];
  let list: any = [];
  const strategys = strategyListData?.filter((item: any) => strategyIds.includes(item.value));
  strategys?.forEach((strategy: { strategyRuleList: any; isRedLine?: string }) => {
    if (strategy.strategyRuleList?.length) {
      list.push(...strategy.strategyRuleList.map((v: any) => ({ ...v, isRedLine: strategy.isRedLine })));
    }
  });
  return list.map((item: any) => {
    return {
      ...item,
      labelData: item.label,
      label: item.ruleName,
      value: item.id,
    };
  });
};

interface IProps {
  groupList?: any[];
  form?: FormInstance;
  tenantNo: string;
  showColumns?: ('time' | 'rule' | 'session')[];
  ruleProps?: any;
  groupProps?: any;
  isTimeType?: boolean;
  isFilterSmallModelConfig?: boolean;
}

const useStrategyRuleSearch = (options: IProps) => {
  const {
    groupList: withoutGroupList,
    form,
    tenantNo,
    showColumns,
    ruleProps = {},
    groupProps = {},
    isTimeType,
    isFilterSmallModelConfig,
  } = options || {};
  const { groupList: _groupList } = useSelector((state: { global: any }) => state.global);
  const formGroupIdList = useWatch('groupIdList', form);
  const formStrategyIdList = useWatch('strategyIdList', form);
  const formRuleIdList = useWatch('ruleIdList', form);
  const [straConfigList, setStraConfigList] = useState<any>([]);
  const [startDate, setStartDate] = useState<Dayjs | null>(initialValues.time[0]);

  const groupList = useMemo(
    () => (typeof withoutGroupList !== 'undefined' ? withoutGroupList : _groupList),
    [_groupList, withoutGroupList]
  );

  const groupIdList = useMemo(
    () =>
      typeof formGroupIdList === 'string' || typeof formGroupIdList === 'number' ? [formGroupIdList] : formGroupIdList,
    [formGroupIdList]
  );

  const strategyIdList = useMemo(
    () =>
      typeof formStrategyIdList === 'string' || typeof formStrategyIdList === 'number'
        ? [formStrategyIdList]
        : formStrategyIdList,
    [formStrategyIdList]
  );

  const ruleIdList = useMemo(
    () =>
      typeof formRuleIdList === 'string' || typeof formRuleIdList === 'number' ? [formRuleIdList] : formRuleIdList,
    [formRuleIdList]
  );

  const getStraConfigList = useCallback(
    (groupIds) => {
      if (!groupIds?.length) return [];
      return (
        straConfigList?.filter(
          ({ groupId, smallModelConfig }: { groupId: number; smallModelConfig?: string }) =>
            (groupProps?.type === 'cascader' ? [groupIds[groupIds.length - 1]] : groupIds).includes(groupId) &&
            // 判断是否需要过滤出小模型
            (isFilterSmallModelConfig ? !!smallModelConfig : true)
        ) || []
      );
    },
    [straConfigList, groupProps?.type, isFilterSmallModelConfig]
  );

  const strategyList = useMemo(() => getStraConfigList(groupIdList), [getStraConfigList, groupIdList]);

  const ruleList = useMemo(() => getRuleList(strategyIdList, strategyList), [strategyIdList, strategyList]);

  const checkGroups = useMemo(() => {
    let res: any[] = [];
    groupIdList?.forEach((v: string) => {
      getGroupNames(v, groupList, res);
    });

    return res;
  }, [groupIdList, groupList]);

  const checkStrategys = useMemo(() => {
    return (strategyIdList || [])
      .map((v: string) => strategyList.find((item: any) => item.value === v))
      .filter((item: any) => item?.label);
  }, [strategyIdList, strategyList]);

  const checkRules = useMemo(() => {
    return (ruleIdList || [])
      .map((v: string) => ruleList.find((item: any) => item.value === v))
      .filter((item: any) => item?.label);
  }, [ruleIdList, ruleList]);

  const handleGetStrategyByGroupId = useCallback(async () => {
    if (!tenantNo) return;
    handleStraConfigList({
      tenantNo,
    }).then((res) => {
      if (res.data.success) {
        const data = res.data.value.map((item: any) => ({ label: item.strategyName, value: item.id, ...item })) || [];
        setStraConfigList(data);
      }
    });
  }, [tenantNo]);

  useEffect(() => {
    handleGetStrategyByGroupId();
  }, [handleGetStrategyByGroupId]);

  const columns = useMemo(() => {
    let data: any[] = [];
    if (showColumns?.includes('time')) {
      data.push({
        label: isTimeType ? (
          <Form.Item name="timeType">
            <Select options={timeOptions} />
          </Form.Item>
        ) : (
          '会话开始时间'
        ),
        name: 'time',
        type: 'rangePicker',
        allowClear: false,
        disabledDate: (current: Dayjs) => {
          if (!startDate) return false;
          const maxDate = dayjs(startDate).add(2, 'years');
          const minDate = dayjs(startDate).subtract(2, 'years');
          return current && (current > maxDate || current < minDate);
        },
        onCalendarChange: (dates: Dayjs[]) => setStartDate(dates?.[0]),
        onChange: (dates: Dayjs[]) => setStartDate((dates as Dayjs[])?.[0]),
      });
    }
    data = [
      ...data,
      {
        label: '策略分组',
        name: 'groupIdList',
        type: 'treeSelect',
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        options: groupList,
        treeData: getAllTreeList(groupList) || [],
        treeDefaultExpandedKeys: getFirstLevelKeys(groupList),
        maxTagCount: 'responsive',
        treeTitleRender: (node: any) => (
          <span className={classNames({ red: node?.isRedLine === '1' })}>{node?.title}</span>
        ),
        filterTreeNode: (input: any, option: any) => {
          return (option?.title ?? '').toLowerCase().includes(input.toLowerCase());
        },
        onChange: () => {
          form?.setFieldValue('strategyIdList', undefined);
          form?.setFieldValue('ruleIdList', undefined);
        },
        ...(groupProps || {}),
      },
      {
        label: '策略名称',
        name: 'strategyIdList',
        type: 'treeSelect',
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        maxTagCount: 'responsive',
        options: strategyList,
        treeData: getAllTreeList(strategyList, { titleKey: 'label' }) || [],
        treeDefaultExpandedKeys: getFirstLevelKeys(strategyList, { titleKey: 'label' }),
        fieldNames: { value: 'value', label: 'label', children: 'children' },
        treeTitleRender: (node: any) => (
          <span className={classNames({ red: node?.isRedLine === '1' })}>{node?.label}</span>
        ),
        disabled: !groupIdList?.length,
        onChange: () => form?.setFieldValue('ruleIdList', undefined),
        filterTreeNode: selectSearch.filterOption,
      },
    ];
    if (showColumns?.includes('rule')) {
      data.push({
        label: '规则名称',
        name: 'ruleIdList',
        type: 'treeSelect',
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        maxTagCount: 'responsive',
        disabled: !strategyIdList?.length,
        options: ruleList,
        treeData: getAllTreeList(ruleList, { titleKey: 'label' }) || [],
        treeDefaultExpandedKeys: getFirstLevelKeys(ruleList, { titleKey: 'label' }),
        fieldNames: { value: 'value', label: 'label', children: 'children' },
        treeTitleRender: (node: any) => (
          <span className={classNames({ red: node?.isRedLine === '1' })}>{node?.label}</span>
        ),
        filterTreeNode: selectSearch.filterOption,
        ...(ruleProps || {}),
      });
    }
    if (showColumns?.includes('session')) {
      data.push({
        label: '会话类型',
        name: 'sessionTypes',
        type: 'select',
        mode: 'multiple',
        options: SESSION_TYPE_OPTIONS,
        ...selectSearch,
      });
    }
    return data;
  }, [
    JSON.stringify(showColumns || []),
    JSON.stringify(groupProps),
    JSON.stringify(ruleProps),
    form,
    startDate,
    groupList,
    strategyList,
    groupIdList,
    strategyIdList,
    ruleList,
    isTimeType,
  ]);

  return {
    columns,
    strategyList,
    ruleList,
    checkGroups,
    checkStrategys,
    checkRules,
    straConfigList,
    getStraConfigList,
  };
};

export default useStrategyRuleSearch;
