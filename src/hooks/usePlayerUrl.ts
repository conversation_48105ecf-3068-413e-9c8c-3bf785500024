import { isPublicEnv } from '@/config.env';
import { isSilkAudio } from '@/utils/ffmpeg';
import { useCallback, useEffect, useMemo, useState } from 'react';
import useAmrPlayerUrl from './useAmrPlayerUrl';
import useSilkPlayerUrl from './useSilkPlayerUrl';

const ossPrefix = '/oss-proxy';
const tripartiteOssPrefix = '/tripartite-oss-proxy';

const getMyOss = (playerUrl?: string) =>
  playerUrl?.indexOf('za-ark-ce') !== -1 || playerUrl?.indexOf('oss-cn-hongkong') !== -1;

export const changeOssUrlProxy = (playerUrl?: string) => {
  if (!playerUrl) return playerUrl;
  // 对第三方oss域名进行代理
  if (!getMyOss(playerUrl)) {
    return `${location.host.includes('localhost') ? 'http://localhost:8080' : ''}${tripartiteOssPrefix}?url=${encodeURIComponent(playerUrl)}`;
  }
  // oss非第三方域名进行代理
  const urlObj = new URL(playerUrl);
  const path = `${urlObj.pathname}${urlObj.search}`;
  return ossPrefix + path;
};

interface IProps {
  src?: string;
}

const usePlayerUrl = (props: IProps) => {
  const { src: playerUrl } = props;
  const [isSilk, setIsSilk] = useState<boolean>();

  const isMyOss = useMemo(() => getMyOss(playerUrl), [playerUrl]);

  const src = useMemo(() => {
    return changeOssUrlProxy(playerUrl);
  }, [playerUrl]);

  const getIsSilkAudio = useCallback(async () => {
    if (!src) return;
    try {
      const res = await isSilkAudio(src);
      console.log(`player url isSilk: ${res}`);
      setIsSilk(res);
    } catch {
      setIsSilk(false);
    }
  }, [src]);

  useEffect(() => {
    getIsSilkAudio();
  }, [getIsSilkAudio]);

  const amrData = useAmrPlayerUrl({ src, isRun: typeof isSilk === 'boolean' && !isSilk });
  const silkData = useSilkPlayerUrl({ src, isRun: typeof isSilk === 'boolean' && isSilk });

  const playerData = useMemo(() => {
    const data: any = isSilk ? silkData : amrData;
    return {
      playerUrl: data.playerUrl || (isPublicEnv() && isMyOss ? src : playerUrl),
      setPlayerMode: data.setPlayerMode || (() => {}),
      isPlayerFinish: data.isPlayerFinish,
    };
  }, [isSilk, amrData, silkData, playerUrl, src, isMyOss]);

  return playerData;
};

export default usePlayerUrl;
