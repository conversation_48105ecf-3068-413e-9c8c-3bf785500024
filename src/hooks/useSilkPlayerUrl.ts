import { handlesilk } from '@/utils/ffmpeg';
import { useSelector } from '@magi/magi';
import { useCallback, useEffect, useState } from 'react';

interface IProps {
  src?: string;
  isRun?: boolean;
}

const useSilkPlayerUrl = (props: IProps) => {
  const { src: playerUrl, isRun = true } = props;
  const { isInitFFmpeg } = useSelector((state: { global: any }) => state.global);
  const [formatSrc, setFormatSrc] = useState('');
  const [isFinish, setIsFinish] = useState(false);

  const loadVendor = useCallback(async () => {
    if (!playerUrl || !isInitFFmpeg) return;
    try {
      const res = await handlesilk(playerUrl);
      res && setFormatSrc(res);
    } catch (err) {
      console.error('sk load:', err);
    } finally {
      setIsFinish(true);
    }
  }, [playerUrl, isInitFFmpeg]);

  useEffect(() => {
    if (!isRun) return;
    loadVendor();
  }, [loadVendor, isRun]);

  return { playerUrl: formatSrc, isPlayerFinish: isFinish };
};

export default useSilkPlayerUrl;
