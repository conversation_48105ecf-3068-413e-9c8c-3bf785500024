import useStrategyRuleSearch, { getRuleList } from '@/hooks/useStrategyRuleSearch';
import { useSelector } from '@magi/magi';
import { FormInstance } from 'antd';
import { cloneDeep } from 'lodash';
import { useMemo, useState } from 'react';

const getLeafIds = (root: any[]) => {
  const leafIds: string[] = [];
  const dfs = (node: any) => {
    if (!node?.children?.length) {
      (node.value || typeof node.value === 'number') && leafIds.push(node.value);
    } else {
      node.children?.map(dfs);
    }
  };
  root?.map(dfs);
  return leafIds;
};

interface IProps {
  form: FormInstance;
  currentTenantNo: string;
  showColumns: string[];
}

const useGetRuleNames = (options: IProps) => {
  const { form, currentTenantNo, showColumns } = options || {};
  const { groupList } = useSelector((state: { global: any }) => state.global);
  const {
    columns: strategyRuleSearchList,
    getStraConfigList,
    ruleList,
  } = useStrategyRuleSearch({
    form,
    tenantNo: currentTenantNo,
    showColumns: showColumns as any,
    isTimeType: true,
  });
  const [searchData, setSearchData] = useState<{ [key: string]: any }>();

  // 策略分组、策略名称、规则名称如果为空，则选择全部
  const searchDataObj = useMemo(() => {
    let newSearchData = cloneDeep(searchData || {});
    const isGroupIdList = !!newSearchData?.groupIdList?.length;
    if (!isGroupIdList) {
      newSearchData.groupIdList = getLeafIds(groupList || []);
    }
    let strategyList = getStraConfigList(newSearchData.groupIdList);
    if (!newSearchData.strategyIdList?.length) {
      newSearchData.strategyIdList = strategyList?.map(({ value }: { value: number }) => value);
    }
    if (!newSearchData.ruleIdList?.length) {
      newSearchData.ruleIdList = getRuleList(newSearchData.strategyIdList, strategyList)?.map(
        ({ value }: { value: number }) => value
      );
    }
    return newSearchData;
  }, [searchData, groupList, getStraConfigList]);

  const ruleNames = useMemo(() => {
    let strategyList = getStraConfigList(searchDataObj.groupIdList);
    return (
      searchData?.ruleIdList?.length
        ? ruleList?.filter(({ value }: { value: number }) => searchData.ruleIdList?.includes(value))
        : getRuleList(searchDataObj.strategyIdList, strategyList)
    )
      ?.map(({ label }: { label: number }) => label)
      ?.join('、');
  }, [searchDataObj, searchData, ruleList]);

  return {
    strategyRuleSearchList,
    ruleNames,
    searchDataObj,
    setSearchData,
  };
};

export default useGetRuleNames;
