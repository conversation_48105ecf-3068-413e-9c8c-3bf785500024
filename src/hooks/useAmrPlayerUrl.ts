import { useCallback, useEffect, useMemo, useState } from 'react';

interface IProps {
  src?: string;
  isRun?: boolean;
}

const useAmrPlayerUrl = (props: IProps) => {
  const { src, isRun = true } = props;
  const [formatSrc, setFormatSrc] = useState('');
  const [mode, setMode] = useState('');

  const onRun = useCallback(
    (amr) => {
      if (!src || !amr) return;
      const run = async () => {
        if (src.includes('.amr') || ['nb', 'wb'].includes(mode)) {
          try {
            const blob = await fetch(src).then((res) => res.blob());
            function readBlob(blob: Blob) {
              return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function (e) {
                  //@ts-ignore
                  const data = new Uint8Array(e.target.result);
                  resolve(data);
                };
                reader.readAsArrayBuffer(blob);
              });
            }
            if (!blob) return;
            const data = await readBlob(blob);
            const buffer = amr.toWAV(data);
            const url = URL.createObjectURL(new Blob([buffer], { type: 'audio/x-wav' }));
            url && setFormatSrc(url);
          } catch (err) {
            console.log(`amr change wav error: ${err}`);
          }
        }
      };
      run();
    },
    [src, mode]
  );

  const runAMRNB = useCallback(() => {
    setMode('nb');
    //@ts-ignore
    onRun(window.AMR);
  }, [onRun]);

  const runAMRWB = useCallback(() => {
    setMode('wb');
    //@ts-ignore
    onRun(window.AMRWB);
  }, [onRun]);

  useEffect(() => {
    if (!src || !isRun) return;
    if (mode === 'nb') {
      runAMRNB();
    } else if (mode === 'wb') {
      runAMRWB();
    }
  }, [src, isRun, mode, runAMRNB, runAMRWB]);

  const setPlayerMode = useCallback(() => {
    setMode((preState) => {
      if (preState === '') return 'nb';
      if (preState === 'nb') return 'wb';
      return preState;
    });
  }, []);

  const isFinish = useMemo(() => mode === 'wb', [mode]);

  return { playerUrl: formatSrc, setPlayerMode, isPlayerFinish: isFinish };
};

export default useAmrPlayerUrl;
