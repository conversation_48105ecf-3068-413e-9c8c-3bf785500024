<svg width="90" height="820" viewBox="0 0 90 820" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1996_8081)">
<rect width="90" height="820" transform="matrix(1 0 0 -1 0 820)" fill="#070707"/>
<g filter="url(#filter0_f_1996_8081)">
<rect width="541" height="409" rx="204.5" transform="matrix(1 0 0 -1 -26 180)" fill="#4361FC"/>
</g>
<g filter="url(#filter1_f_1996_8081)">
<rect width="555" height="555" rx="277.5" transform="matrix(1 0 0 -1 -361 1383)" fill="#EE46BC" fill-opacity="0.8"/>
</g>
</g>
<defs>
<filter id="filter0_f_1996_8081" x="-526" y="-729" width="1541" height="1409" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1996_8081"/>
</filter>
<filter id="filter1_f_1996_8081" x="-861" y="328" width="1555" height="1555" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1996_8081"/>
</filter>
<clipPath id="clip0_1996_8081">
<rect width="90" height="820" fill="white" transform="matrix(1 0 0 -1 0 820)"/>
</clipPath>
</defs>
</svg>
