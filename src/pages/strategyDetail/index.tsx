import {
  addStrategyTemplate,
  commitDraft,
  getFieldList,
  getOperatorList,
  getStrategyConfigDetail,
  getStrategyDraftDetail,
  getStrategyTemplateDetail,
  saveDraft,
  updateStrategyTemplate,
} from '@/services/ce';
import { getRedLineListConfig } from '@/services/redLine';
import { EFieldsType, ERuleType, ETime, isShowMsgNum, menuWidth, QUALITY_TYPE } from '@/utils/constants';
import { history, useLocation, useSelector } from '@magi/magi';
import { Button, Form, message, Modal, Spin, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty } from 'lodash';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ApplicationScope from './ApplicationScope';
import BaseInfo from './BaseInfo';
import FieldRuleConfig from './FieldRuleConfig';
import styles from './index.scss';
import MiniatureInfo from './MiniatureInfo';
import StrategyRuleConfig from './StrategyRuleConfig';

const getInitData = (data?: any[]) => {
  const list: any[] = [];
  data?.forEach(({ sort, ...v }) => {
    if (v.ruleType === ERuleType.CE) {
      const index = list.findIndex((item) => (sort || typeof sort === 'number') && item.sort === sort);
      const val = {
        id: v.id,
        ruleName: v.ruleName,
        riskLevel: v.riskLevel || undefined,
        score: v.score,
        label: v.label
          ?.split(',')
          .filter(Boolean)
          .map((v: string) => Number(v)),
      };
      if (index !== -1) {
        list[index].ruleData.push(val);
      } else {
        list.push({ sort, ...v, ruleData: [val] });
      }
    } else {
      list.push(v);
    }
  });
  return list;
};

export const getOperatorMap = (fieldsList?: any[], operatorList?: any) => {
  const data: any = {};
  const opObj = operatorList;
  fieldsList?.forEach((item: any) => {
    let keys: any = [];
    if (item.fieldType === EFieldsType.Date || item.fieldType === EFieldsType.Number) {
      keys = ['gt', 'gte', 'lt', 'lte'];
    }
    if (item.fieldType === EFieldsType.String) {
      keys = item.tokenizer === 1 ? ['contains', 'not contains'] : ['equals', 'not equals'];
    }
    const list: any = [];
    Object.keys(opObj).forEach((key) => {
      if (keys.includes(key)) {
        list.push({
          label: opObj[key as any],
          value: key,
        });
      }
    });
    data[item.targetFieldCode] = list;
  });
  return data;
};

const StrategyDetail: FC<any> = ({
  currentDetail,
  isRedLine,
  refresh,
  isTemplate = false,
  onlyRead = false,
  closeDrawer,
}) => {
  const location: any = useLocation();
  const {
    currentTenantNo: _currentTenantNo,
    labelList: noRedLabelList,
    redLabelList,
    riskList: noRedRiskList,
    redRiskList,
    groupMap,
    whitelistPermission,
  } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = _currentTenantNo || location?.query?.tenantNo || currentDetail?.tenantNo;
  const groupId: any = location?.query?.groupId || currentDetail?.groupId;
  const [form] = Form.useForm();
  const [applicationForm] = Form.useForm();
  const miniatureInfoRef = useRef<any>();
  const strategyFieldRuleRef = useRef<any>();
  const fieldRuleRef = useRef<any>();
  const oldTenantNosRef = useRef<any>();
  const [id, setId] = useState<any>('');
  const [strategyId, setStrategyId] = useState<any>('');
  const [isReset, setIsReset] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [oldData, setOldData] = useState<any>({});
  const [operatorList, setOperatorList] = useState<any[]>([]);
  const [fieldsList, setFieldsList] = useState<IFieldConfig[]>([]);
  const [testStatus, setTestStatus] = useState(false);
  const [qualityType, setQualityType] = useState<string>();
  const tenantNos = Form.useWatch(['tenantNos'], applicationForm);
  const strategyName = Form.useWatch(['strategyName'], form);

  // 小模型权限控制
  const isSmallModel = useMemo(() => {
    const { smallModel } = whitelistPermission || {};
    return smallModel?.includes(currentTenantNo);
  }, [currentTenantNo, whitelistPermission]);

  // 非红线的访问红线的规则，标签数据判断
  const labelList = useMemo(
    () => (isRedLine === '1' ? redLabelList : noRedLabelList),
    [isRedLine, redLabelList, noRedLabelList]
  );

  const riskList = useMemo(
    () => (isRedLine === '1' ? redRiskList : noRedRiskList),
    [isRedLine, redRiskList, noRedRiskList]
  );

  useEffect(() => {
    if (isEmpty(currentDetail)) return;
    setId(currentDetail?.id || null);
  }, [currentDetail]);

  const initFormData = (data: any) => {
    data.strategyRuleList = getInitData(data.strategyRuleList);
    setOldData(data);
    applicationForm.setFieldsValue({ tenantNos: data.addTenantNos || undefined });
    setQualityType(data.qualityType);
    form.setFieldsValue({
      strategyName: data.strategyName,
      qualityType: data.qualityType,
      qualityMsgNumType: data.qualityMsgNumType,
      qualityMsgNum: data.qualityMsgNum,
      groupId: data.groupId,
      status: data.status,
      executeType: data.timeRelationDTO?.executeType,
      executeTime: data.timeRelationDTO?.executeTime
        ? dayjs(`2023-01-01 ${data.timeRelationDTO?.executeTime}`)
        : undefined,
    });
    const strategyRuleList = cloneDeep(data.strategyRuleList || []);
    fieldRuleRef.current?.initFields?.(strategyRuleList);
    const strategyGroupConfigData = cloneDeep(
      isRedLine === '1' ? data.tenantStrategyDefList || [] : data.strategyGroupConfigDTO || {}
    );
    strategyFieldRuleRef.current?.initFields?.(strategyGroupConfigData);
    miniatureInfoRef.current?.form?.setFieldsValue?.({
      ...(data.smConfigDTO || {}),
      useSmallModel: data.smConfigDTO?.useSmallModel?.toString() === '1',
    });
  };

  const initData = useCallback(
    async (id, isReset = false, isTemplate = false) => {
      if (id) {
        let res: any;
        if (isTemplate) {
          res = await getStrategyTemplateDetail(id);
        } else {
          if (isReset) {
            res = await getStrategyConfigDetail(id);
          } else {
            res = await getStrategyDraftDetail(id, isRedLine);
          }
        }

        if (res.data.success) {
          setTestStatus(true);
          let data: any;
          if (!isReset) {
            setStrategyId(res.data.value.configData.id);
            data = cloneDeep(res.data.value.configData);
            delete data.id;
          } else {
            if (!isEmpty(res.data.value)) {
              data = res.data.value;
              setIsReset(true);
            } else {
              message.warning('未找到已发布数据');
              return;
            }
          }
          initFormData(data);
        }
      }
    },
    [isRedLine]
  );

  const handleFieldList = async () => {
    let res;
    if (isRedLine === '1') {
      if (tenantNos?.length) {
        res = await getRedLineListConfig({ tenantNo: currentTenantNo, isDeleted: 'N', tenantNos });
      }
    } else {
      res = await getFieldList(currentTenantNo, 'N');
    }
    setFieldsList(
      res
        ? res.data.value.map((item: IFieldConfig) => {
            return {
              ...item,
              label: item.showName,
              value: item.targetFieldCode,
            };
          })
        : []
    );
  };

  useEffect(() => {
    if (id === '') return;
    handleFieldList();
  }, [tenantNos, isRedLine, currentTenantNo, id]);

  const handleOperatorList = async () => {
    const res = await getOperatorList();
    setOperatorList(res.data.value);
  };

  useEffect(() => {
    const run = async () => {
      if (id === '') return;
      setLoading(true);
      await handleOperatorList();
      if (currentDetail.templateData) {
        initFormData(currentDetail.templateData);
      } else {
        await initData(id, false, isTemplate);
      }
      setLoading(false);
    };
    run();
  }, [currentTenantNo, id, isTemplate, currentDetail]);

  const operatorMap = useMemo<any>(() => getOperatorMap(fieldsList, operatorList), [fieldsList, operatorList]);

  const groupName = useMemo(() => {
    try {
      if (groupId && groupMap) {
        return groupMap[groupId].title;
      }
      if (!isEmpty(oldData) && groupMap) {
        return groupMap[oldData.groupId].title;
      }
    } catch (error) {
      return '';
    }
  }, [id, groupId, oldData, groupMap]);

  const getAddDic = (configFields: any, tenantNo?: string, cb?: (res: any) => void) => {
    // 红线的时候会是数组的状态
    if (Array.isArray(configFields)) {
      const list: any[] = [];
      const oldTenantStrategyDefList: any[] = [];
      configFields.filter(Boolean).forEach((item) => {
        list.push(
          ...getAddDic(item, item.tenantNo, (val) => {
            const { strategyGroupConfigDTO, tenantStrategyDefList } = val || {};
            oldTenantStrategyDefList.push({
              ...(tenantStrategyDefList?.find((v: any) => v.tenantNo === item.tenantNo) || {}),
              strategyGroupConfigDTO,
            });
          })
        );
      });
      // 调用setOldData进行更新
      oldTenantStrategyDefList.length &&
        setOldData({
          ...oldData,
          tenantStrategyDefList: oldTenantStrategyDefList,
        });
      return [...new Set(list)];
    }
    const { groups } = configFields || {};
    const { strategyGroupConfigDTO: _strategyGroupConfigDTO, tenantStrategyDefList } = oldData || {};
    const strategyGroupConfigDTO =
      tenantStrategyDefList?.find((v: any) => v.tenantNo === tenantNo)?.strategyGroupConfigDTO ||
      _strategyGroupConfigDTO ||
      {};
    const { fieldGroupList } = strategyGroupConfigDTO;
    const extractWords = (value: string) => {
      return (
        value
          ?.replace(/[()（）]/g, '')
          ?.split('-')
          ?.slice(1)
          ?.map((part) => {
            const parts = part?.split(' ')?.[0];
            return parts?.trim();
          })
          ?.filter(Boolean) || []
      );
    };
    // 构建 fieldGroupList 的 Map，键为 fieldCode，值为具有相同 fieldCode 的后缀单词数组
    const fieldGroupListMap = new Map();
    (fieldGroupList || []).forEach((val: { fieldCode: string; fieldValue: string }[]) => {
      val.forEach((item) => {
        if (item.fieldCode === 'textContent') {
          if (!fieldGroupListMap.has(item.fieldCode)) {
            fieldGroupListMap.set(item.fieldCode, new Set());
          }
          const words = extractWords(item.fieldValue);
          words.forEach((word) => fieldGroupListMap.get(item.fieldCode).add(word));
        }
      });
    });
    const resultSet = new Set<string>();
    // 遍历 groups，查找和添加差异项
    (groups || []).forEach((item: { fieldCode: string; fieldValue: string }[]) => {
      item?.forEach(({ fieldCode, fieldValue }) => {
        if (fieldCode === 'textContent') {
          const newWords = extractWords(fieldValue);
          const correspondingWords = fieldGroupListMap.get(fieldCode) || new Set();
          newWords.forEach((newWord) => {
            if (!correspondingWords.has(newWord)) {
              resultSet.add(newWord);
            }
          });
        }
      });
    });
    // 如果resultSet不为空，则更新oldData
    if (resultSet.size > 0) {
      const updatedOldData = {
        ...oldData,
        strategyGroupConfigDTO: {
          ...strategyGroupConfigDTO,
          fieldGroupList: [...(groups || [])],
        },
      };
      if (cb) {
        cb(updatedOldData);
      } else {
        // 调用setOldData进行更新
        setOldData(updatedOldData);
      }
    }
    // 返回去重后的结果
    return Array.from(resultSet);
  };

  const back = () => {
    currentDetail ? closeDrawer() : history.goBack();
  };

  const save = async (isDraft: boolean, config: { isTemplate: boolean } = { isTemplate: false }) => {
    const [baseFields, miniatureData, configFields, ruleFields, applicationFields] = await Promise.all([
      form.validateFields(),
      miniatureInfoRef.current?.form?.validateFields(),
      strategyFieldRuleRef.current?.getFields(),
      fieldRuleRef.current?.getFields(),
      applicationForm.validateFields(),
    ]);
    let cango = true;
    let info = '';
    if (!isEmpty(ruleFields)) {
      ruleFields.forEach((node: any, i: number) => {
        if (node?.fieldRuleList && node?.fieldRuleList.length > 0) {
          node.fieldRuleList.forEach((item: any, index: number) => {
            const fieldType = fieldsList.find((field) => field.targetFieldCode === item.fieldCode)?.fieldType;
            if (isEmpty(item.fieldCode) || isEmpty(item.fieldValue)) {
              cango = false;
              info = `第${i + 1}个质检规则的第${index + 1}个规则填写不完整`;
              return;
            }
            if (item.fieldCode !== 'textContent' && isEmpty(item.fieldOperator)) {
              cango = false;
              info = `第${i + 1}个质检规则的第${index + 1}个规则填写不完整`;
              return;
            }
            if (fieldType === EFieldsType.Date && item.fieldValue) {
              item.fieldValue = item.fieldValue.valueOf();
            }
          });
        }
      });
    }
    if (!cango) {
      message.error(info);
      return;
    }
    const params: any = cloneDeep(oldData);
    params.strategyName = baseFields.strategyName;
    params.qualityType = baseFields.qualityType;
    // 实时质检
    if (params.qualityType === QUALITY_TYPE.REAL_TIME) {
      params.qualityMsgNumType = baseFields.qualityMsgNumType;
      params.qualityMsgNum = isShowMsgNum.includes(params.qualityMsgNumType) ? baseFields.qualityMsgNum : undefined;
    } else {
      params.qualityMsgNumType = undefined;
      params.qualityMsgNum = undefined;
    }
    params.groupId = Number(oldData?.groupId || groupId);
    params.status = testStatus ? baseFields.status : 0;
    params.timeRelationDTO = {
      ...(oldData?.timeRelationDTO || {}),
      executeType: baseFields.executeType,
      executeTime:
        baseFields.executeType === ETime.REGULAR_TIME ? dayjs(baseFields.executeTime).format('HH:mm:ss') : '',
    };
    params.fieldRuleList = configFields;
    delete params.fieldRuleList;
    params.strategyRuleList = ruleFields;
    params.tenantNo = currentTenantNo;
    // 小模型
    if (isSmallModel || params.smConfigDTO) {
      delete params.smallModelConfig;
      params.smConfigDTO = {
        useSmallModel: miniatureData?.useSmallModel ? 1 : 0,
        modelId: miniatureData?.useSmallModel ? miniatureData?.modelId : undefined,
        useType: miniatureData?.useSmallModel ? miniatureData?.useType : undefined,
      };
    }
    // 红线
    if (isRedLine === '1') {
      params.addTenantNos = applicationFields.tenantNos;
      params.isRedLine = isRedLine;
      params.tenantStrategyDefList = configFields?.filter(Boolean)?.map((configField: any) => {
        return {
          id: oldData?.tenantStrategyDefList?.find((v: any) => v?.tenantNo === configField.tenantNo)?.id,
          tenantNo: configField.tenantNo,
          strategyGroupConfigDTO: {
            operateType: configField.operator,
            fieldGroupList: configField.groups,
          },
        };
      });
      delete params.strategyGroupConfigDTO;
    } else {
      params.strategyGroupConfigDTO = {
        operateType: configFields.operator,
        fieldGroupList: configFields.groups,
      };
    }
    /**保存模版 */
    if (config.isTemplate) {
      const api = !!id ? updateStrategyTemplate : addStrategyTemplate;
      setConfirmLoading(true);
      const newParams: any = {
        configData: params,
      };
      if (!!id) {
        newParams.id = id;
      }
      if (!!strategyId) {
        newParams.configData.id = strategyId;
      }
      api(newParams)
        .then((res) => {
          if (res.data.success) {
            message.success('保存成功');
            back();
            refresh();
          }
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    } else {
      const newParams: any = {
        id,
        configData: params,
      };
      if (!!strategyId) {
        newParams.configData.id = strategyId;
      }
      const addDic = getAddDic(configFields);
      if (addDic?.length) {
        newParams.addDic = addDic;
      }
      setConfirmLoading(true);
      const api = isDraft ? saveDraft : commitDraft;
      api(newParams)
        .then((res) => {
          if (res.data.success) {
            if (testStatus) {
              message.success('保存成功');
            } else {
              message.success('保存成功，策略未进行试运行或者试运行不通过，状态已被修改为禁用');
            }
            back();
            refresh();
          }
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    }
  };

  const reset = async () => {
    try {
      setLoading(true);
      await initData(strategyId, true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isRedLine !== '1') return;
    const configForm = strategyFieldRuleRef.current?.form;
    const deleteNos = oldTenantNosRef.current?.filter((v: string) => !tenantNos.includes(v));
    if (Array.isArray(oldTenantNosRef.current) && deleteNos?.length) {
      Modal.confirm({
        title: '提示',
        content: '更新后对应的策略定义和关键词规则都将会被清空',
        onOk() {
          const obj: any = {};
          deleteNos.forEach((k: string) => {
            obj[k] = { fields: [] };
          });
          configForm?.setFieldsValue(obj);
          fieldRuleRef.current?.clearCustomRuleFormValues();
          oldTenantNosRef.current = tenantNos || [];
        },
        onCancel() {
          applicationForm.setFieldsValue({ tenantNos: oldTenantNosRef.current });
        },
      });
    } else {
      oldTenantNosRef.current = tenantNos || [];
    }
  }, [tenantNos, isRedLine]);

  const onBaseInfoValuesChange = (changedValues: any) => {
    if (!Object.keys(changedValues || {}).includes('qualityType')) return;
    if (changedValues.qualityType === QUALITY_TYPE.REAL_TIME) {
      const fields =
        strategyFieldRuleRef.current?.form?.getFieldsValue()?.fields ||
        Object.values(strategyFieldRuleRef.current?.form?.getFieldsValue() || {})
          .map((item: any) => item?.fields || [])
          ?.flat();
      const fieldsData = Array.isArray(fields) ? fields : Object.values(fields || {});
      if (
        fieldsData.some((item) =>
          Object.entries(item || {}).some(([k, v]) =>
            k === 'fieldCode' && v ? !['sessionType', 'textContent'].includes(v as string) : !!v
          )
        )
      ) {
        Modal.confirm({
          title: '提示',
          content: '切换后策略定义将清空，是否继续?',
          onOk() {
            setQualityType(changedValues.qualityType);
            setTimeout(() => {
              strategyFieldRuleRef.current?.changeBasicInfoForm();
            }, 200);
          },
          onCancel() {
            form.setFieldsValue({ qualityType });
          },
        });
      } else {
        setQualityType(changedValues.qualityType);
        setTimeout(() => {
          strategyFieldRuleRef.current?.changeBasicInfoForm();
        }, 200);
      }
    } else {
      setQualityType(changedValues.qualityType);
    }
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.container} style={onlyRead ? { paddingBottom: 20 } : {}}>
        {isRedLine === '1' && <ApplicationScope form={applicationForm} disabled={onlyRead} />}
        <BaseInfo
          isTemplate={isTemplate}
          form={form}
          groupName={groupName}
          disabled={onlyRead}
          onValuesChange={onBaseInfoValuesChange}
          isRedLine={isRedLine}
        />
        <div className={styles.fieldConfig}>
          <StrategyRuleConfig
            fieldsList={fieldsList}
            operatorMap={operatorMap}
            ref={strategyFieldRuleRef}
            tenantNo={currentTenantNo}
            getAddDic={getAddDic}
            tenantNos={tenantNos}
            isRedLine={isRedLine}
            onlyRead={onlyRead}
            operatorList={operatorList}
            setConfirmLoading={setConfirmLoading}
            setTestStatus={setTestStatus}
            qualityType={qualityType}
          />
        </div>
        {!isTemplate && isSmallModel && <MiniatureInfo ref={miniatureInfoRef} disabled={onlyRead} />}
        <FieldRuleConfig
          labelList={labelList || []}
          riskList={riskList || []}
          fieldsList={fieldsList}
          operatorMap={operatorMap}
          ref={fieldRuleRef}
          tenantNo={currentTenantNo}
          id={!isReset ? id : strategyId}
          strategyName={strategyName}
          tenantNos={tenantNos}
          isRedLine={isRedLine}
          onlyRead={onlyRead}
        />
        {!onlyRead && (
          <div className={styles.actions} style={{ left: menuWidth }}>
            {!isTemplate && (
              <>
                <Tooltip title="重置后将清空当前已保存的草稿内容，回到该策略配置的已发布版本">
                  <Button onClick={() => reset()} style={{ marginRight: 8 }}>
                    重置
                  </Button>
                </Tooltip>
                <Button onClick={() => save(true)} style={{ marginRight: 8 }}>
                  保存草稿
                </Button>
              </>
            )}
            <Button style={{ marginRight: 8 }} onClick={() => back()}>
              取消
            </Button>
            {!isTemplate ? (
              <Tooltip title={!testStatus ? '策略未进行试运行或者试运行不通过，保存状态会被修改为禁用' : ''}>
                <Button loading={confirmLoading} onClick={() => save(false)} type="primary">
                  提交发布
                </Button>
              </Tooltip>
            ) : (
              <Button type="primary" loading={confirmLoading} onClick={() => save(false, { isTemplate: true })}>
                保存
              </Button>
            )}
          </div>
        )}
      </div>
    </Spin>
  );
};

export default StrategyDetail;
