import { getListSupportedModels } from '@/services/ce';
import { MINIATURE_USE_TYPE_OPTIONS, requiredRules } from '@/utils/constants';
import { Col, Form, Row, Select, Switch } from 'antd';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import styles from './index.scss';

interface IProps {
  disabled?: boolean;
}

const MiniatureInfo = forwardRef<any, IProps>(({ disabled }, ref) => {
  const [form] = Form.useForm();
  const [modelData, setModelData] = useState<any[]>([]);

  useImperativeHandle(ref, () => ({
    form,
  }));

  const handleListSupportedModels = useCallback(async () => {
    const res = await getListSupportedModels();
    if (res.data.success && res.data.value) {
      setModelData(res.data.value);
    }
  }, []);

  useEffect(() => {
    handleListSupportedModels();
  }, [handleListSupportedModels]);

  return (
    <div className={styles.miniatureInfo}>
      <Form labelAlign="left" form={form} disabled={disabled} className={styles.formContainer}>
        <Row gutter={24}>
          <Col span={6}>
            <Form.Item label="是否使用小模型" name="useSmallModel" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Form.Item shouldUpdate={(preValue, curValue) => preValue.useSmallModel !== curValue.useSmallModel} noStyle>
            {({ getFieldValue }) =>
              !!getFieldValue('useSmallModel') && (
                <>
                  <Col span={9}>
                    <Form.Item label="选择小模型" name="modelId" rules={requiredRules}>
                      <Select
                        placeholder="请选择小模型"
                        options={modelData}
                        fieldNames={{ label: 'modelNameZh', value: 'modelId' }}
                        filterOption={(input, option) =>
                          option ? option.modelNameZh?.toLowerCase()?.indexOf(input.toLowerCase()) >= 0 : false
                        }
                        showSearch
                      />
                    </Form.Item>
                  </Col>
                  <Col span={9}>
                    <Form.Item label="使用方法" name="useType" rules={requiredRules}>
                      <Select placeholder="请选择使用方法" options={MINIATURE_USE_TYPE_OPTIONS} />
                    </Form.Item>
                  </Col>
                </>
              )
            }
          </Form.Item>
        </Row>
      </Form>
    </div>
  );
});

export default MiniatureInfo;
