import Table from '@/components/Table';
import { getConfigTestPage, summitRunningData } from '@/services/ce';
import { antConfig } from '@/utils/constants';
import { Button, DatePicker, Form, message, Modal, Spin } from 'antd';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import React, { FC, useEffect, useRef, useState } from 'react';
import styles from './index.scss';

const { RangePicker } = DatePicker;

interface IProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  currentTenantNo: string;
  setTestStatus: React.Dispatch<React.SetStateAction<boolean>>;
  setConfirmLoading: React.Dispatch<React.SetStateAction<boolean>>;
  getFields: () => any;
  qualityType?: string;
}

const StrategicTrialModal: FC<IProps> = ({
  visible,
  setVisible,
  currentTenantNo,
  setTestStatus,
  setConfirmLoading,
  getFields,
  qualityType,
}) => {
  const [hitListModalVisible, setHitListModalVisible] = useState(false);
  const [hitListTotal, setHitListTotal] = useState(0);
  const [hitList, setHitList] = useState([]);
  const [hitPagination, setHitPagination] = useState({ current: 1, pageSize: 10 });
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [timeRange, setTimeRange] = useState<any>([]);
  const [perHitList, setPerHitList] = useState([]);
  const [hitDataStatus, setHitDataStatus] = useState(false);
  const [estimateData, setEstimateData] = useState(0);
  const [hitData, setHitData] = useState(0);
  const [searchParams, setSearchParams] = useState<any>({});
  const [runningLoading, setRunningLoading] = useState(false);
  const startTimeRef = useRef<any>({});
  const endTimeRef = useRef<any>({});
  const [form] = Form.useForm();

  useEffect(() => {
    if (!hitListModalVisible) {
      setHitPagination({ current: 1, pageSize: 10 });
    }
  }, [hitListModalVisible, searchParams]);

  useEffect(() => {
    if (!isEmpty(searchParams) && hitListModalVisible) {
      getConfigTestPage(
        Object.assign({ qualityType }, searchParams, {
          pageSize: hitPagination.pageSize,
          pageNum: hitPagination.current,
        })
      ).then((res) => {
        setHitList(res.data.value?.list || []);
        setHitListTotal(res.data.value?.total || 0);
      });
    }
  }, [searchParams, hitListModalVisible, hitPagination, qualityType]);

  const onCancel = () => {
    setVisible(false);
    setSearchParams({});
    setTimeRange([]);
    setEstimateData(0);
    setHitData(0);
    setCurrentTime(0);
    setHitDataStatus(false);
    setPerHitList([]);
  };

  const getEstimateData = async (tenantNo?: string) => {
    const params = {
      tenantNo: tenantNo || currentTenantNo,
      startTime: Date.parse(startTimeRef.current),
      endTime: Date.parse(endTimeRef.current),
      qualityType,
    };
    const res = await summitRunningData(params);
    return res.data.value;
  };

  const submitRunningData = async () => {
    const configFields = await getFields?.();
    const obj = {
      strategyGroupConfigDTO: {
        operateType: configFields.operator,
        fieldGroupList: configFields.groups,
      },
      tenantNo: configFields.tenantNo || currentTenantNo,
      startTime: Date.parse(startTimeRef.current),
      endTime: Date.parse(endTimeRef.current),
      qualityType,
    };
    const params = {
      ...obj,
      pageSize: 30,
      pageNum: 1,
    };
    setSearchParams(obj);
    try {
      const estimateData = await getEstimateData(configFields.tenantNo);
      setEstimateData(estimateData);
      setRunningLoading(true);
      const api = getConfigTestPage;
      const res = await api(params);
      if (res.data.success) {
        message.success('运行成功');
        setHitData(res.data.value.total);
        setPerHitList(res.data.value.list);
        setHitDataStatus(true);
        setTestStatus(true);
      }
    } catch (error) {
    } finally {
      setConfirmLoading(false);
      setRunningLoading(false);
    }
  };

  const handleChange = (value: any) => {
    if (value) {
      setTimeRange(value);
      const startTime = value[0].toDate();
      const endTime = value[1].toDate();
      startTimeRef.current = startTime;
      endTimeRef.current = endTime;
      submitRunningData();
    } else {
      setTimeRange([]);
    }
  };

  const handleToday = async () => {
    await form.validateFields();
    const today = [dayjs().startOf('day'), dayjs().endOf('day')];
    startTimeRef.current = dayjs().startOf('day');
    endTimeRef.current = dayjs().endOf('day');
    setTimeRange(today);
    setCurrentTime(1);
    submitRunningData();
  };

  const handleYesterDay = async () => {
    await form.validateFields();
    const tomorrow = [dayjs().startOf('day').add(-1, 'day'), dayjs().endOf('day').add(-1, 'day')];
    startTimeRef.current = dayjs().startOf('day').add(-1, 'day');
    endTimeRef.current = dayjs().endOf('day').add(-1, 'day');
    setTimeRange(tomorrow);
    setCurrentTime(2);
    submitRunningData();
  };

  const handleLastWeek = async () => {
    await form.validateFields();
    const lastWeek = [dayjs().startOf('day').subtract(6, 'days'), dayjs().endOf('day')];
    startTimeRef.current = dayjs().startOf('day').subtract(6, 'days');
    endTimeRef.current = dayjs().endOf('day');
    setTimeRange(lastWeek);
    setCurrentTime(3);
    submitRunningData();
  };

  return (
    <>
      <Modal
        width={700}
        title="策略试运行"
        rootClassName={styles.strategicTrialModal}
        open={visible}
        onOk={onCancel}
        onCancel={onCancel}
      >
        <Spin spinning={runningLoading}>
          <div style={{ display: 'flex', flexWrap: 'wrap', marginTop: 32 }}>
            <div style={{ color: '#4E5969', marginBottom: 8, width: '100%' }}>时间范围：</div>
            <span>
              <RangePicker
                showTime={{ format: 'HH:mm:ss' }}
                format="YYYY-MM-DD HH:mm:ss"
                onChange={handleChange}
                value={timeRange}
              />
              <Button type={currentTime === 1 ? 'primary' : undefined} onClick={handleToday} style={{ marginLeft: 16 }}>
                今天
              </Button>
              <Button
                type={currentTime === 2 ? 'primary' : undefined}
                onClick={handleYesterDay}
                style={{ marginLeft: 16 }}
              >
                昨天
              </Button>
              <Button
                type={currentTime === 3 ? 'primary' : undefined}
                onClick={handleLastWeek}
                style={{ marginLeft: 16 }}
              >
                最近一周
              </Button>
            </span>
          </div>
          <div style={{ marginTop: 24 }}>
            {hitDataStatus ? (
              <>
                <div style={{ color: '#4E5969', marginBottom: 8 }}>结果：</div>
                <div>
                  <span style={{ margin: '0 4px', color: antConfig.theme.token.colorPrimary }}>{estimateData}</span>
                  条数据中有
                  <a
                    style={{ textDecoration: 'underline', margin: '0 4px' }}
                    onClick={() => setHitListModalVisible(true)}
                  >
                    <span style={{ margin: '0 4px', color: antConfig.theme.token.colorPrimary }}>{hitData}</span>
                  </a>
                  条命中
                </div>
                {perHitList.length > 0 && (
                  <>
                    <div style={{ marginTop: 24, color: '#4E5969' }}>命中会话清单：（前30条）</div>
                    <div className={styles.perHistList}>
                      {perHitList.map((item: any) => {
                        return (
                          <div className={styles.perHistListItem} key={item.sessionId}>
                            {item.sessionId}
                          </div>
                        );
                      })}
                    </div>
                  </>
                )}
              </>
            ) : (
              ''
            )}
          </div>
        </Spin>
      </Modal>
      <Modal
        onCancel={() => setHitListModalVisible(false)}
        open={hitListModalVisible}
        title="查看结果"
        width={700}
        footer={null}
      >
        <Table
          pagination={{
            pageSize: hitPagination.pageSize,
            current: hitPagination.current,
            total: hitListTotal,
          }}
          onChange={(pagination) => {
            const obj: any = {
              current: pagination.pageSize !== hitPagination.pageSize ? 1 : pagination.current,
              pageSize: pagination.pageSize || 10,
            };
            setHitPagination(obj);
          }}
          dataSource={hitList}
          rowKey={'sessionId'}
          columns={[
            {
              dataIndex: 'sessionId',
              key: 'sessionId',
              title: '会话id',
            },
          ]}
        />
      </Modal>
    </>
  );
};

export default StrategicTrialModal;
