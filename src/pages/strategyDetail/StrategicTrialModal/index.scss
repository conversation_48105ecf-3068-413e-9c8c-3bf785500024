.strategicTrialModal {
  :global {
    .ant-modal {
      .ant-modal-content {
        .ant-modal-header {
          margin-bottom: 16px;
        }
        .ant-modal-body {
          padding: 0 32px;
          .ant-picker {
            width: 360px;
            background: #f2f3f5;
            border: none;
          }
          .ant-btn-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: rgba(93, 95, 239, 0.1);
          }
        }
        .ant-modal-footer {
          margin-top: 16px;
        }
      }
    }
  }
}
.perHistList {
  display: flex;
  flex-wrap: wrap;
  max-height: 250px;
  overflow-y: auto;
}
.perHistListItem {
  color: #4e5969;
  margin-right: 24px;
  margin-top: 8px;
  font-size: 14px;
  line-height: 22px;
}
.ruleItemName {
  &::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}
