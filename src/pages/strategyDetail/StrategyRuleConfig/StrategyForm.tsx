import MoreClose from '@/assets/moreClose.svg';
import MoreOpen from '@/assets/moreOpen.svg';
import Collapse from '@/components/Collapse';
import { maintainAnalyzerDict } from '@/services/ce';
import { convertToChineseNumber } from '@/utils';
import { ECondition, EFieldsType } from '@/utils/constants';
import { Button, FormInstance, Row } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty } from 'lodash';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import TextContentTestModal from '../TextContentTestModal';
import styles from './index.scss';
import RenderFields from './RenderFields';

interface IConfig {
  operator: ECondition;
  groups: {
    groupKey: number;
    fields: IRuleField[];
  }[];
}

interface IProps {
  tenantNo: string;
  fieldsList: IFieldConfig[];
  operatorMap: any;
  setVisible: (visible: boolean) => void;
  getAddDic: (configFields: any) => string[];
  onlyRead?: boolean;
  form: FormInstance;
  feildId?: string;
  isRealTime?: boolean;
}

const StrategyForm = forwardRef<any, IProps>(
  ({ onlyRead, fieldsList, tenantNo, form, operatorMap, getAddDic, setVisible, feildId, isRealTime }, ref) => {
    const rowKey = useRef(0);
    const groupKey = useRef(0);
    const [textContentTestModalVisible, setTextContentTestModalVisible] = useState(false);
    const [currentTest, setCurrentTest] = useState<any>({});
    const [loading, setLoading] = useState(false);
    const [config, setConfig] = useState<IConfig>(() => {
      return {
        operator: ECondition.AND,
        groups: [
          {
            groupKey: groupKey.current++,
            fields: [
              {
                fieldCode: '',
                fieldName: '',
                fieldOperator: '',
                fieldType: 0,
                fieldValue: '',
                rowKey: rowKey.current++,
              },
            ],
          },
        ],
      };
    });

    const getRealTimeConfigFields = (isAdd?: boolean): any[] => {
      const realTimeFields = ['sessionType', 'textContent'];
      const fields = realTimeFields
        .filter((v) => !!fieldsList?.find((item) => item.targetFieldCode === v))
        .map((v) => ({
          fieldCode: v,
          fieldName: (fieldsList as any[])?.find((item) => item.targetFieldCode === v)?.label,
          fieldOperator: undefined,
          fieldType: (fieldsList as any[])?.find((item) => item.targetFieldCode === v)?.fieldType,
          fieldValue: undefined,
          rowKey: rowKey.current++,
        }));
      const newFields = fields?.reduce(
        (acc, cur) => {
          return {
            ...acc,
            [cur.rowKey]: cur,
          };
        },
        isAdd ? form.getFieldValue(feildId ? [feildId, 'fields'] : 'fields') || {} : {}
      );
      form.setFieldValue(feildId ? [feildId, 'fields'] : 'fields', newFields);
      return fields;
    };

    const initRealTimeConfig = () => {
      if (!fieldsList?.length || !isRealTime) return;
      rowKey.current = 0;
      groupKey.current = 0;
      const fields = getRealTimeConfigFields();
      const newConfig: any = {
        operator: ECondition.AND,
        groups: [
          {
            groupKey: groupKey.current++,
            fields,
          },
        ],
      };
      setConfig(newConfig);
    };

    const getFormFields = (values: any) => {
      const res: IConfig = cloneDeep(config);
      const rowKeyList: Number[] = [];
      res.groups.map((group) => {
        group.fields.map((field) => {
          rowKeyList.push(field.rowKey);
        });
      });
      let formData = feildId ? values[feildId]?.fields : values.fields;
      if (!Array.isArray(formData) && formData) {
        formData = Object.entries(formData || {}).map(([k, v]) => ({ ...(v || {}), rowKey: Number(k) }));
      }
      const formFields = Array.from(formData || [])
        .map((item: any, index: number) => {
          return {
            ...item,
            rowKey: item.rowKey ?? index,
          };
        })
        .filter((item: any) => rowKeyList.includes(item.rowKey));
      const formFieldsMap = formFields.reduce((acc: any, cur: any) => {
        acc[cur.rowKey] = cur;
        return acc;
      }, {});
      res.groups.forEach((group) => {
        group.fields.forEach((field) => {
          const formField = formFieldsMap[field.rowKey];
          if (formField) {
            field.fieldCode = formField.fieldCode;
            field.fieldOperator = formField.fieldOperator;
            field.fieldValue = formField.fieldValue;
            const fieldType = fieldsList.find((field) => field.targetFieldCode === formField.fieldCode)?.fieldType;
            if (fieldType === EFieldsType.Date) {
              field.fieldValue =
                formField.fieldValue && formField.fieldValue.valueOf
                  ? formField.fieldValue.valueOf()
                  : formField.fieldValue;
            }
            field.fieldName = fieldsList.find((item) => item.targetFieldCode === formField.fieldCode)?.showName || '';
            field.fieldType = fieldsList.find((item) => item.targetFieldCode === formField.fieldCode)?.fieldType || 0;
            //@ts-ignore
            delete field.rowKey;
          }
        });
        //@ts-ignore
        delete group.groupKey;
      });
      //@ts-ignore
      res.groups = res.groups.map((item) => item.fields);
      //@ts-ignore
      feildId && (res.tenantNo = feildId);
      return res;
    };

    const getFields = (nameList?: string[], formObj?: any) => {
      return new Promise((resolve, reject) => {
        form
          .validateFields(nameList, formObj)
          .then((values) => {
            resolve(getFormFields(values));
          })
          .catch((error: any) => {
            if (!error.errorFields?.length && error.outOfDate) {
              resolve(getFormFields(error.values));
            } else {
              reject(error);
            }
          });
      });
    };

    const initFields = (config: any) => {
      if (isEmpty(config)) {
        return;
      }
      rowKey.current = 0;
      groupKey.current = 0;
      const newConfig = cloneDeep(config);
      newConfig.operator = newConfig.operateType;
      newConfig.groups = newConfig.fieldGroupList;
      delete newConfig.operateType;
      delete newConfig.fieldGroupList;
      const newFields: any = [];
      const newGroups = newConfig.groups;
      newGroups.forEach((group: any, index: number) => {
        newGroups[index] = { fields: group, groupKey: groupKey.current++ };
        newGroups[index].fields.forEach((field: any, fieldIndex: number) => {
          const obj = { ...field, rowKey: rowKey.current++ };
          if (obj.fieldType === EFieldsType.Date) {
            obj.fieldValue = obj.fieldValue ? dayjs(Number(obj.fieldValue)) : '';
          }
          newGroups[index].fields[fieldIndex] = obj;
          newFields.push(obj);
        });
      });
      setConfig(newConfig);
      form.setFieldValue(feildId ? [feildId, 'fields'] : 'fields', newFields);
    };

    const handleAddRule = (groupKey: number) => {
      const newFields = cloneDeep(config.groups);
      const group = newFields.find((item) => item.groupKey === groupKey);
      if (!group) {
        return;
      }
      group.fields.push({
        fieldCode: '',
        fieldName: '',
        fieldOperator: '',
        fieldType: 0,
        fieldValue: '',
        rowKey: rowKey.current++,
      });
      setConfig({
        ...config,
        groups: newFields,
      });
    };

    const handleAddGroup = () => {
      const newFields = cloneDeep(config.groups);
      newFields.push({
        groupKey: groupKey.current++,
        fields: isRealTime
          ? getRealTimeConfigFields(true)
          : [
              {
                fieldCode: '',
                fieldName: '',
                fieldOperator: '',
                fieldType: 0,
                fieldValue: '',
                rowKey: rowKey.current++,
              },
            ],
      });
      setConfig({
        ...config,
        groups: newFields,
      });
    };

    const handleDeleteGroup = (groupKey: number) => {
      const newFields = cloneDeep(config.groups);
      const group = newFields.find((item) => item.groupKey === groupKey);
      if (!group) {
        return;
      }
      newFields.splice(newFields.indexOf(group), 1);
      setConfig({
        ...config,
        groups: newFields,
      });
    };

    const onMaintainAnalyzerDict = async () => {
      const configFields = await getFields(feildId ? [feildId] : undefined, { recursive: true });
      const addDic = getAddDic(configFields);
      if (!addDic?.length) {
        setVisible(true);
        return;
      }
      try {
        setLoading(true);
        const res = await maintainAnalyzerDict({ addDic });
        if (res.data.success) {
          setVisible(true);
        }
      } finally {
        setLoading(false);
      }
    };

    useImperativeHandle(ref, () => ({
      getFields,
      initFields,
      initRealTimeConfig,
      handleAddGroup,
    }));

    return (
      <>
        <div className={styles.formContainer}>
          <div className={styles.groupLayout}>
            {config.groups.length > 1 && (
              <div className={styles.relation}>
                <div
                  onClick={() => {
                    if (onlyRead) return;
                    const obj = { ...config };
                    const key = config.operator === ECondition.AND ? ECondition.OR : ECondition.AND;
                    obj.operator = Number(key);
                    setConfig(obj);
                  }}
                  className={styles.relationText}
                >
                  {config.operator === ECondition.AND ? '且' : '或'}
                </div>
                <div className={styles.line} />
              </div>
            )}
            <div style={{ width: config.groups.length <= 1 ? '100%' : '' }} className={styles.formLayout}>
              {config.groups.map((group, index) => {
                const groupKey = group.groupKey;
                const renderTitle = (icon: any) => (
                  <Row style={{ justifyContent: 'space-between' }}>
                    <Row>
                      <img style={{ marginRight: 8 }} src={icon} />
                      <div style={{ color: 'rgba(29, 33, 41, 1)', fontWeight: 600 }} className={styles.ruleItemName}>
                        策略组合{convertToChineseNumber(index + 1)}
                      </div>
                    </Row>
                    {config.groups.length > 1 && !onlyRead && (
                      <div
                        style={{ fontSize: 14, cursor: 'pointer', color: '#4E5969' }}
                        onClick={() => handleDeleteGroup(groupKey)}
                      >
                        删除
                      </div>
                    )}
                  </Row>
                );
                return (
                  <div className={styles.group} key={(feildId || '') + groupKey + '__' + index}>
                    <Collapse
                      defaultOpen
                      key={(feildId || '') + '__' + groupKey + '__' + index}
                      children={
                        <>
                          <div className={styles.fieldsLayout}>
                            {group.fields.length > 1 && (
                              <div className={styles.relation}>
                                <div className={styles.relationText}>且</div>
                                <div className={styles.line}></div>
                              </div>
                            )}
                            <div
                              style={{ width: group.fields.length <= 1 ? '100%' : '' }}
                              className={styles.formLayout}
                            >
                              <div className={styles.groupFields}>
                                <RenderFields
                                  fields={group.fields}
                                  fieldsList={fieldsList}
                                  groupKey={groupKey}
                                  setCurrentTest={setCurrentTest}
                                  tenantNo={tenantNo}
                                  setTextContentTestModalVisible={setTextContentTestModalVisible}
                                  operatorMap={operatorMap}
                                  setConfig={setConfig}
                                  onlyRead={onlyRead}
                                  feildId={feildId}
                                  isRealTime={isRealTime}
                                />
                              </div>
                            </div>
                          </div>
                          {!onlyRead && !isRealTime && (
                            <div className={styles.groupAction}>
                              <div style={{ position: 'relative', left: -12 }}>
                                <Button
                                  onClick={() => handleAddRule(groupKey)}
                                  style={{ fontWeight: 500 }}
                                  icon={<div className={styles.corss} />}
                                  type="link"
                                >
                                  添加条件
                                </Button>
                              </div>
                            </div>
                          )}
                        </>
                      }
                      activeLabel={renderTitle(MoreClose)}
                      unActiveLabel={renderTitle(MoreOpen)}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <TextContentTestModal
          modalVisible={textContentTestModalVisible}
          setModalVisible={setTextContentTestModalVisible}
          currentTest={currentTest}
        />
        {!onlyRead && (
          <Row
            className={styles.formContainer}
            style={{
              justifyContent: 'flex-end',
              marginTop: 12,
            }}
          >
            <Button onClick={onMaintainAnalyzerDict} loading={loading} type="primary" ghost style={{ fontWeight: 500 }}>
              策略试运行
            </Button>
          </Row>
        )}
      </>
    );
  }
);

export default StrategyForm;
