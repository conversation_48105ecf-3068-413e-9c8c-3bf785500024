import { numberToLetter, selectSearch } from '@/utils';
import { EFieldsType } from '@/utils/constants';
import { CloseOutlined } from '@ant-design/icons';
import { Button, DatePicker, Form, Input, InputNumber, Select } from 'antd';
import { cloneDeep } from 'lodash';
import React, { FC, useMemo } from 'react';
import styles from './index.scss';

interface IProps {
  fields: IRuleField[];
  fieldsList: IFieldConfig[];
  groupKey: number;
  setCurrentTest: React.Dispatch<any>;
  tenantNo: string;
  setTextContentTestModalVisible: React.Dispatch<boolean>;
  operatorMap: any;
  setConfig: React.Dispatch<any>;
  onlyRead?: boolean;
  feildId?: string;
  isRealTime?: boolean;
}

const RenderFields: FC<IProps> = ({
  fields,
  fieldsList,
  groupKey,
  setCurrentTest,
  tenantNo,
  setTextContentTestModalVisible,
  operatorMap,
  setConfig,
  onlyRead,
  feildId,
  isRealTime,
}) => {
  const formName = useMemo(() => (feildId ? [feildId, 'fields'] : ['fields']), [feildId]);

  const renderFieldValue = (rowKey: number | string, getFieldValue: any) => {
    const fieldCode = getFieldValue([...formName, rowKey, 'fieldCode']);
    const fieldType = fieldsList?.find((field) => field.targetFieldCode === fieldCode)?.fieldType;
    try {
      if (fieldType === EFieldsType.Date) {
        return <DatePicker showTime style={{ width: '100%' }} placeholder="请选择字段值" />;
      }
      if (fieldType === EFieldsType.Number) {
        return <InputNumber style={{ width: '100%' }} placeholder="请输入字段值" />;
      }
      return <Input maxLength={500} placeholder="请输入字段值" />;
    } catch (error) {
      return <Input maxLength={500} placeholder="请输入字段值" />;
    }
  };

  const handleDeleteRule = (groupKey: number, rowKey: number | string) => {
    setConfig((config: any) => {
      const newFields = cloneDeep(config.groups);
      const group = newFields.find((item: any) => item.groupKey === groupKey);
      if (!group) {
        return config;
      }
      group.fields = group.fields.filter((field: any) => field.rowKey?.toString() !== rowKey?.toString());
      return {
        ...config,
        groups: newFields,
      };
    });
  };

  return (
    <>
      {fields.map((field, index: number) => {
        const rowKey = field.rowKey?.toString();
        return (
          <div
            className={`${styles.formField} ${index === fields.length - 1 ? styles.formFieldEnd : ''}`}
            key={(feildId || '') + '__' + groupKey + '__' + rowKey + '__' + index}
          >
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                (feildId ? prevValues?.[feildId]?.fields : prevValues?.fields)?.[rowKey]?.fieldCode !==
                (feildId ? currentValues?.[feildId]?.fields : currentValues?.fields)?.[rowKey]?.fieldCode
              }
            >
              {({ getFieldValue }) => (
                <>
                  <div style={{ display: 'flex' }}>
                    <div className={styles.letter}>{numberToLetter(index + 1)}</div>
                    <div style={{ width: 'calc(100% - 32px)', display: 'flex' }}>
                      <Form.Item
                        rules={[{ required: true, message: '请选择字段' }]}
                        className={styles.formItem}
                        name={[...formName, rowKey, 'fieldCode']}
                        style={{ width: 'calc((100% - 60px) * 0.4)' }}
                      >
                        <Select
                          placeholder="请选择字段"
                          options={fieldsList}
                          fieldNames={{ label: 'label', value: 'value' }}
                          disabled={isRealTime || onlyRead}
                          {...selectSearch}
                        />
                      </Form.Item>
                      {getFieldValue([...formName, rowKey, 'fieldCode']) === 'textContent' ? (
                        <>
                          {!onlyRead && (
                            <Button
                              style={{ marginRight: !isRealTime ? 12 : 0, fontWeight: 500 }}
                              onClick={() => {
                                setCurrentTest({
                                  fieldValue: getFieldValue([...formName, rowKey, 'fieldValue']) || '',
                                  tenantNo,
                                });
                                setTextContentTestModalVisible(true);
                              }}
                              type="primary"
                              ghost
                            >
                              规则试运行
                            </Button>
                          )}
                        </>
                      ) : (
                        <>
                          <Form.Item
                            rules={[{ required: true, message: '请选择操作符' }]}
                            className={styles.formItem}
                            name={[...formName, rowKey, 'fieldOperator']}
                            style={{ width: 'calc((100% - 60px) * 0.2)' }}
                          >
                            <Select
                              placeholder="请选择操作符"
                              options={operatorMap[getFieldValue([...formName, rowKey, 'fieldCode'])]}
                            />
                          </Form.Item>
                          <Form.Item
                            style={{ width: 'calc((100% - 60px) * 0.4)' }}
                            rules={[{ required: true, message: '请输入字段值' }]}
                            className={styles.formItem}
                            name={[...formName, rowKey, 'fieldValue']}
                          >
                            {renderFieldValue(rowKey, getFieldValue)}
                          </Form.Item>
                        </>
                      )}
                      {!onlyRead && !isRealTime && (
                        <CloseOutlined
                          style={{
                            visibility: index > 0 ? 'visible' : 'hidden',
                            color: '#4E5969',
                            position: 'relative',
                            top: index === fields.length - 1 ? 0 : '-10px',
                          }}
                          onClick={() => handleDeleteRule(groupKey, rowKey)}
                        />
                      )}
                    </div>
                  </div>
                  {getFieldValue([...formName, rowKey, 'fieldCode']) === 'textContent' && (
                    <Form.Item
                      style={{
                        marginLeft: 32,
                        marginRight: isRealTime || onlyRead ? 0 : 26,
                        marginBottom: index === fields.length - 1 ? 0 : 20,
                        marginTop: index === fields.length - 1 ? 16 : 0,
                      }}
                      rules={[{ required: true, message: '请输入会话内容' }]}
                      className={styles.formItem}
                      name={[...formName, rowKey, 'fieldValue']}
                    >
                      <Input.TextArea
                        maxLength={4000}
                        showCount
                        autoSize={{ minRows: 2, maxRows: 6 }}
                        placeholder="请输入会话内容"
                      />
                    </Form.Item>
                  )}
                </>
              )}
            </Form.Item>
          </div>
        );
      })}
    </>
  );
};

export default RenderFields;
