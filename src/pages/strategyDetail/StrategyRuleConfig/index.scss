.formField {
  .formItem {
    margin-right: 12px;
    margin-bottom: 20px;
    flex: 1;
    &:last-child {
      margin-right: 0;
    }
  }
}

.group {
  border-radius: 8px;
  position: relative;
  background-color: #fff;
  padding: 20px 16px;
  border: 1px solid #e4e5e6;
  margin-top: 20px;
  &:first-child {
    margin-top: 0;
  }
}
.groupName {
  font-size: 12px;
  border-radius: 6px 6px 0px 0px;
  color: #fff;
  background-color: #e8e9f9;
  padding: 4px 12px 4px 12px;
  width: fit-content;
  color: var(--primary-color);
}
.groupFields {
}
.groupAction {
  padding: 8px 0;
  padding-bottom: 0;
  display: flex;
  justify-content: space-between;
}

.groupCondition {
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
}
.title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: #1d2129;
}
.titleContainer {
  display: flex;
  justify-content: space-between;
  width: 70%;
  margin-bottom: 12px;
}

.formContainer {
  padding-left: 0 !important;
  border-radius: 8px;
  width: 70%;
}

@media (max-width: 1200px) {
  .formContainer {
    width: 100%;
  }
  .titleContainer {
    width: 100%;
  }
}

.groupOperatorTrigger {
  margin: 0 auto;
  display: flex;
  align-items: center;
  width: fit-content;
  justify-content: center;
  padding: 4px 12px 4px 12px;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  background-color: #f2f2fe;
  border-radius: 100px;
  cursor: pointer;
  margin-top: 20px;
  margin-bottom: 8px;
}
.groupOperatorTriggerNew {
  width: 54px;
  height: 24px;
  border-radius: 100px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 8px;
  color: var(--primary-color);
  background-color: #f2f2fe;
}
.formFieldEnd {
  :global {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}
.letter {
  width: 20px;
  height: 20px;
  background-color: var(--primary-color);
  text-align: center;
  line-height: 20px;
  border-radius: 2px;
  color: #fff;
  margin-right: 12px;
  margin-top: 6px;
}

.rowItem {
  display: flex;
}

.groupLayout {
  width: 100%;
  display: flex;
  .relation {
    width: 28px;
    margin-right: 12px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    .line {
      width: 3px;
      background-color: rgba(223, 223, 252, 1);
      top: 0;
      bottom: 0;
      position: absolute;
    }
    .relationText {
      cursor: pointer;
      position: relative;
      z-index: 1;
      height: 28px;
      width: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      color: var(--primary-color);
      background-color: #e7e7fd;
      font-weight: 600;
      line-height: 28px;
    }
  }
  .formLayout {
    width: calc(100% - 40px);
  }
  .fieldsLayout {
    width: 100%;
    display: flex;
    margin-top: 16px;
    .relation {
      width: 24px;
      margin-right: 12px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      .line {
        width: 2px;
        background-color: rgba(223, 223, 252, 1);
        top: 0;
        bottom: 0;
        position: absolute;
      }
      .relationText {
        position: relative;
        z-index: 1;
        height: 24px;
        cursor: default;
        width: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        color: var(--primary-color);
        background-color: #e7e7fd;
        font-weight: 600;
        line-height: 24px;
      }
    }

    .formLayout {
      width: calc(100% - 36px);
    }
  }

  .corss {
    background: #f2f2fe;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: relative;
    &::before,
    &::after {
      content: '';
      position: absolute;
      background-color: var(--primary-color);
      border-radius: 4px;
      transform: translate(-50%, -50%);
      top: 50%;
      left: 50%;
    }
    &::before {
      width: 1.5px;
      height: 10px;
    }

    &::after {
      width: 10px;
      height: 1.5px;
    }
  }
}

.tabs {
  :global {
    .ant-tabs-nav {
      margin-bottom: 12px;
      font-weight: 500;
      .ant-tabs-tab:not(.ant-tabs-tab-active) {
        color: #4e5969;
      }
    }
  }
}
