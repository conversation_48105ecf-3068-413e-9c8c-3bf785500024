/**策略定义 */
import useRobotData from '@/hooks/useRobotData';
import { getFieldList } from '@/services/ce';
import { QUALITY_TYPE } from '@/utils/constants';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Form, message, Tabs, Tooltip } from 'antd';
import React, { Dispatch, FC, SetStateAction, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { getOperatorMap } from '../index';
import StrategicTrialModal from '../StrategicTrialModal';
import styles from './index.scss';
import StrategyForm from './StrategyForm';

interface IProps {
  tenantNo: string;
  fieldsList: IFieldConfig[];
  operatorMap: any;
  getAddDic: (configFields: any) => string[];
  tenantNos?: any[];
  isRedLine?: string;
  onlyRead?: boolean;
  operatorList?: any;
  setTestStatus: Dispatch<SetStateAction<boolean>>;
  setConfirmLoading: Dispatch<SetStateAction<boolean>>;
  qualityType?: string;
}

const AddAssemblyBtn: FC<{ onClick: () => void }> = ({ onClick }) => (
  <div onClick={onClick} style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
    <div
      style={{
        background: '#27AE60',
        padding: 4,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <PlusOutlined style={{ fontSize: 12, color: '#fff', fontWeight: 500 }} />
    </div>
    <div
      style={{
        marginLeft: 8,
        color: '#27AE60',
        fontWeight: 500,
      }}
    >
      添加组合
    </div>
  </div>
);

const StrategyRuleConfig = React.forwardRef<any, IProps>(
  (
    {
      fieldsList,
      operatorMap,
      tenantNo,
      getAddDic,
      tenantNos,
      isRedLine,
      onlyRead,
      operatorList,
      setTestStatus,
      setConfirmLoading,
      qualityType,
    },
    ref
  ) => {
    const [form] = Form.useForm();
    const { applicationList } = useRobotData();
    const [fieldsListData, setFieldsListData] = useState<any>({});
    const [visible, setVisible] = useState(false);
    const strategyFormRef = useRef<any>(null);
    const strategyRedFormsRef = useRef<any>({});
    const currentTenantNoRef = useRef<string>();

    const isRealTime = useMemo(() => (qualityType as any) === QUALITY_TYPE.REAL_TIME, [qualityType]);

    const tabItems = useMemo(() => {
      if (isRedLine !== '1') return [];
      const filteredList = tenantNos?.reduce((acc: any[], tenantNo: string) => {
        const matchingItems = applicationList?.filter((item: any) => item.botNo === tenantNo);
        return matchingItems?.length ? acc.concat(matchingItems) : acc;
      }, []);
      return (
        filteredList?.map((item: any) => {
          return {
            key: item.botNo,
            label: item.botName,
            forceRender: true,
            children: (
              <>
                {!onlyRead && (
                  <div className={styles.titleContainer} style={{ justifyContent: 'flex-end' }}>
                    <AddAssemblyBtn onClick={() => strategyRedFormsRef.current[item.botNo]?.handleAddGroup?.()} />
                  </div>
                )}
                <StrategyForm
                  ref={(ref) => (strategyRedFormsRef.current[item.botNo] = ref)}
                  onlyRead={onlyRead}
                  fieldsList={fieldsListData[item.botNo]}
                  tenantNo={item.botNo}
                  feildId={item.botNo}
                  form={form}
                  operatorMap={getOperatorMap(fieldsListData[item.botNo], operatorList)}
                  getAddDic={getAddDic}
                  setVisible={(bool: boolean) => {
                    currentTenantNoRef.current = item.botNo;
                    setVisible(bool);
                  }}
                  isRealTime={isRealTime}
                />
              </>
            ),
          };
        }) || []
      );
    }, [isRedLine, tenantNos, applicationList, onlyRead, form, fieldsListData, operatorList, getAddDic, isRealTime]);

    const handleFieldList = async () => {
      const nos = tenantNos?.filter((v: string) => !fieldsListData[v]);
      if (isRedLine !== '1' || !nos?.length) return;
      const promises = [...nos].map((v) => getFieldList(v, 'N'));
      const res = await Promise.all(promises);
      const data: any = {};
      res?.forEach((item, index) => {
        if (item.data.value) {
          data[nos[index]] = item.data.value.map((item: IFieldConfig) => {
            return {
              ...item,
              label: item.showName,
              value: item.targetFieldCode,
            };
          });
        } else {
          data[nos[index]] = [];
        }
      });
      setFieldsListData((preState: any) => ({ ...preState, ...data }));
    };

    useEffect(() => {
      handleFieldList();
    }, [isRedLine, tenantNos]);

    const getFields = (...args: any[]) => {
      if (isRedLine === '1') {
        const promises = Object.values(strategyRedFormsRef.current).map((item: any) => item?.getFields?.(...args));
        return Promise.all(promises).catch((err) => {
          const noSet = new Set();
          err?.errorFields?.forEach((item: any) => {
            item?.name?.[0] && noSet.add(item.name[0]);
          });
          const botName = applicationList
            ?.filter((item: any) => noSet.has(item.botNo))
            ?.map((item: any) => item.botName)
            ?.join('、');
          message.error(`请填写${botName + '的' || ''}策略定义`);
          return Promise.reject(err);
        });
      } else {
        return strategyFormRef.current?.getFields(...args);
      }
    };

    const handleStrategicTrialFields = (...args: any[]) => {
      if (isRedLine === '1') {
        return strategyRedFormsRef.current?.[currentTenantNoRef.current!]?.getFields([currentTenantNoRef.current], {
          recursive: true,
        });
      } else {
        return strategyFormRef.current?.getFields(...args);
      }
    };

    const initFields = (...args: any[]) => {
      if (isRedLine === '1') {
        const config = args[0];
        config?.forEach((item: any) => {
          strategyRedFormsRef.current?.[item?.tenantNo]?.initFields(item?.strategyGroupConfigDTO);
        });
      } else {
        strategyFormRef.current?.initFields(...args);
      }
    };

    const changeBasicInfoForm = () => {
      if (isRedLine === '1') {
        Object.values(strategyRedFormsRef.current).forEach((item: any) => item?.initRealTimeConfig?.());
      } else {
        strategyFormRef.current?.initRealTimeConfig();
      }
    };

    useImperativeHandle(ref, () => ({
      form,
      getFields,
      initFields,
      changeBasicInfoForm,
    }));

    const handleValuesChange = (changedValues: any) => {
      if (isRedLine !== '1') {
        const fields = changedValues.fields;
        for (let i in fields) {
          const field = fields[i];
          if (field.fieldCode) {
            form.setFieldValue(['fields', i, 'fieldOperator'], undefined);
            form.setFieldValue(['fields', i, 'fieldValue'], undefined);
          }
        }
      } else {
        Object.entries(changedValues || {}).forEach(([key, value]: any) => {
          const fields = value?.fields;
          for (let i in fields) {
            const field = fields[i];
            if (field.fieldCode) {
              form.setFieldValue([key, 'fields', i, 'fieldOperator'], undefined);
              form.setFieldValue([key, 'fields', i, 'fieldValue'], undefined);
            }
          }
        });
      }
      setTestStatus(false);
    };

    return (
      <>
        <div className={styles.titleContainer}>
          <div className={styles.title}>
            策略定义
            <Tooltip title="使用一个或者多个条件组合进行过滤会话，过滤后的会话集合即为一个质检策略">
              <QuestionCircleOutlined style={{ fontSize: 16, color: '#BABEC4', marginLeft: 4, cursor: 'pointer' }} />
            </Tooltip>
          </div>
          {!onlyRead && isRedLine !== '1' && (
            <AddAssemblyBtn onClick={() => strategyFormRef.current?.handleAddGroup()} />
          )}
        </div>
        <Form onValuesChange={handleValuesChange} form={form} disabled={onlyRead}>
          {isRedLine === '1' ? (
            <Tabs className={styles.tabs} items={tabItems} />
          ) : (
            <StrategyForm
              ref={strategyFormRef}
              onlyRead={onlyRead}
              fieldsList={fieldsList}
              tenantNo={tenantNo}
              form={form}
              operatorMap={operatorMap}
              getAddDic={getAddDic}
              setVisible={setVisible}
              isRealTime={isRealTime}
            />
          )}
        </Form>
        <StrategicTrialModal
          visible={visible}
          setVisible={setVisible}
          qualityType={qualityType}
          currentTenantNo={tenantNo}
          setTestStatus={setTestStatus}
          setConfirmLoading={setConfirmLoading}
          getFields={handleStrategicTrialFields}
        />
      </>
    );
  }
);
export default StrategyRuleConfig;
