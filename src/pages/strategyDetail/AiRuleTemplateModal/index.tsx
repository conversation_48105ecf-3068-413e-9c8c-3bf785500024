import EasyTable from '@/components/EasyTable';
import { getAiRuleTemplateList } from '@/services/ce';
import { message, Modal, Radio } from 'antd';
import { isEmpty } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

interface IProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (params: any) => void;
}
const AiRuleTemplateModal = (props: IProps) => {
  const { open, onCancel, onConfirm } = props;
  const request = useCallback(async () => {
    const res = await getAiRuleTemplateList();
    return {
      total: res.data.value.length,
      data: res.data.value,
    };
  }, []);
  const [current, setCurrent] = useState<any>({});

  const columns = useMemo(() => {
    return [
      {
        title: '',
        width: 50,
        key: 'action',
        render: (_: any, record: any) => {
          return <Radio onClick={() => setCurrent(record)} checked={record.id === current.id} />;
        },
      },
      {
        title: '规则名称',
        dataIndex: 'aiRuleName',
        key: 'aiRuleName',
      },
      {
        title: '规则描述',
        dataIndex: 'aiRuleDesc',
        key: 'aiRuleDesc',
      },
    ];
  }, [current]);
  useEffect(() => {
    setCurrent({});
  }, [open]);
  return (
    <Modal
      width={800}
      open={open}
      destroyOnClose
      onCancel={onCancel}
      onOk={() => {
        if (isEmpty(current)) {
          message.warning('请先选择模版');
          return;
        }
        onConfirm(current);
      }}
      title="选择AI规则模版"
    >
      <EasyTable style={{ marginTop: 24 }} columns={columns} request={request} />
    </Modal>
  );
};

export default AiRuleTemplateModal;
