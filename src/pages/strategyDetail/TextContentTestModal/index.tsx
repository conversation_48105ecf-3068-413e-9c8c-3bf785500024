import useRobotData from '@/hooks/useRobotData';
import { regularTestData } from '@/services/ce';
import { requiredRules } from '@/utils/constants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Input, message, Modal, Select, Tooltip } from 'antd';
import React, { FC, useMemo, useState } from 'react';

const TextContentTestModal: FC<any> = ({ currentTest, modalVisible, setModalVisible, tenantNos, isRedLine }) => {
  const [isShow, setIsShow] = useState(false);
  const [ishitData, setHitData] = useState<boolean | null>(null);
  const { fieldValue, tenantNo } = currentTest;
  const [loading, setLoading] = useState(false);
  const { applicationList } = useRobotData();
  const [form] = Form.useForm();

  const robotList = useMemo(() => {
    return applicationList?.filter((v: any) => tenantNos?.includes(v.botNo));
  }, [applicationList, tenantNos]);

  const submitRunningdata = async () => {
    const values = await form.validateFields();
    setLoading(true);
    const params = {
      regularExpression: fieldValue || '',
      textContent: values.textContent,
      tenantNo: values.tenantNo || tenantNo,
    };
    try {
      const res = await regularTestData(params);
      if (res.data.success) {
        message.success('运行成功');
        setIsShow(true);
        if (res.data.value) {
          setHitData(res.data.value.isHit === '命中' ? true : false);
        } else {
          setHitData(false);
        }
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error(error);
    }
  };

  return (
    <Modal
      onOk={submitRunningdata}
      width={700}
      title="规则试运行"
      open={modalVisible}
      bodyStyle={{ height: '400px', overflow: 'auto' }}
      onCancel={() => {
        setModalVisible(false);
        setHitData(null);
      }}
      confirmLoading={loading}
      destroyOnClose
    >
      <div style={{ display: 'block', flexWrap: 'wrap' }}>
        <div style={{ flex: 1 }}>
          <Form form={form} style={{ width: '100%' }} layout="vertical">
            {isRedLine === '1' && (
              <Form.Item style={{ width: '50%' }} name="tenantNo" label="生效对象：" rules={requiredRules}>
                <Select
                  placeholder="请选择生效对象"
                  options={robotList}
                  fieldNames={{ label: 'botName', value: 'botNo' }}
                />
              </Form.Item>
            )}
            <Form.Item name="textContent" label="测试文本：">
              <Input.TextArea placeholder="请输入" maxLength={4000} showCount style={{ height: '200px' }} />
            </Form.Item>
          </Form>
        </div>
        <div style={{ flex: 1, flexWrap: 'wrap', marginTop: 10 }}>
          <span style={{ display: 'flex' }}>
            结果：
            <Tooltip
              title={
                <>
                  结果：
                  <br />
                  展示针对以上测试文本是否命中关键词规则，为提升结果名字的准确性，文本内容输入格式应该为
                  <br />
                  客户：
                  <br />
                  坐席：
                </>
              }
            >
              <QuestionCircleOutlined style={{ fontSize: 16, color: '#BABEC4', marginLeft: 4, cursor: 'pointer' }} />
            </Tooltip>
          </span>
          {isShow && (
            <span style={{ display: 'flex', marginTop: 5 }}>
              {ishitData === null ? '' : ishitData ? <span style={{ color: 'red' }}>命中</span> : <span>未命中</span>}
            </span>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default TextContentTestModal;
