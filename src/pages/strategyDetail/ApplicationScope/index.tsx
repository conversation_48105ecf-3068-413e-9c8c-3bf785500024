import useRobotData from '@/hooks/useRobotData';
import { requiredRules } from '@/utils/constants';
import { Col, Form, FormInstance, Row, Select } from 'antd';
import React, { FC } from 'react';
import styles from './index.scss';

const { Item } = Form;

interface IProps {
  form: FormInstance;
  disabled?: boolean;
}

const ApplicationScope: FC<IProps> = ({ form, disabled }) => {
  const { applicationList } = useRobotData();

  return (
    <div className={styles.applicationScope}>
      <div className={styles.applicationScopeTitle}>应用范围</div>
      <Form labelAlign="left" className={styles.form} form={form} disabled={disabled}>
        <Row gutter={24}>
          <Col span={16}>
            <Item rules={requiredRules} label="生效对象" name="tenantNos">
              <Select
                placeholder="请选择生效对象"
                mode="multiple"
                maxTagCount="responsive"
                options={applicationList}
                fieldNames={{ label: 'botName', value: 'botNo' }}
              />
            </Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default ApplicationScope;
