import SubTitle from '@/components/SubTitle';
import { drawerWidth } from '@/utils/constants';
import { Drawer, DrawerProps } from 'antd';
import { isEmpty } from 'lodash';
import React, { FC } from 'react';
import '../../latex.scss';
import StrategyDetail from '../strategyDetail';

interface IProps extends DrawerProps {
  currentDetail: any;
  setCurrentDetail: any;
  refresh: () => void;
  isTemplate?: boolean;
  isRedLine?: string;
  isSubTitle?: boolean;
  onlyRead?: boolean;
}

const StrategyDetailDrawer: FC<IProps> = ({
  currentDetail,
  setCurrentDetail,
  refresh,
  isTemplate,
  isRedLine,
  isSubTitle,
  title,
  onlyRead,
  ...extraProps
}) => {
  return (
    <Drawer
      maskClosable={false}
      title={title || (currentDetail.id ? '编辑策略' : '新建策略')}
      rootClassName="strategy-detail-drawer"
      width={drawerWidth}
      open={!isEmpty(currentDetail)}
      onClose={() => {
        setCurrentDetail({});
      }}
      {...extraProps}
    >
      {isSubTitle && (
        <SubTitle title={title || (currentDetail.id ? '编辑策略' : '新建策略')} goBack={() => setCurrentDetail({})} />
      )}
      {!isEmpty(currentDetail) && (
        <StrategyDetail
          refresh={refresh}
          closeDrawer={() => setCurrentDetail({})}
          currentDetail={currentDetail}
          isTemplate={isTemplate}
          isRedLine={isRedLine}
          onlyRead={onlyRead}
        />
      )}
    </Drawer>
  );
};

export default StrategyDetailDrawer;
