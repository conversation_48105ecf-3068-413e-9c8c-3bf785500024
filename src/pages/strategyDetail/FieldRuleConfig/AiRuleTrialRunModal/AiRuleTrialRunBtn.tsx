import { <PERSON><PERSON>, Too<PERSON><PERSON> } from 'antd';
import React, { FC, useState } from 'react';
import BatchTestModal from '../BatchTestModal';
import AiRuleTrialRunModal from './index';

const AiRuleTrialRunBtn: FC<any> = ({
  isTemplate,
  form,
  ruleId,
  tenantNo,
  tenantNos,
  botNo,
  strategyId,
  parentForm,
  parentRowKey,
  isRedLine,
  isCE,
  strategyName,
  style,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [pagination, setPagination] = useState<{ current: number; pageSize: number }>(() => {
    return {
      current: 1,
      pageSize: 10,
    };
  });
  const [testInfo, setTestInfo] = useState<any>({});
  const [currentTest, setCurrentTest] = useState<any>({});
  const [batchTestModalVisible, setBatchTestModalVisible] = useState(false);
  const [testTenantNo, setTestTenantNo] = useState();

  const refresh = () => {
    setPagination({
      current: 1,
      pageSize: pagination.pageSize,
    });
  };

  const handleClickAiTest = async () => {
    if (isTemplate) {
      const fields = await form?.validateFields();
      const info = {
        aiFieldRuleList: [{ ...fields }],
      };
      setTestInfo(info);
    } else {
      const [res, values] = await Promise.all([
        parentForm?.validateFields?.([['fields', parentRowKey, 'skillNo']]),
        form?.validateFields(),
      ]);
      const fields = {
        skillNo:
          //@ts-ignore
          Object.values(res?.fields || [])?.filter((v) => !!v)?.[0]?.skillNo || values?.fields?.[parentRowKey]?.skillNo,
        botNo,
        aiFieldRuleList: [{ ...(values || {}) }],
      };
      setTestInfo(fields);
    }
    setModalVisible(true);
  };

  return (
    <>
      <Tooltip title={!ruleId ? '请先发布策略后再进行规则调试' : ''}>
        <Button
          style={{
            marginTop: isTemplate ? 24 : 16,
            fontWeight: 500,
            marginLeft: !isTemplate ? 0 : 134,
            ...(style || {}),
          }}
          onClick={handleClickAiTest}
          type="primary"
          disabled={!ruleId}
          ghost={!!ruleId}
        >
          规则试运行
        </Button>
      </Tooltip>
      <AiRuleTrialRunModal
        tenantNo={tenantNo}
        ruleId={ruleId}
        isTemplate={isTemplate}
        modalVisible={modalVisible}
        setPagination={setPagination}
        pagination={pagination}
        testInfo={testInfo}
        setCurrentTest={setCurrentTest}
        setBatchTestModalVisible={setBatchTestModalVisible}
        setModalVisible={setModalVisible}
        refresh={refresh}
        tenantNos={tenantNos}
        isRedLine={isRedLine}
        setTestTenantNo={setTestTenantNo}
        isCE={isCE}
        strategyName={strategyName}
      />
      <BatchTestModal
        form={form}
        currentTest={currentTest}
        strategyId={strategyId}
        ruleId={ruleId}
        tenantNo={tenantNo}
        open={batchTestModalVisible}
        testTenantNo={testTenantNo}
        refresh={refresh}
        handleVisibleChange={(value: boolean) => {
          if (!value) {
            setCurrentTest({});
          }
          setBatchTestModalVisible(value);
        }}
        isCE={isCE}
        strategyName={strategyName}
        testInfo={testInfo}
      />
    </>
  );
};

export default AiRuleTrialRunBtn;
