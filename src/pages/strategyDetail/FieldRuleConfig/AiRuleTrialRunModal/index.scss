.result {
  display: flex;
  margin: 10px 32px 0;

  .resultTitle {
    width: 69.11px;
    text-align: right;
    white-space: nowrap;
  }
  .resultContent {
    margin-left: 12px;
    flex: 1;
  }
  .batchTable {
  }
}

.modalBox {
  :global {
    .ant-modal-wrap .ant-modal {
      .ant-modal-header {
        margin-bottom: 24px;
      }
      .ant-modal-body {
        padding: 0;
      }
      .ant-modal-footer {
        margin-top: 24px;
      }
    }
  }
}

.modalTabs {
  :global {
    .ant-tabs-nav {
      margin: 0 32px 24px !important;
      &::before {
        display: none;
      }
    }
    .ant-form {
      margin: 0 32px;
    }
    .ant-tabs-nav-list {
      background: #eff1f4;
      border-radius: 6px;
      font-size: 14px;
      padding: 4px;
      .ant-tabs-tab {
        padding: 8px 16px;
        margin: 0;
        font-weight: normal;
        color: #4e5969;
        .ant-tabs-tab-btn {
          text-shadow: none !important;
        }
        &:not(:first-child) {
          margin-left: 8px;
        }
        &.ant-tabs-tab-active {
          background: #fff;
          color: var(--primary-color);
          box-shadow: 0px 1px 2px 0px #d0d0d0;
          border-radius: 4px;
          font-weight: 500;
        }
      }
      .ant-tabs-ink-bar {
        display: none;
      }
    }
    .ant-tabs-content {
      max-height: 60vh;
      overflow-y: auto;
    }
  }
}
