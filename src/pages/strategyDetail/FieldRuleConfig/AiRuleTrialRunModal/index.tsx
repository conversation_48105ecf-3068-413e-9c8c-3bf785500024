import Table from '@/components/Table';
import useRobotData from '@/hooks/useRobotData';
import { deleteRuleTest, getRuleTestList, testAiRule, testBotRule } from '@/services/ce';
import { requiredRules } from '@/utils/constants';
import { Button, Form, Input, Modal, Popconfirm, Row, Select, Tabs, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.scss';

const AiRuleTrialRunModal: FC<any> = ({
  tenantNo,
  ruleId,
  isTemplate,
  modalVisible,
  setPagination,
  pagination,
  testInfo,
  setCurrentTest,
  setBatchTestModalVisible,
  setModalVisible,
  refresh,
  tenantNos,
  isRedLine,
  setTestTenantNo,
  isCE,
  strategyName,
}) => {
  const [testForm] = Form.useForm();
  const [hasResult, setHasResult] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [testResult, setTestResult] = useState<any>([]);
  const [list, setList] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const { applicationList } = useRobotData();
  const currentTenantNo = Form.useWatch(['tenantNo'], testForm);

  const robotList = useMemo(() => {
    return applicationList?.filter((v: any) => tenantNos?.includes(v.botNo));
  }, [applicationList, tenantNos]);

  const columns = [
    {
      dataIndex: 'ruleName',
      key: 'ruleName',
      title: '规则名称',
    },
    {
      dataIndex: 'ruleDesc',
      key: 'ruleDesc',
      title: '规则描述',
      width: 500,
      render: (text: any) => (
        <Tooltip destroyTooltipOnHide title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      dataIndex: 'gmtCreated',
      key: 'gmtCreated',
      title: '测试时间',
      render: (text: any) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      dataIndex: 'endTime',
      key: 'endTime',
      title: '测试结束时间',
      render: (text: any) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      dataIndex: 'totalCount',
      key: 'totalCount',
      title: '测试数量',
    },
    {
      dataIndex: 'creator',
      key: 'creator',
      title: '测试人',
    },
    {
      dataIndex: 'statusName',
      key: 'statusName',
      title: '状态',
    },
    {
      dataIndex: 'accuracy',
      key: 'accuracy',
      title: '正确率',
    },
    {
      dataIndex: 'actions',
      key: 'actions',
      title: '操作',
      fixed: 'right',
      width: 150,
      render: (_: any, record: any) => {
        return (
          <div style={{ display: 'flex' }}>
            <Button
              type="link"
              onClick={() => {
                setCurrentTest(record);
                setBatchTestModalVisible(true);
              }}
              style={{ marginRight: 20, padding: 0 }}
            >
              查看
            </Button>
            <Popconfirm
              onConfirm={async () => {
                // @ts-ignore
                await deleteRuleTest({
                  id: record.id,
                  tenantNo,
                });
                const obj = { ...pagination };
                if (total - 1 > 0 && (total - 1) % obj.pageSize === 0) {
                  obj.pageSize = obj.pageSize - 1;
                }
                setPagination(obj);
              }}
              title="确认删除吗"
            >
              <Button type="link" danger style={{ padding: 0 }}>
                删除
              </Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const handleTestRule = async () => {
    const { text, tenantNo: _tenantNo } = await testForm.validateFields();
    let res;
    try {
      setTestLoading(true);
      if (isCE) {
        let ruleNames: string[] = [];
        testInfo?.aiFieldRuleList?.forEach((item: any) => {
          const list = item?.fields?.find((v: any) => v.skillNo == testInfo?.skillNo)?.ruleData;
          ruleNames = list?.map((v: any) => v?.ruleName);
        });
        res = await testBotRule({
          skillNo: testInfo?.skillNo,
          ruleNames,
          strategyName,
          textContent: text,
          tenantNo: _tenantNo || tenantNo,
        });
      } else {
        const params = {
          ...testInfo,
          textContent: text,
          botNo: _tenantNo || testInfo.botNo,
          tenantNo: _tenantNo || tenantNo,
        };
        res = await testAiRule(params);
      }
      setTestResult(res.data.value);
    } finally {
      setTestLoading(false);
      setHasResult(true);
    }
  };

  const getOriginLine = (lines: any, text: string) => {
    return lines?.map((line: number, index: number) => {
      const arr = text?.split('\n');
      return <div key={index}>{arr?.[line - 1]}</div>;
    });
  };

  const renderTestRule = () => {
    return (
      <>
        <Form form={testForm}>
          {isRedLine === '1' && (
            <Form.Item style={{ width: '50%' }} label="生效对象" name={'tenantNo'} rules={requiredRules}>
              <Select
                placeholder="请选择生效对象"
                options={robotList}
                fieldNames={{ label: 'botName', value: 'botNo' }}
              />
            </Form.Item>
          )}
          <Form.Item label="测试文本" name={'text'} rules={requiredRules}>
            <Input.TextArea style={{ height: 200 }} showCount maxLength={20000} placeholder="请输入测试文本" />
          </Form.Item>
        </Form>
        {!isEmpty(testResult) &&
          testResult.map((result: any, index: number) => {
            return (
              <div className={styles.result} key={index}>
                <div className={styles.resultTitle}>结果：</div>
                <div className={styles.resultContent}>
                  <div>是否命中：{result.isHit}</div>
                  {Array.isArray(result.lines) && result.lines.length > 0 && (
                    <div style={{ display: 'flex' }}>
                      命中原文：
                      <div>{getOriginLine(result.lines, testForm.getFieldValue('text'))}</div>
                    </div>
                  )}
                  <div>原因分析：{result.recordAnalyze}</div>
                </div>
              </div>
            );
          })}
        {isEmpty(testResult) && hasResult && <div className={styles.result}>无违规</div>}
        <div className="ant-modal-footer">
          <Button onClick={() => setModalVisible(false)}>取消</Button>
          <Button loading={testLoading} onClick={handleTestRule} style={{ marginLeft: 8 }} type="primary">
            提交
          </Button>
        </div>
      </>
    );
  };

  const renderBatchTestRule = () => {
    return (
      <>
        {isRedLine === '1' && (
          <Form form={testForm}>
            <Form.Item
              style={{ width: '50%', marginBottom: 0 }}
              label="生效对象"
              name={'tenantNo'}
              rules={requiredRules}
            >
              <Select
                placeholder="请选择生效对象"
                options={robotList}
                fieldNames={{ label: 'botName', value: 'botNo' }}
              />
            </Form.Item>
          </Form>
        )}
        <div style={{ padding: '0 32px' }}>
          <Row style={{ display: 'flex', justifyContent: 'right', marginBottom: 16 }}>
            <Button onClick={refresh} style={{ marginRight: 8 }}>
              刷新
            </Button>
            <Button
              type="primary"
              style={{ marginRight: 8 }}
              onClick={async () => {
                if (isRedLine === '1') {
                  const values = await testForm.validateFields(['tenantNo']);
                  setTestTenantNo(values.tenantNo);
                }
                setBatchTestModalVisible(true);
              }}
            >
              新增批量测试
            </Button>
          </Row>
          <Table
            style={{ marginBottom: 24 }}
            pagination={{
              total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            }}
            onChange={(obj) => {
              setPagination({
                current: (obj.pageSize !== pagination.pageSize ? 1 : obj.current) || 1,
                pageSize: obj.pageSize || 10,
              });
            }}
            className={styles.batchTable}
            dataSource={list}
            columns={columns as any}
          />
        </div>
      </>
    );
  };

  const tabsItems: any[] = [
    {
      key: '1',
      label: '规则调试',
      children: renderTestRule(),
    },
  ];
  if (!isTemplate) {
    tabsItems.push({
      key: '2',
      label: (
        <Tooltip title={!ruleId ? '请先提交发布后再进行批量测试，建议先将策略置于禁用状态，待调试通过后再启用' : ''}>
          批量测试
        </Tooltip>
      ),
      children: renderBatchTestRule(),
      disabled: !ruleId,
    });
  }

  const getList = useCallback(async (pagination: any, ruleId: any, tenantNo: any) => {
    const { current, pageSize } = pagination;
    const res = await getRuleTestList({
      pageSize,
      pageNum: current,
      ruleId,
      tenantNo,
    });
    setTotal(res.data.value.total);
    setList(res.data.value.list);
  }, []);

  useEffect(() => {
    if (modalVisible) {
      const tNo = isRedLine === '1' ? currentTenantNo : tenantNo;
      if (tNo) {
        getList(pagination, ruleId, tNo);
      } else {
        setTotal(0);
        setList([]);
      }
    }
  }, [pagination, getList, modalVisible, ruleId, tenantNo, currentTenantNo]);

  useEffect(() => {
    if (!modalVisible) {
      setTestResult([]);
      setHasResult(false);
      testForm.resetFields();
    }
  }, [modalVisible]);

  return (
    <Modal
      onCancel={() => setModalVisible(false)}
      width={'70vw'}
      footer={null}
      open={modalVisible}
      title={isCE ? '质检技能试运行' : 'AI规则试运行'}
      rootClassName={styles.modalBox}
    >
      <Tabs className={styles.modalTabs} items={tabsItems}></Tabs>
    </Modal>
  );
};

export default AiRuleTrialRunModal;
