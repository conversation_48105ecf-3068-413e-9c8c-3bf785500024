import Table from '@/components/Table';
import { PREFIX } from '@/services';
import { batchImport, botBatchImport, getRuleTestDetail, reTestRule } from '@/services/ce';
import { copy, download } from '@/utils';
import { getResultRich, STATIC_URL } from '@/utils/constants';
import { CloudUploadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Col, message, Modal, Row, Steps, Tooltip, Upload } from 'antd';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.scss';

const { Dragger } = Upload;

const BatchTestModal = (props: any) => {
  const {
    open,
    handleVisibleChange,
    strategyId,
    ruleId,
    tenantNo: _tenantNo,
    currentTest = {},
    refresh,
    form,
    testTenantNo,
    isCE,
    strategyName,
    testInfo,
  } = props;
  const [currentStep, setCurrentStep] = useState(0);

  const tenantNo = useMemo(() => testTenantNo || _tenantNo, [testTenantNo, _tenantNo]);

  const columns = useMemo(() => {
    return [
      {
        dataIndex: 'inOriginal',
        key: 'inOriginal',
        title: '输入-文本内容',
        width: 300,
        render: (text: any) => (
          <Tooltip
            overlayStyle={{ maxWidth: 700 }}
            title={<div style={{ maxHeight: 500, overflowY: 'auto' }} dangerouslySetInnerHTML={{ __html: text }}></div>}
          >
            <span onClick={() => copy(text)}>{text}</span>
          </Tooltip>
        ),
      },
      {
        dataIndex: 'isHit',
        key: 'isHit',
        title: '输出-是否命中',
      },
      {
        dataIndex: 'outOriginal',
        key: 'outOriginal',
        title: '输出-原文',
      },
      {
        dataIndex: 'recordAnalyze',
        key: 'recordAnalyze',
        title: '输出-原因分析',
        render: (text: any) => (
          <Tooltip
            overlayStyle={{ maxWidth: 700 }}
            title={<div style={{ maxHeight: 500, overflowY: 'auto' }} dangerouslySetInnerHTML={{ __html: text }}></div>}
          >
            <span onClick={() => copy(text)}>{text}</span>
          </Tooltip>
        ),
      },
      {
        dataIndex: 'expectedResult',
        key: 'expectedResult',
        title: '预期结果',
      },
      {
        dataIndex: 'isRecall',
        key: 'isRecall',
        title: '是否召回',
      },
      {
        dataIndex: 'isRight',
        key: 'isRight',
        title: '是否准确',
      },
    ];
  }, []);
  const [list, setList] = useState([]);
  const [file, setFile] = useState<any>({});
  const [fileUrl, setFileUrl] = useState<any>({});
  const [importLoading, setImportLoading] = useState(false);
  const [detail, setDetail] = useState<any>({});
  useEffect(() => {
    if (!open) {
      setList([]);
      setFile({});
      setFileUrl('');
      setDetail({});
    }
  }, [open]);

  useEffect(() => {
    if (!isEmpty(currentTest)) {
      setCurrentStep(1);
      getRuleTestDetail(currentTest.batchNo).then((res) => {
        setDetail(res.data.value);
      });
    } else {
      setCurrentStep(0);
    }
  }, [currentTest]);

  const onChange = (value: number) => {
    setCurrentStep(value);
  };

  useEffect(() => {
    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [fileUrl]);
  const handleBatchTest = async () => {
    if (isEmpty(file)) {
      message.warning('文件不可为空');
      return;
    }
    const formData = new FormData();
    formData.append('file', file);
    formData.append('strategyId', strategyId);
    formData.append('ruleId', ruleId);
    formData.append('tenantNo', tenantNo);
    setImportLoading(true);
    try {
      if (isCE) {
        let ruleNames: string[] = [];
        testInfo?.aiFieldRuleList?.forEach((item: any) => {
          const list = item?.fields?.find((v: any) => v.skillNo == testInfo?.skillNo)?.ruleData;
          ruleNames = list?.map((v: any) => v?.ruleName);
        });
        //@ts-ignore
        formData.append('ruleNames', ruleNames);
        formData.append('robotNo', tenantNo);
        formData.append('skillNo', testInfo?.skillNo);
        formData.append('strategyName', strategyName);
        await botBatchImport(formData);
      } else {
        const fields = await form.validateFields();
        formData.append('ruleName', fields.aiRuleName);
        formData.append('ruleDesc', fields.aiRuleDesc);
        await batchImport(formData);
      }
      handleVisibleChange(false);
      refresh();
    } catch {
      message.error('规则名称或规则描述不能为空');
    } finally {
      setImportLoading(false);
    }
  };

  const reTest = () => {
    reTestRule({
      batchNo: currentTest.batchNo,
      strategyId,
      ruleId,
      tenantNo,
    }).then(() => {
      handleVisibleChange(false);
      refresh();
    });
  };

  const exportResult = () => {
    download(
      `${PREFIX}/api/v1/ruleTest/download`,
      {
        batchNo: currentTest.batchNo,
      },
      `${detail.ruleName}-运行结果.xlsx`
    );
  };

  const renderStep0 = () => {
    const uploadProps = {
      name: 'file',
      multiple: false,
      action: '',
      beforeUpload: (file: any) => {
        return false;
      },
      onChange(info: any) {
        const file = info.file;
        setFile(file);
        setFileUrl(URL.createObjectURL(file));
      },
      onDrop(e: any) {},
    };
    return (
      <div style={{ marginLeft: 16 }}>
        <h3>说明</h3>
        <div>批量测试功能可批量导入需要测试的数据集，从而测试当前规则的输出结果</div>
        <div>1. 输出是否命中，并与测试集的预期结果进行对比，从而得出该批次的正确率</div>
        <div>2. 输出原文和原因分析，辅助判断AI结果的正确性</div>
        <h3>导入模版</h3>
        <a href={`${STATIC_URL}/website/cs/ai-puma/template/batch-template.xlsx`}>下载模版</a>
        <h3>开始导入</h3>
        <Dragger {...uploadProps} fileList={[]}>
          <p className="ant-upload-text">
            <CloudUploadOutlined style={{ marginRight: 8 }} />
            将文档拖拽到此处，或 <a>本地上传</a>
          </p>
          <p className="ant-upload-hint">请使用模版格式，每次支持单个文档上传</p>
        </Dragger>
        <a href={fileUrl} download={file.name}>
          {file.name}
        </a>
        <div className="ant-modal-footer" style={{ padding: 0 }}>
          <Button onClick={() => handleVisibleChange(false)}>取消</Button>
          <Button loading={importLoading} onClick={handleBatchTest} style={{ marginLeft: 8 }} type="primary">
            提交
          </Button>
        </div>
      </div>
    );
  };

  const resultDesc = useMemo(() => {
    return getResultRich();
  }, []);

  const renderStep1 = () => {
    return (
      <div className={styles.step1}>
        <h3>
          查看结果
          <Tooltip
            rootClassName={styles.resultDesc}
            title={<div dangerouslySetInnerHTML={{ __html: resultDesc }}></div>}
          >
            <QuestionCircleOutlined style={{ cursor: 'pointer', marginLeft: 8 }} />
          </Tooltip>
        </h3>
        <Row>
          <Col span={8}>
            <div className={styles.colRow}>
              <h4>规则：</h4>
              <span>{detail.ruleName}</span>
            </div>
          </Col>
          <Col span={8}>
            <div className={styles.colRow}>
              <h4>测试时间：</h4>
              <span>{detail.startTime ? dayjs(detail.startTime).format('YYYY-MM-DD HH:mm:ss') : ''}</span>
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <div className={styles.colRow}>
              <h4>召回率：</h4>
              <span>{detail.recallRate}</span>
            </div>
          </Col>
          <Col span={8}>
            <div className={styles.colRow}>
              <h4>准确率：</h4>
              <span>{detail.accuracy}</span>
            </div>
          </Col>
          <Col span={8} className={styles.colRow}>
            <Button type="link" disabled={isEmpty(detail) || detail.status === 1} onClick={reTest}>
              再次生成
            </Button>
            <Button type="link" disabled={isEmpty(detail) || detail.status === 1} onClick={exportResult}>
              导出
            </Button>
          </Col>
        </Row>
        <Table
          style={{ marginBottom: 24 }}
          columns={columns}
          dataSource={detail.ruleTestResultDTOList || []}
          pagination={false}
        />
      </div>
    );
  };
  return (
    <Modal
      width={'70vw'}
      title={isCE ? '质检技能规则试运行' : 'AI规则试运行'}
      footer={null}
      open={open}
      onCancel={() => handleVisibleChange(false)}
    >
      <div className={styles.container}>
        <div className={styles.left}>
          <Steps
            size="small"
            current={currentStep}
            onChange={onChange}
            direction="vertical"
            items={[
              {
                title: '开始导入',
              },
              {
                title: '查看结果',
              },
            ]}
          />
        </div>
        <div className={styles.content}>
          {currentStep === 0 && renderStep0()}
          {currentStep === 1 && renderStep1()}
        </div>
      </div>
    </Modal>
  );
};

export default BatchTestModal;
