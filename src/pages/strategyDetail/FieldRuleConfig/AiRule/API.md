/api/v1/ruleTest/batchImport
导入测试数据 
POST   form-data

file 
strategyId
ruleId
tenantNo


/api/v1/ruleTest/reTest
重新生成测试
POST
{
    "strategyId":134,
    "batchNo":"b88e6432-927f-4312-805b-c2edda005f68",
    "ruleId":225,
    "tenantNo":"20231020161621507"
}

导出测试结果
/api/v1/ruleTest/download
POST
{
    "batchNo":"b88e6432-927f-4312-805b-c2edda005f68"
}

/api/v1/ruleTest/detail

结果详情
GET
?batchNo=28b70510-0d45-4b56-a221-0cda360008ef

返回参数
{
    "value": [
        {
            "sessionId": "sip-3-05.4380648.520",
            "expectedResult": "命中",
            "inOriginal": "0|客户|hi，你好，我是欢迎语\n\n1|客服|举报\n2|客服|[大哭]\n3|客服|[大哭]\n4|客服|投诉\n5|客服|垃圾\n6|客服|[大哭]\n7|客服|[大哭]\n8|客服|骗保\n9|客服|市民热线\n10|客服|银保监\n11|客户|[呲牙]\n12|客户|[呲牙]\n13|客户|[呲牙]\n14|客户|[呲牙]\n15|客户|[呲牙]\n16|客户|[呲牙]\n17|客户|[呲牙]\n18|客户|滚\n19|客户|请评价本次人工服务\n20|客户|邀请客户评价\n21|客户|\n",
            "tenantNo": "20231020161621507",
            "outOriginal": "[\"5|3|客服|[大哭]\"]",
            "recordAnalyze": "垃圾",
            "isHit": "命中",
            "isRecall": "是",
            "isRight": "是"
        }
    ],
    "success": true,
    "errorCode": null,
    "errorMsg": null,
    "extraInfo": null
}


分页查询任务
/api/v1/ruleTest/page
POST
{
    "tenantNo":"20231020161621507",
    "ruleId":225,
    "pageNum":1,
    "pageSize":10
}

返回数据
{
    "value": {
        "total": 0,
        "list": [
            {
                "id": 7,
                "strategyId": 134,
                "batchNo": "28b70510-0d45-4b56-a221-0cda360008ef",
                "ruleName": "辱骂客户",
                "ruleId": 225,
                "taskId": null,
                "status": 2,
                "ruleDesc": "客服存在明确辱骂、争吵、不文明用语、恶意言辞、威胁或恐吓的行为",
                "accuracy": "100.00%",
                "recallRate": "100.00%",
                "tenantNo": "20231020161621507",
                "isDeleted": "N",
                "creator": "system",
                "gmtCreated": "2024-04-23T02:08:18.000+00:00",
                "modifier": "system",
                "gmtModified": "2024-04-23T02:08:27.000+00:00",
                "startTime": null,
                "endTime": null
            },
            {
                "id": 6,
                "strategyId": 134,
                "batchNo": "b88e6432-927f-4312-805b-c2edda005f68",
                "ruleName": "辱骂客户",
                "ruleId": 225,
                "taskId": null,
                "status": 2,
                "ruleDesc": "客服存在明确辱骂、争吵、不文明用语、恶意言辞、威胁或恐吓的行为",
                "accuracy": "100.00%",
                "recallRate": "100.00%",
                "tenantNo": "20231020161621507",
                "isDeleted": "N",
                "creator": "system",
                "gmtCreated": "2024-04-22T13:41:35.000+00:00",
                "modifier": "system",
                "gmtModified": "2024-04-23T02:08:27.000+00:00",
                "startTime": null,
                "endTime": null
            }
        ],
        "pageNum": 0,
        "pageSize": 0,
        "size": 0,
        "startRow": 0,
        "endRow": 0,
        "pages": 0,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": false,
        "isLastPage": false,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 0,
        "navigatepageNums": null,
        "navigateFirstPage": 0,
        "navigateLastPage": 0
    },
    "success": true,
    "errorCode": null,
    "errorMsg": null,
    "extraInfo": null
}

删除任务
/api/v1/ruleTest/delete
POST

{
    "tenantNo":"20231020161621507",
    "id":6
}