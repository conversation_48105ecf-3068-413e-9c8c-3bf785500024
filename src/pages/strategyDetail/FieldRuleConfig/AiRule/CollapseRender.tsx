import Collapse from '@/components/Collapse';
import { Form, InputNumber, Select } from 'antd';
import classNames from 'classnames';
import React, { FC } from 'react';
import ExtraDown from '../../../../assets/extraDown.svg';
import ExtraUp from '../../../../assets/extraUp.svg';
import styles from './index.scss';

interface IProps {
  labelList: any[];
  riskList: any[];
  className?: string;
  riskFormName?: string;
  isRedLineRobot?: boolean;
}

const CollapseRender: FC<IProps> = ({ labelList, riskList, className, riskFormName, isRedLineRobot }) => {
  return (
    <Collapse
      className={className}
      activeLabel={
        <div className={styles.extraHeader}>
          <img src={ExtraDown} />
          <span>高级配置</span>
        </div>
      }
      unActiveLabel={
        <div className={styles.extraHeader}>
          <img src={ExtraUp} />
          <span>收起</span>
        </div>
      }
      children={
        <>
          <Form.Item label="风险等级" name={riskFormName || 'riskLevel'}>
            <Select
              allowClear
              options={riskList}
              placeholder="选择风险等级"
              style={{ marginLeft: 64, width: 'calc(100% - 64px)' }}
              optionRender={(option) => (
                <span className={classNames({ red: option?.data?.isRedLine === '1' && !isRedLineRobot })}>
                  {option?.data?.label}
                </span>
              )}
            />
          </Form.Item>
          <Form.Item label="评分" name={'score'}>
            <InputNumber placeholder="请输入评分" style={{ marginLeft: 92, width: 'calc(100% - 92px)' }} />
          </Form.Item>
          <Form.Item label="标签" name={'label'}>
            <Select
              allowClear
              options={labelList}
              mode="multiple"
              placeholder="请选择标签"
              style={{ marginLeft: 92, width: 'calc(100% - 92px)' }}
              optionRender={(option) => (
                <span className={classNames({ red: option?.data?.isRedLine === '1' && !isRedLineRobot })}>
                  {option?.data?.label}
                </span>
              )}
            />
          </Form.Item>
        </>
      }
    />
  );
};

export default CollapseRender;
