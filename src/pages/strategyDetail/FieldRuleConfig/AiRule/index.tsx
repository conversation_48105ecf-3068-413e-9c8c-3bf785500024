import { requiredRules } from '@/utils/constants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Divider, Form, Input, Row, Tooltip } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useImperativeHandle } from 'react';
import AiRuleTrialRunBtn from '../AiRuleTrialRunModal/AiRuleTrialRunBtn';
import CollapseRender from './CollapseRender';
import styles from './index.scss';

const AiRule = React.forwardRef((props: any, ref) => {
  const {
    parentRowKey,
    parentForm,
    tenantNo,
    botNo,
    fields,
    ruleId,
    strategyId,
    labelList,
    riskList,
    parentField,
    isTemplate,
    tenantNos,
    isRedLine,
    onlyRead,
  } = props;
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => {
    return {
      form,
    };
  });

  useEffect(() => {
    if (!isEmpty(fields)) {
      form.setFieldValue('ruleName', parentField?.ruleName || fields?.ruleName || fields?.aiRuleName);
      form.setFieldValue('aiRuleName', fields.aiRuleName);
      form.setFieldValue('aiRuleDesc', fields.aiRuleDesc);
    }
  }, [fields, parentField?.ruleName]);

  useEffect(() => {
    if (parentField) {
      form.setFieldValue('riskLevel', parentField.riskLevel || undefined);
      form.setFieldValue('score', parentField.score);
      form.setFieldValue(
        'label',
        (parentField.label || '')
          .split(',')
          .filter(Boolean)
          .map((item: any) => Number(item))
      );
    }
  }, [parentField]);

  return (
    <>
      <Form style={{ marginBottom: 0, marginTop: 16 }} form={form} disabled={onlyRead}>
        <Form.Item label="规则名称" name={'ruleName'} rules={requiredRules} className={styles.ruleFormItem}>
          <Input placeholder="请输入规则名称" />
        </Form.Item>
        <Form.Item label="AI处理名称" name={'aiRuleName'} className={styles.ruleAiFormItem}>
          <Input placeholder="请输入AI处理名称" />
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 0 }}
          label={
            <>
              规则描述
              <Tooltip
                title={
                  <>
                    直接输入想要判定内容的规则描述，例如：
                    <br />
                    泄露信息：客户未提供身份信息，坐席主动报出客户完整证件号，视为违规。
                  </>
                }
              >
                <QuestionCircleOutlined style={{ fontSize: 16, color: '#BABEC4', marginLeft: 4, cursor: 'pointer' }} />
              </Tooltip>
            </>
          }
          name={'aiRuleDesc'}
          rules={[{ required: true, message: '请填写规则描述' }]}
        >
          <Input.TextArea
            autoSize={{ minRows: 3, maxRows: 30 }}
            style={{ marginLeft: 33, width: 'calc(100% - 33px)' }}
            showCount
            maxLength={4000}
            placeholder="请输入规则描述"
          />
        </Form.Item>
        {!onlyRead && (
          <>
            {!isTemplate && (
              <>
                <CollapseRender labelList={labelList} riskList={riskList} />
                <Divider style={{ margin: 0, borderColor: '#E4E5E6', transform: 'scaleY(0.5)' }} />
              </>
            )}
            <Row justify={'end'}>
              <AiRuleTrialRunBtn
                isTemplate={isTemplate}
                form={form}
                ruleId={ruleId}
                tenantNo={tenantNo}
                tenantNos={tenantNos}
                botNo={botNo}
                strategyId={strategyId}
                parentForm={parentForm}
                parentRowKey={parentRowKey}
                isRedLine={isRedLine}
              />
            </Row>
          </>
        )}
      </Form>
    </>
  );
});

export default AiRule;
