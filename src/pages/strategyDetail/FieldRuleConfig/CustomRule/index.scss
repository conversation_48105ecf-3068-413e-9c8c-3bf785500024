.container {
  .ruleItem {
    background-color: #fff;
    position: relative;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .deleteAction {
    cursor: pointer;
    position: absolute;
    right: 4px;
    color: #4e5969;
    top: 10px;
  }
  .groupOperatorTrigger {
    margin: 0 auto;
    display: flex;
    align-items: center;
    width: fit-content;
    justify-content: center;
    padding: 4px 12px 4px 12px;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background-color: #f2f2fe;
    border-radius: 100px;
    cursor: pointer;
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .letter {
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    text-align: center;
    line-height: 20px;
    border-radius: 2px;
    color: #fff;
    margin-right: 12px;
    margin-top: 6px;
  }
  .layout {
    width: 100%;
    display: flex;

    .relation {
      width: 28px;
      margin-right: 12px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      .line {
        width: 2px;
        background-color: rgba(223, 223, 252, 1);
        top: 0;
        bottom: 0;
        position: absolute;
      }
      .relationText {
        cursor: pointer;
        position: relative;
        z-index: 1;
        height: 28px;
        width: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        color: var(--primary-color);
        background-color: #e7e7fd;
        font-weight: 600;
        line-height: 28px;
      }
    }
    .formLayout {
      width: calc(100% - 40px);
    }
  }

  .corss {
    background: #f2f2fe;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: relative;
    &::before,
    &::after {
      content: '';
      position: absolute;
      background-color: var(--primary-color);
      border-radius: 4px;
      transform: translate(-50%, -50%);
      top: 50%;
      left: 50%;
    }
    &::before {
      width: 1.5px;
      height: 10px;
    }

    &::after {
      width: 10px;
      height: 1.5px;
    }
  }
}
