import { numberToLetter, selectSearch } from '@/utils';
import { ECondition, EFieldsType } from '@/utils/constants';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Col, DatePicker, Divider, Form, Input, InputNumber, Row, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import TextContentTestModal from '../../TextContentTestModal';
import styles from './index.scss';

const CustomRule = React.forwardRef((props: any, ref) => {
  const { fields, fieldsList, operator, operatorMap, onOperatorChange, tenantNo, tenantNos, isRedLine, onlyRead } =
    props;
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [currentTest, setCurrentTest] = useState<any>({});
  const [ruleDefineList, setRuleDefineList] = useState<any[]>([]);
  const rowKey = useRef(0);
  const emptyInitRef = useRef(false);

  const getFields = () => {
    const fields = ruleDefineList.map((field) => {
      const rowKey = field.rowKey;
      const fieldCode = form.getFieldValue(['fields', rowKey, 'fieldCode']);
      const fieldOperator = form.getFieldValue(['fields', rowKey, 'fieldOperator']);
      let fieldValue = form.getFieldValue(['fields', rowKey, 'fieldValue']);
      const fieldType = field.fieldType;
      if (fieldType === EFieldsType.Date) {
        fieldValue = fieldValue ? dayjs(fieldValue).valueOf() : fieldValue;
      }
      return {
        fieldType,
        fieldCode,
        fieldOperator,
        fieldValue,
      };
    });
    return fields;
  };

  useImperativeHandle(ref, () => {
    return { form, getFields };
  });

  useEffect(() => {
    const initForm = (list: any) => {
      list.forEach((field: any) => {
        form.setFieldValue(['fields', field.rowKey, 'fieldCode'], field.fieldCode);
        form.setFieldValue(['fields', field.rowKey, 'fieldOperator'], field.fieldOperator);
        form.setFieldValue(['fields', field.rowKey, 'fieldValue'], field.fieldValue);
        form.setFieldValue(['fields', field.rowKey, 'fieldType'], field.fieldType);
      });
    };
    if (fields?.length > 0) {
      const newFields = fields.map((field: any) => {
        const obj = { ...field, rowKey: rowKey.current++ };
        const fieldType = fieldsList.find((item: any) => item.targetFieldCode === field.fieldCode)?.fieldType;
        if (fieldType === EFieldsType.Date) {
          obj.fieldValue = obj.fieldValue ? dayjs(Number(field.fieldValue)) : obj.fieldValue;
        }
        return obj;
      });
      setRuleDefineList(newFields);
      initForm(newFields);
    } else {
      if (emptyInitRef.current) {
        return;
      }
      emptyInitRef.current = true;
      const list = [
        {
          fieldCode: undefined,
          fieldName: '',
          fieldOperator: undefined,
          fieldType: 0,
          fieldValue: '',
          rowKey: rowKey.current++,
        },
      ];
      setRuleDefineList(list);
      initForm(list);
    }
  }, [fields, fieldsList]);

  const handleAdd = () => {
    const fields = [
      ...ruleDefineList,
      {
        fieldCode: undefined,
        fieldName: '',
        fieldOperator: undefined,
        fieldType: 0,
        fieldValue: '',
        rowKey: rowKey.current++,
      },
    ];
    setRuleDefineList(fields);
  };

  const handleDelete = (rowKeyField: number) => {
    const newFields = ruleDefineList.filter((field) => field.rowKey !== rowKeyField).filter((item) => item);
    setRuleDefineList(newFields);
  };

  const onFieldCodeChange = (value: any, rowKey: any) => {
    const one = fieldsList.filter((item: any) => item.value === value);
    form.setFieldValue(['fields', rowKey, 'fieldType'], one[0].fieldType);
    form.setFieldValue(['fields', rowKey, 'fieldOperator'], undefined);
    form.setFieldValue(['fields', rowKey, 'fieldValue'], undefined);

    const list = [...ruleDefineList];
    const index = list.findIndex((item) => item.rowKey === rowKey);
    list[index].fieldCode = value;
    list[index].fieldType = one[0].fieldType;
    list[index].fieldOperator = undefined;
    list[index].fieldValue = undefined;

    setRuleDefineList(list);
  };
  const renderFieldValue = (rowKey: number) => {
    const fieldCode = form.getFieldValue(['fields', rowKey, 'fieldCode']);
    const fieldType = fieldsList.find((field: any) => field.targetFieldCode === fieldCode)?.fieldType;

    try {
      if (fieldType === EFieldsType.Date) {
        return <DatePicker showTime style={{ width: '100%' }} placeholder="请选择字段值" />;
      }
      if (fieldType === EFieldsType.Number) {
        return <InputNumber style={{ width: '100%' }} placeholder="请输入字段值" />;
      }
      return <Input maxLength={500} placeholder="请输入字段值" />;
    } catch (error) {
      return <Input maxLength={500} placeholder="请输入字段值" />;
    }
  };
  return (
    <div className={styles.container}>
      <Divider style={{ marginTop: 0, marginBottom: 16, borderColor: '#E4E5E6', transform: 'scaleY(0.5)' }} />
      <div className={styles.layout}>
        {ruleDefineList.length > 1 && (
          <div className={styles.relation}>
            <div
              onClick={() => {
                if (onlyRead) return;
                const key = operator === ECondition.AND ? ECondition.OR : ECondition.AND;
                const formFields = ruleDefineList.map((field) => {
                  const { rowKey } = field;
                  const fieldCode = form.getFieldValue(['fields', rowKey, 'fieldCode']);
                  const fieldOperator = form.getFieldValue(['fields', rowKey, 'fieldOperator']);
                  const fieldValue = form.getFieldValue(['fields', rowKey, 'fieldValue']);
                  return {
                    fieldCode,
                    fieldOperator,
                    fieldValue,
                  };
                });
                onOperatorChange(Number(key), formFields);
              }}
              className={styles.relationText}
            >
              {operator === ECondition.AND ? '且' : '或'}
            </div>
            <div className={styles.line}></div>
          </div>
        )}
        <div style={{ width: ruleDefineList.length <= 1 ? '100%' : '' }} className={styles.formLayout}>
          <Form form={form} disabled={onlyRead}>
            {ruleDefineList.map((field, index) => {
              const { rowKey } = field;
              return (
                <div className={styles.ruleItem} key={rowKey}>
                  <Row>
                    <div className={styles.letter}>{numberToLetter(index + 1)}</div>
                    <Form.Item
                      style={{
                        width: field.fieldCode == 'textContent' ? 300 : 'calc((100% - 100px) * 0.4)',
                        marginRight: 16,
                        marginBottom: 0,
                      }}
                      rules={[{ required: true, message: '请选择字段' }]}
                      className={styles.formItem}
                      name={['fields', rowKey, 'fieldCode']}
                    >
                      <Select
                        placeholder="请选择字段"
                        options={fieldsList}
                        {...selectSearch}
                        onChange={(v) => onFieldCodeChange(v, rowKey)}
                      />
                    </Form.Item>
                    {field.fieldCode == 'textContent' ? null : (
                      <Form.Item
                        style={{ width: 'calc((100% - 100px) * 0.2)', marginRight: 16, marginBottom: 0 }}
                        rules={[{ required: true, message: '请选择操作符' }]}
                        className={styles.formItem}
                        name={['fields', rowKey, 'fieldOperator']}
                      >
                        <Select
                          placeholder="操作符"
                          options={operatorMap[form.getFieldValue(['fields', rowKey, 'fieldCode'])]}
                        />
                      </Form.Item>
                    )}
                    {field.fieldCode == 'textContent' && !onlyRead && (
                      <Row>
                        <Col flex="400px">
                          <Button
                            onClick={() => {
                              setCurrentTest({
                                fieldValue: form.getFieldValue(['fields', rowKey, 'fieldValue']) || '',
                                tenantNo,
                              });
                              setModalVisible(true);
                            }}
                            style={{ marginRight: 20, fontWeight: 500 }}
                            type="primary"
                            ghost
                          >
                            规则试运行
                          </Button>
                        </Col>
                      </Row>
                    )}
                    {!['textContent'].includes(field.fieldCode) && (
                      <Form.Item
                        style={{ width: 'calc((100% - 100px) * 0.4)', marginBottom: 0 }}
                        rules={[{ required: true, message: '请输入字段值' }]}
                        className={styles.formItem}
                        name={['fields', rowKey, 'fieldValue']}
                      >
                        {renderFieldValue(rowKey)}
                      </Form.Item>
                    )}
                    {index > 0 && !onlyRead && (
                      <CloseOutlined onClick={() => handleDelete(rowKey)} className={styles.deleteAction} />
                    )}
                    <Form.Item
                      className={styles.formItem}
                      style={{ visibility: 'hidden', width: 0, height: 0, padding: 0, margin: 0, border: 0 }}
                      name={['fields', rowKey, 'fieldType']}
                    >
                      <Input maxLength={32} placeholder="请输入字段值" />
                    </Form.Item>
                  </Row>
                  {field.fieldCode == 'textContent' && (
                    <Row style={{ width: 'calc(100% - 32px)', marginLeft: 32 }}>
                      <Form.Item
                        style={{ width: '100%', marginTop: 16 }}
                        rules={[{ required: true, message: '请输入规则表达式' }]}
                        className={styles.formItem}
                        name={['fields', rowKey, 'fieldValue']}
                      >
                        <Input.TextArea
                          autoSize={{ minRows: 2, maxRows: 6 }}
                          maxLength={4000}
                          showCount
                          placeholder="请输入规则表达式"
                        />
                      </Form.Item>
                    </Row>
                  )}
                </div>
              );
            })}
          </Form>
        </div>
      </div>
      {!onlyRead && (
        <Row style={{ display: 'flex', marginTop: 10, position: 'relative', left: '-12px' }}>
          <Button onClick={handleAdd} style={{ fontWeight: 500 }} icon={<div className={styles.corss} />} type="link">
            添加规则
          </Button>
        </Row>
      )}
      <TextContentTestModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        currentTest={currentTest}
        tenantNos={tenantNos}
        isRedLine={isRedLine}
      />
    </div>
  );
});

export default CustomRule;
