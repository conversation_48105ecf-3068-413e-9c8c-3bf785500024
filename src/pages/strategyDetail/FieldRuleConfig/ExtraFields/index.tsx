import ExtraDown from '@/assets/extraDown.svg';
import ExtraUp from '@/assets/extraUp.svg';
import Collapse from '@/components/Collapse';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Divider, Form, Input, InputNumber, Row, Select } from 'antd';
import React, { FC } from 'react';
import AiRuleTrialRunBtn from '../AiRuleTrialRunModal/AiRuleTrialRunBtn';
import styles from './index.scss';

const AdvancedConfig: FC<any> = ({ riskList, labelList, name, rowKey }) => {
  return (
    <Collapse
      activeLabel={
        <div className={styles.extraHeader}>
          <img src={ExtraDown} />
          <span>高级配置</span>
        </div>
      }
      unActiveLabel={
        <div className={styles.extraHeader}>
          <img src={ExtraUp} />
          <span>收起</span>
        </div>
      }
      key={rowKey}
      children={
        <>
          <Form.Item label="风险等级" name={[...name, 'riskLevel']}>
            <Select
              allowClear
              options={riskList}
              placeholder="选择风险等级"
              style={{ marginLeft: 64, width: 'calc(100% - 64px)' }}
            />
          </Form.Item>
          <Form.Item label="评分" name={[...name, 'score']}>
            <InputNumber placeholder="请输入评分" style={{ marginLeft: 92, width: 'calc(100% - 92px)' }} />
          </Form.Item>
          <Form.Item label="标签" name={[...name, 'label']}>
            <Select
              allowClear
              options={labelList}
              mode="multiple"
              placeholder="请选择标签"
              style={{ marginLeft: 92, width: 'calc(100% - 92px)' }}
            />
          </Form.Item>
        </>
      }
    />
  );
};

const ExtraFields: FC<any> = ({
  field: parentField,
  riskList,
  labelList,
  isCE = false,
  onlyRead,
  tenantNo,
  tenantNos,
  isTemplate,
  isRedLine,
  parentForm,
  strategyId,
  botNo,
  strategyName,
  parentRowKey,
}) => {
  const { rowKey, id: ruleId } = parentField;

  return isCE ? (
    <>
      <Form.List name={['fields', rowKey, 'ruleData']} initialValue={[{}]}>
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, index) => (
              <div key={field.key} className={styles.extraFieldListItem}>
                <div className={styles.extraFieldListItemCon}>
                  {/* 为了提交的时候获取到id */}
                  <Form.Item hidden name={[field.name, 'id']}>
                    <Input />
                  </Form.Item>
                  <Form.Item
                    label="规则名称"
                    style={{ marginBottom: 0, marginTop: 16 }}
                    rules={[
                      {
                        required: true,
                        message: '请输入规则名称',
                      },
                    ]}
                    name={[field.name, 'ruleName']}
                    className={styles.ruleFormItem}
                  >
                    <Input placeholder="请输入规则名称" />
                  </Form.Item>
                  {!onlyRead && (
                    <CloseOutlined
                      style={{
                        visibility: index > 0 ? 'visible' : 'hidden',
                        color: '#4E5969',
                        position: 'absolute',
                        top: '8px',
                        right: '-5px',
                      }}
                      onClick={() => remove(field.name)}
                    />
                  )}
                </div>
                <AdvancedConfig rowKey={field.key} riskList={riskList} labelList={labelList} name={[field.name]} />
              </div>
            ))}
            {!onlyRead && (
              <>
                <Button
                  onClick={() => add()}
                  style={{ fontWeight: 500, padding: 0, margin: '10px 0' }}
                  icon={<div className={styles.corss} />}
                  type="link"
                >
                  添加规则
                </Button>
                <Divider style={{ margin: 0, borderColor: '#E4E5E6', transform: 'scaleY(0.5)' }} />
                <Row justify={'end'}>
                  <AiRuleTrialRunBtn
                    style={{ marginTop: 16 }}
                    isTemplate={isTemplate}
                    form={parentForm}
                    ruleId={ruleId}
                    tenantNo={tenantNo}
                    tenantNos={tenantNos}
                    botNo={botNo}
                    strategyId={strategyId}
                    parentRowKey={parentRowKey}
                    isRedLine={isRedLine}
                    isCE={isCE}
                    strategyName={strategyName}
                  />
                </Row>
              </>
            )}
          </>
        )}
      </Form.List>
    </>
  ) : (
    <AdvancedConfig rowKey={rowKey} riskList={riskList} labelList={labelList} name={['fields', rowKey]} />
  );
};

export default ExtraFields;
