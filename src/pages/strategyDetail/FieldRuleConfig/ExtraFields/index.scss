.extraHeader {
  display: flex;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 16px;
  img {
    margin-right: 8px;
  }
  span {
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
  }
}

.extraFieldListItem {
  border-radius: 8px;
  background: #f8f8f8;
  padding: 0 16px 16px;
  margin-top: 16px;
  overflow: hidden;
  .extraFieldListItemCon {
    position: relative;
  }
  .extraHeader {
    margin-bottom: 0;
  }
  :global {
    .ant-form-item-control {
      margin-left: 37px;
      margin-right: 20px;
    }
    .ant-collapse-content {
      &.ant-collapse-content-active {
        margin-top: 16px;
      }
      .ant-form-item-control {
        margin-left: -16px;
      }
      background: transparent !important;
    }
  }
}

.corss {
  background: #f2f2fe;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  &::before,
  &::after {
    content: '';
    position: absolute;
    background-color: var(--primary-color);
    border-radius: 4px;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
  }
  &::before {
    width: 1.5px;
    height: 10px;
  }

  &::after {
    width: 10px;
    height: 1.5px;
  }
}
