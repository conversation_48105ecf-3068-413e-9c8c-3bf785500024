.formItem {
  margin-right: 12px !important;
  width: 200px;
}
.ruleFormItem {
  :global {
    .ant-form-item-control {
      margin-left: 53px;
    }
  }
}
.container {
  padding: 20px;
  border-radius: 12px;
  background-color: #fff;
  margin-top: 16px;
}
.title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: #1d2129;
}
.titleContainer {
  display: flex;
  justify-content: space-between;
  width: 70%;
}
.formContainer {
  width: 70%;
}
@media (max-width: 1200px) {
  .formContainer {
    width: 100%;
  }
  .titleContainer {
    width: 100%;
  }
}
.ruleRow {
  margin-top: 16px;
  padding: 20px;
  border-radius: 8px;
  padding-right: 20px;
  position: relative;
  &.ruleRowCustom {
    padding-right: 20px;
  }
}
.deleteAction {
  position: absolute;
  right: 0;
  top: 22px;
}
.ruleOptionRect {
  width: 36px;
  height: 36px;
  opacity: 0.5;
  border-radius: 4px;
  margin-bottom: 8px;
}
.ruleItemName {
  width: 100px;
  color: #4e5969;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.ruleItemDesc {
  &:global(.ant-tag) {
    color: #1d2129;
    margin-right: 24px;
    font-size: 12px;
    font-weight: 500;
    height: 20px;
    padding: 0 8px;
    border-radius: 20px;
    display: flex;
    align-items: center;
  }
}
.deleteActionText {
  cursor: pointer;
  position: absolute;
  right: -4px;
  top: 0px;
  font-size: 14px;
  color: #1d2129;
  width: 32px;
}
.layout {
  width: 100%;
  display: flex;

  .relation {
    width: 28px;
    margin-right: 12px;
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 16px;
    justify-content: center;
    .line {
      width: 3px;
      background-color: rgba(223, 223, 252, 1);
      top: 0;
      bottom: 0;
      position: absolute;
    }
    .relationText {
      position: relative;
      z-index: 1;
      height: 28px;
      width: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      color: var(--primary-color);
      background-color: #e7e7fd;
      font-weight: 600;
      line-height: 28px;
    }
  }
  .formLayout {
    width: 100%;
  }
}
