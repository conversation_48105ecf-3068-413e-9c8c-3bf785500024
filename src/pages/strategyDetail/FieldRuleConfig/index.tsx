//@ts-nocheck
/**规则定义 */
import MoreClose from '@/assets/moreClose.svg';
import MoreOpen from '@/assets/moreOpen.svg';
import Collapse from '@/components/Collapse';
import { getAvailableNewList, getRobotSkillList, getRuleTypeList } from '@/services/ce';
import { convertToChineseNumber } from '@/utils';
import { ECondition, ERuleType, RuleTypeMap } from '@/utils/constants';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Input, message, Popconfirm, Row, Select, Tag, Tooltip } from 'antd';
import classnames from 'classnames';
import { cloneDeep } from 'lodash';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import AiRuleTemplateModal from '../AiRuleTemplateModal';
import AddRuleModal from './AddRuleModal';
import AiRule from './AiRule';
import CustomRule from './CustomRule';
import ExtraFields from './ExtraFields';
import styles from './index.scss';

interface IProps {
  tenantNo: string;
  id: string;
  fieldsList: any;
  operatorMap: any;
  labelList: any;
  riskList: any;
  tenantNos?: any[];
  isRedLine?: string;
  onlyRead?: boolean;
  strategyName?: string;
}

const FieldRuleConfig = React.forwardRef((props: IProps, ref) => {
  const { id, tenantNo, fieldsList, operatorMap, riskList, labelList, tenantNos, isRedLine, onlyRead, strategyName } =
    props;
  const [skillMap, setSkillMap] = useState<Record<string, ISkillOption[]>>({});
  const [addRuleModalVisible, setAddRuleModalVisible] = useState(false);
  const [aiRuleTemplateModalVisible, setAiRuleTemplateModalVisible] = useState(false);
  const [addRuleLoading, setAddRuleLoading] = useState(false);
  const [selectOption, setSelectOption] = useState({});
  const [ruleOptions, setRuleOptions] = useState([]);
  const [fields, setFields] = useState<IStrategyRule[]>([]);
  const skillMapRef = useRef({});
  const customRuleFormList = useRef<any>({
    0: React.createRef(),
  });
  const rowKey = useRef(0);
  const [form] = Form.useForm();

  useEffect(() => {
    if (!addRuleModalVisible) {
      setSelectOption({});
    }
  }, [addRuleModalVisible]);

  useEffect(() => {
    const run = async () => {
      if (!fields.length) {
        return;
      }
      let needRequestId = fields
        .filter((field: any) => field && !skillMap[field.robotNo])
        .map((field: any) => field.robotNo)
        .filter((v: any) => !!v && !skillMapRef.current[v]);
      needRequestId = [...new Set(needRequestId)];
      if (needRequestId.length === 0) {
        return;
      }
      const promiseList = needRequestId.map((id: any) => {
        skillMapRef.current[id] = true;
        return getRobotSkillList(id);
      });
      const res = await Promise.all(promiseList);
      needRequestId.forEach((id: string, index: number) => {
        setSkillMap((pre) => {
          return {
            ...pre,
            [id]: res[index].data.value.map((item: any) => {
              return {
                ...item,
                label: item.skillName,
                value: item.skillNo,
              };
            }),
          };
        });
      });
    };
    run();
  }, [fields, skillMap]);

  const handleAddRule = async (params: any = {}) => {
    if (selectOption.code === ERuleType.AI_TEMPLATE && !params.createFromTemplate) {
      setAiRuleTemplateModalVisible(true);
      return;
    }
    setAddRuleLoading(true);
    let bot = {};
    if (selectOption.code !== ERuleType.CUSTOM) {
      const res = await getAvailableNewList(tenantNo, params.code || selectOption.code);

      if (res.data.success && res.data.value?.[0]) {
        bot = res.data.value[0];
      } else {
        message.error('获取机器人失败');
        setAddRuleLoading(false);
        return;
      }
    }
    customRuleFormList.current[rowKey.current] = React.createRef();
    const newFields = [
      ...fields,
      {
        robotNo: bot.botNo,
        ruleType: params.code || selectOption.code,
        skillName: '',
        ruleName: '',
        skillNo: '',
        rowKey: rowKey.current++,
        fieldRuleList: null,
        operateType: ECondition.AND,
        aiFieldRuleList: params.createFromTemplate === ERuleType.AI_TEMPLATE ? params.aiFieldRuleList : null,
      },
    ];
    setFields(newFields);
    setAddRuleLoading(false);
    setAddRuleModalVisible(false);
  };

  useEffect(() => {
    getRuleTypeList().then((res) => {
      if (res.data.success) {
        setRuleOptions(res.data.value);
      }
    });
  }, []);

  const initFields = (fields: any) => {
    if (fields?.length === 0) {
      return;
    }
    rowKey.current = 0;
    customRuleFormList.current = {};
    const newFields = fields.map((field: any) => {
      customRuleFormList.current[rowKey.current] = React.createRef();
      return {
        ...field,
        fieldRuleList: field?.strategyGroupConfigDTO?.fieldGroupList
          ? [].concat(...field?.strategyGroupConfigDTO?.fieldGroupList)
          : [],
        operateType: field?.strategyGroupConfigDTO?.operateType || ECondition.AND,
        rowKey: rowKey.current++,
      };
    });
    setFields(newFields);
    newFields.forEach((field: any) => {
      form.setFieldValue(['fields', field.rowKey, 'robotNo'], field.robotNo);
      form.setFieldValue(['fields', field.rowKey, 'ruleName'], field.ruleName);
      form.setFieldValue(['fields', field.rowKey, 'skillNo'], field.skillNo);
      form.setFieldValue(['fields', field.rowKey, 'riskLevel'], field.riskLevel || undefined);
      form.setFieldValue(['fields', field.rowKey, 'score'], field.score);
      form.setFieldValue(
        ['fields', field.rowKey, 'label'],
        (field.label || '')
          .split(',')
          .filter(Boolean)
          .map((item) => Number(item))
      );
      form.setFieldValue(['fields', field.rowKey, 'ruleData'], field.ruleData);
    });
  };

  const getFields = async () => {
    if (fields.length === 0) {
      message.warning('请添加质检规则');
      return Promise.reject('');
    }
    const values = await form.validateFields();
    const res: IStrategyRule[] = [...fields];
    const rowKeyList = fields.map((field) => field.rowKey);
    const formFields: any = [];
    for (const rowKey of rowKeyList) {
      const fields = values.fields || [];
      formFields.push({
        ...fields[rowKey],
        rowKey,
      });
    }
    const formFieldsMap = formFields.reduce((acc: any, cur: any) => {
      acc[cur.rowKey] = cur;
      return acc;
    }, {});
    for (const field of res) {
      const formField = formFieldsMap[field.rowKey];
      !!formField.ruleData?.length && (field.ruleData = formField.ruleData);
      if (field?.ruleType == ERuleType.CUSTOM || field?.ruleType == ERuleType.AI) {
        const ref = customRuleFormList.current[field.rowKey];
        if (ref.current && ref.current.form) {
          await ref.current.form.validateFields();
        }
      }

      if (field.ruleType === ERuleType.AI) {
        field.skillNo = formField.skillNo;
        if (skillMap?.[field?.robotNo]) {
          field.skillName = skillMap[field.robotNo].find((item) => item.value === formField.skillNo)?.label || '';
        }

        const ref = customRuleFormList.current[field.rowKey];
        const values = await ref.current.form.validateFields();
        field.aiFieldRuleList = [
          {
            ...values,
            aiRuleName: values.aiRuleName || values.ruleName,
          },
        ];
        field.ruleName = values.ruleName;
        field.score = values.score || '';
        field.riskLevel = values.riskLevel || '';
        field.label = values.label?.join(',') || '';
      } else if (formField) {
        if (formField.robotNo) {
          field.robotNo = formField.robotNo;
        }
        field.skillNo = formField.skillNo;
        if (skillMap?.[field.robotNo || formField?.robotNo]) {
          field.skillName =
            skillMap[field.robotNo || formField?.robotNo].find((item) => item.value === formField.skillNo)?.label || '';
        }
        field.ruleName = formField.ruleName;
        if (field.ruleType === ERuleType.CUSTOM) {
          const ref = customRuleFormList.current[field.rowKey];
          const fieldsList = ref.current.getFields();
          field.strategyGroupConfigDTO = {
            fieldGroupList: fieldsList.map((item) => [item]),
            operateType: field.operateType || ECondition.AND,
          };
        }
      }
      if (field.ruleType !== ERuleType.AI) {
        field.score = formField.score || '';
        field.riskLevel = formField.riskLevel || '';
        field.label = formField.label?.join(',') || '';
      }
      delete field.operateType;
    }
    let resData = [];
    res?.forEach(({ ruleData, ...other }, index) => {
      if (ruleData?.length) {
        resData = [
          ...resData,
          ...ruleData.map((v) => {
            return {
              ...other,
              ...v,
              label: v.label?.length ? v.label.join(',') : '',
              score: v.score || '',
              riskLevel: v.riskLevel || '',
              sort: index,
            };
          }),
        ];
      } else {
        resData.push({ ...other, sort: index });
      }
    });
    return cloneDeep(resData).map((item: any) => {
      delete item.rowKey;
      delete item.fieldRuleList;
      return item;
    }) as Omit<IFieldConfig, 'rowKey'>[];
  };

  const handleDelete = (rowKey: number) => {
    const newFields = fields.filter((field) => field.rowKey !== rowKey);
    delete customRuleFormList.current[rowKey];
    setFields(newFields);
  };

  const getCustomRuleFormList = () => {
    const forms = [];
    fields?.forEach((field) => {
      if (field?.ruleType == ERuleType.CUSTOM) {
        forms.push(customRuleFormList.current[field.rowKey]?.current?.form);
      }
    });
    return forms;
  };

  const getCustomRuleFormValues = () => {
    const values = getCustomRuleFormList()?.map((form) => form?.getFieldsValue());
    return values;
  };

  const clearCustomRuleFormValues = () => {
    getCustomRuleFormList()?.forEach((form) => form?.setFieldsValue({ fields: [] }));
  };

  useImperativeHandle(ref, () => ({
    form,
    getCustomRuleFormValues,
    clearCustomRuleFormValues,
    getFields,
    initFields,
  }));

  return (
    <div className={styles.container}>
      <div className={styles.titleContainer}>
        <div className={styles.title}>
          规则定义
          <Tooltip title="基于当前质检策略，配置一个或者多个质检规则，符合该策略的会话将被此处配置的所有规则进行质检">
            <QuestionCircleOutlined style={{ fontSize: 16, color: '#BABEC4', marginLeft: 4, cursor: 'pointer' }} />
          </Tooltip>
        </div>
        {!onlyRead && (
          <div
            onClick={() => setAddRuleModalVisible(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
            }}
          >
            <div
              style={{
                background: '#27AE60',
                padding: 4,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <PlusOutlined style={{ fontSize: 12, color: '#fff', fontWeight: 500 }} />
            </div>
            <div
              style={{
                marginLeft: 8,
                color: '#27AE60',
                fontWeight: 500,
              }}
            >
              添加规则
            </div>
          </div>
        )}
      </div>
      <div className={styles.layout}>
        <div className={styles.formLayout}>
          <Form className={styles.formContainer} form={form} disabled={onlyRead}>
            {fields.map((field, index) => {
              const { rowKey } = field;
              const renderTitle = (icon) => (
                <Row>
                  <img style={{ marginRight: 8 }} src={icon} />
                  <div style={{ color: 'rgba(29, 33, 41, 1)', fontWeight: 600 }} className={styles.ruleItemName}>
                    质检规则{convertToChineseNumber(index + 1)}：
                  </div>
                  <Tag
                    style={{
                      color: RuleTypeMap[field.ruleType].tagColor,
                      borderColor: RuleTypeMap[field.ruleType].tagBorderColor,
                      background: RuleTypeMap[field.ruleType].tagBgColor,
                    }}
                    className={styles.ruleItemDesc}
                  >
                    {ruleOptions.find((item) => item.code === field.ruleType)?.name || '未知规则'}
                  </Tag>
                  {!onlyRead && (
                    <Popconfirm
                      title="删除规则后，原规则命中的结果将无法被查到"
                      onPopupClick={(e) => e.stopPropagation()}
                      onConfirm={() => handleDelete(rowKey)}
                    >
                      <div style={{ top: 0 }} className={styles.deleteAction} onClick={(e) => e.stopPropagation()}>
                        <div className={styles.deleteActionText}>删除</div>
                      </div>
                    </Popconfirm>
                  )}
                </Row>
              );
              return (
                <div
                  style={{
                    border: '1px solid #E4E5E6',
                    boxSizing: 'border-box',
                  }}
                  className={classnames(styles.ruleRow, {
                    [styles.ruleRowCustom]: field.ruleType === ERuleType.CUSTOM,
                  })}
                  key={rowKey}
                >
                  <Collapse
                    defaultOpen
                    children={
                      <>
                        {[ERuleType.NORMAL, ERuleType.CE].includes(field?.ruleType) ? (
                          <>
                            <Form.Item
                              label="规则技能"
                              style={{ marginBottom: 0, marginTop: 16 }}
                              rules={[
                                {
                                  required: field?.ruleType == ERuleType.CUSTOM ? false : true,
                                  message: '请选择技能',
                                },
                              ]}
                              name={['fields', rowKey, 'skillNo']}
                              className={styles.ruleFormItem}
                            >
                              <Select placeholder="请选择技能" options={skillMap?.[field?.robotNo] || []} />
                            </Form.Item>
                            <ExtraFields
                              isCE={field?.ruleType == ERuleType.CE}
                              field={field}
                              riskList={riskList}
                              labelList={labelList}
                              onlyRead={onlyRead}
                              tenantNo={tenantNo}
                              tenantNos={tenantNos}
                              botNo={field?.robotNo}
                              strategyId={id}
                              parentForm={form}
                              strategyName={strategyName}
                              parentRowKey={rowKey}
                            />
                          </>
                        ) : (
                          <Form.Item noStyle hidden name={['fields', rowKey, 'skillNo']}>
                            <Input />
                          </Form.Item>
                        )}
                        {field?.ruleType == ERuleType.CUSTOM && (
                          <>
                            <Form.Item
                              label="规则名称"
                              style={{ marginBottom: 0, marginTop: 16 }}
                              rules={[{ required: true, message: '请输入规则名称' }]}
                              name={['fields', rowKey, 'ruleName']}
                              className={styles.ruleFormItem}
                            >
                              <Input maxLength={32} placeholder="请输入规则名称" />
                            </Form.Item>
                            <ExtraFields field={field} riskList={riskList} labelList={labelList} onlyRead={onlyRead} />
                          </>
                        )}
                        {field?.ruleType == ERuleType.CUSTOM && (
                          <CustomRule
                            ref={customRuleFormList.current[rowKey]}
                            operator={field.operateType}
                            onOperatorChange={(value, fieldValue) => {
                              const newFields = [...fields];
                              newFields[index].operateType = Number(value);
                              newFields[index].fieldRuleList = fieldValue;
                              setFields(newFields);
                            }}
                            fields={field.fieldRuleList || []}
                            fieldsList={fieldsList}
                            tenantNo={tenantNo}
                            operatorMap={operatorMap}
                            tenantNos={tenantNos}
                            isRedLine={isRedLine}
                            onlyRead={onlyRead}
                          />
                        )}
                        {field?.ruleType == ERuleType.AI && (
                          <AiRule
                            riskList={riskList}
                            labelList={labelList}
                            botNo={field?.robotNo}
                            parentForm={form}
                            parentRowKey={rowKey}
                            ruleId={field.id}
                            parentField={field}
                            fields={field.aiFieldRuleList ? field.aiFieldRuleList[0] : {}}
                            tenantNo={tenantNo}
                            strategyId={id}
                            tenantNos={tenantNos}
                            isRedLine={isRedLine}
                            ref={customRuleFormList.current[rowKey]}
                            onlyRead={onlyRead}
                          />
                        )}
                      </>
                    }
                    activeLabel={renderTitle(MoreClose)}
                    unActiveLabel={renderTitle(MoreOpen)}
                  />
                </div>
              );
            })}
          </Form>
        </div>
      </div>
      <AddRuleModal
        visible={addRuleModalVisible}
        setVisible={setAddRuleModalVisible}
        loading={addRuleLoading}
        handleAddRule={handleAddRule}
        ruleOptions={ruleOptions}
        selectOption={selectOption}
        setSelectOption={setSelectOption}
      />
      <AiRuleTemplateModal
        open={aiRuleTemplateModalVisible}
        onCancel={() => setAiRuleTemplateModalVisible(false)}
        onConfirm={(template: any) => {
          const params = {
            createFromTemplate: ERuleType.AI_TEMPLATE,
            code: ERuleType.AI,
            aiFieldRuleList: [
              {
                aiRuleName: template.aiRuleName,
                aiRuleDesc: template.aiRuleDesc,
              },
            ],
          };
          setAiRuleTemplateModalVisible(false);
          handleAddRule(params);
        }}
      />
    </div>
  );
});
export default FieldRuleConfig;
