.addRuleModal {
    :global {
        .ant-modal {
            .ant-modal-content {
                .ant-modal-close {
                    display: none;
                }

                .ant-modal-header {
                    margin-bottom: 16px;
                    text-align: center;
                }

                .ant-modal-footer {
                    margin-top: 16px;
                }
            }
        }
    }
}

.ruleOptions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    .ruleOption {
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-sizing: border-box;
        align-items: center;
        height: 150px;
        box-sizing: border-box;
        margin: 8px;
        background-color: #f0f0f1;
        border-radius: 8px;
        width: 200px;
        padding: 4px;
        cursor: pointer;
        border: 1px solid transparent;
    }

    .ruleOptionTitle {
        font-weight: 500;
        font-size: 16px;
        text-align: center;
        color: #1d2129;
        margin-bottom: 8px;
    }

    .ruleOptionDesc {
        color: #1d2129;
        font-size: 14px;
        text-align: center;
    }
}