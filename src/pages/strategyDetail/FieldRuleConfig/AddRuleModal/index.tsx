import { ERuleType, RuleTypeMap } from '@/utils/constants';
import { message, Modal } from 'antd';
import classnames from 'classnames';
import { isEmpty } from 'lodash';
import React, { FC } from 'react';
import styles from './index.scss';

interface IProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  loading: boolean;
  selectOption: any;
  setSelectOption: React.Dispatch<React.SetStateAction<any>>;
  handleAddRule: () => Promise<void>;
  ruleOptions: any[];
}

const AddRuleModal: FC<IProps> = ({
  visible,
  setVisible,
  loading,
  selectOption,
  setSelectOption,
  handleAddRule,
  ruleOptions,
}) => {
  return (
    <Modal
      centered
      width={720}
      title="新增质检规则"
      rootClassName={styles.addRuleModal}
      open={visible}
      onCancel={() => setVisible(false)}
      confirmLoading={loading}
      onOk={async () => {
        if (isEmpty(selectOption)) {
          message.warning('请选择规则');
          return;
        }
        await handleAddRule();
      }}
    >
      {[ruleOptions.slice(0, 2), ruleOptions.slice(2)].map((ruleOptionsData, index) => (
        <div className={styles.ruleOptions} key={index}>
          {ruleOptionsData.map((ruleOption: any) => (
            <div
              onClick={() => setSelectOption(ruleOption)}
              style={{
                border: `${selectOption.code === ruleOption.code ? '2px' : '0.5px'} solid ${
                  RuleTypeMap[ruleOption.code as ERuleType].color
                }`,
                backgroundColor: RuleTypeMap[ruleOption.code as ERuleType].bgColor,
              }}
              className={classnames(styles.ruleOption)}
              key={ruleOption.code}
            >
              <img style={{ marginBottom: 8 }} src={RuleTypeMap[ruleOption.code as ERuleType].img} />
              <div className={styles.ruleOptionTitle}>{ruleOption.name}</div>
              <div className={styles.ruleOptionDesc}>{ruleOption.desc}</div>
            </div>
          ))}
        </div>
      ))}
    </Modal>
  );
};

export default AddRuleModal;
