import {
  ETime,
  isShowMsgNum,
  QUALITY_MSG_NUM_TYPE_OPTIONS,
  QUALITY_OPTIONS,
  QUALITY_TYPE,
  requiredRules,
} from '@/utils/constants';
import { Col, Form, FormInstance, Input, InputNumber, Row, Select, TimePicker } from 'antd';
import classNames from 'classnames';
import React, { FC, useMemo } from 'react';
import styles from './index.scss';

const { Item } = Form;

interface IProps {
  isTemplate?: boolean;
  form: FormInstance;
  groupName?: string;
  disabled?: boolean;
  isRedLine?: string;
  onValuesChange?: (changedValues: any, values: any) => void;
}

const BaseInfo: FC<IProps> = ({ isTemplate, form, groupName, disabled, onValuesChange, isRedLine }) => {
  const executeType = Form.useWatch(['executeType'], form);
  const qualityType = Form.useWatch(['qualityType'], form);
  const qualityMsgNumType = Form.useWatch(['qualityMsgNumType'], form);

  const isRealTime = useMemo(() => qualityType === QUALITY_TYPE.REAL_TIME, [qualityType]);

  const isRed = useMemo(() => isRedLine === '1', [isRedLine]);

  return (
    <div className={classNames(styles.baseInfo, { [styles.baseInfo1]: !isRed })}>
      <div className={styles.baseInfoTitle}>基本信息</div>
      <Form labelAlign="left" className={styles.form} form={form} disabled={disabled} onValuesChange={onValuesChange}>
        <Row gutter={24}>
          <Col span={8}>
            <Item rules={requiredRules} label="策略名称" name="strategyName">
              <Input maxLength={32} placeholder="请输入策略名称" />
            </Item>
          </Col>
          {!isTemplate && (
            <>
              <Col span={8}>
                <Item rules={requiredRules} label="策略分组" style={!isRed ? {} : { marginLeft: 11 }}>
                  <Input disabled value={groupName} />
                </Item>
              </Col>
              <Col span={8}>
                <Item rules={requiredRules} label="策略状态" name="status">
                  <Select
                    placeholder="请选择状态"
                    options={[
                      {
                        label: '启用',
                        value: 1,
                      },
                      {
                        label: '停用',
                        value: 0,
                      },
                    ]}
                  />
                </Item>
              </Col>
            </>
          )}
        </Row>
        {!isTemplate && (
          <Row gutter={24}>
            <Col span={8}>
              <Item label="执行时间" rules={requiredRules} name="executeType">
                <Select
                  placeholder="请选择执行时间"
                  options={[
                    {
                      label: '立即执行',
                      value: ETime.REAL_TIME,
                    },
                    // {
                    //   label: '定时',
                    //   value: ETime.REGULAR_TIME,
                    // },
                  ]}
                />
              </Item>
            </Col>
            {executeType === ETime.REGULAR_TIME && (
              <Col span={8}>
                <Item rules={[{ required: true, message: '请选择执行时间' }]} name="executeTime">
                  <TimePicker style={{ width: '100%' }} placeholder="请选择执行时间" />
                </Item>
              </Col>
            )}
            <Col span={8}>
              <Item rules={[{ required: true, message: '请选择质检类型' }]} name="qualityType" label="质检类型">
                <Select
                  options={QUALITY_OPTIONS.filter(({ value }) => (isRed ? value !== QUALITY_TYPE.REAL_TIME : true))}
                  placeholder="请选择质检类型"
                />
              </Item>
            </Col>
            {isRealTime && (
              <>
                <Col span={8}>
                  <Item
                    rules={[{ required: true, message: '请选择质检消息量' }]}
                    name="qualityMsgNumType"
                    label="质检消息量"
                  >
                    <Select options={QUALITY_MSG_NUM_TYPE_OPTIONS} placeholder="请选择质检消息量" />
                  </Item>
                </Col>
                {isShowMsgNum.includes(qualityMsgNumType) && (
                  <Col span={8}>
                    <Item
                      rules={[{ required: true, message: '请输入最近消息数' }]}
                      name="qualityMsgNum"
                      label="最近消息数"
                    >
                      <InputNumber
                        min={1}
                        max={1000}
                        precision={0}
                        placeholder="请输入最近消息数"
                        style={{ width: '100%' }}
                      />
                    </Item>
                  </Col>
                )}
              </>
            )}
          </Row>
        )}
      </Form>
    </div>
  );
};

export default BaseInfo;
