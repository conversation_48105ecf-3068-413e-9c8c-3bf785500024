import EasyTable from '@/components/EasyTable';
import PageLayout from '@/components/PageLayout';
import Upload from '@/components/Upload';
import useStrategyRuleSearch from '@/hooks/useStrategyRuleSearch';
import {
  createBatchExecuteTask,
  endRuleOptimizeTask,
  queryBatchExecuteTaskCount,
  queryBatchExecuteTaskList,
  uploadBatchExecuteFile,
} from '@/services/ce';
import { downloadFile } from '@/utils';
import { antConfig, STATIC_URL } from '@/utils/constants';
import { SyncOutlined } from '@ant-design/icons';
import { useLocation } from '@magi/magi';
import { Button, DatePicker, Form, Input, message, Modal, Popconfirm, Radio, Row, TreeSelect, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import styles from './index.scss';

const { SHOW_CHILD } = TreeSelect;

const { RangePicker } = DatePicker;

const transformTreeData = (data: any[]): any[] => {
  return data
    .filter((v: any) => typeof v.status === 'undefined' || v.status === 1)
    .map((item: any) => {
      const newItem = { ...item, title: item.label, value: item.value };
      if (item.children?.length > 0) {
        newItem.children = transformTreeData(item.children);
      }
      return newItem;
    });
};

const STATUS_MAP: any = {
  0: '排队中',
  1: '执行中',
  2: '已完成',
  3: '失败',
  4: '终止',
};

const MAX_TASK_NUM = 200000;

/**
 * 批量跑测
 */
const BatchTest = (props: any) => {
  const location: any = useLocation();
  const { tenantNo } = location.query;
  const currentTenantNo = props.currentTenantNo || tenantNo;
  const [loading, setLoading] = useState(false);
  const [listLoading, setListLoading] = useState(false);
  const [downloadLoadingData, setDownloadLoadingData] = useState<{ [key: string]: boolean }>({});
  const [visible, setVisible] = useState(false);
  const [sessionVolume, setSessionVolume] = useState(0);
  const [form] = Form.useForm();
  const tableRef = useRef<any>();
  const uploadRef = useRef<any>(null);
  const fileUrlRef = useRef<string>();

  const { columns: formColumns } = useStrategyRuleSearch({
    tenantNo: currentTenantNo,
    form,
  });

  const handleDownload = async (record: any) => {
    try {
      if (!record.fileUrl) {
        message.warning('文件不存在');
        return;
      }
      setDownloadLoadingData((preState) => ({ ...preState, [record.id]: true }));
      downloadFile(record.fileUrl, `批量执行任务${record.batchNo}.xlsx`);
    } finally {
      setDownloadLoadingData((preState) => ({ ...preState, [record.id]: false }));
    }
  };

  const columns = useMemo<any[]>(() => {
    return [
      {
        title: '批次号',
        dataIndex: 'batchNo',
        width: 200,
      },
      {
        title: '名称',
        dataIndex: 'taskName',
        width: 180,
      },
      {
        title: '会话开始时间',
        dataIndex: 'startTime',
        width: 340,
        render: (text: any, record: any) =>
          `${text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : text || ''}${text ? ' ~ ' : ''}${
            record?.endTime ? dayjs(record.endTime).format('YYYY-MM-DD HH:mm:ss') : record?.endTime || ''
          }`,
      },
      {
        title: '执行策略',
        dataIndex: 'strategyName',
        width: 400,
        render: (text: string) => (
          <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text }}>
            {text}
          </Typography.Paragraph>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreated',
        width: 180,
        render: (text: any) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : text),
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (text: any) => STATUS_MAP[text],
        width: 100,
      },
      {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
        render: (_: unknown, record: any) => {
          return (
            <div style={{ display: 'flex' }}>
              {[0, 1].includes(record.status) && (
                <Popconfirm
                  title="确定终止任务吗？"
                  onConfirm={async () => {
                    const res = await endRuleOptimizeTask({ id: record.id });
                    res.data?.value && tableRef.current.fetchData();
                  }}
                >
                  <Button type="link" style={{ marginRight: 20, padding: 0 }}>
                    终止
                  </Button>
                </Popconfirm>
              )}
              {record.status === 2 && (
                <Button
                  type="link"
                  loading={downloadLoadingData[record.id]}
                  onClick={() => handleDownload(record)}
                  style={{ padding: 0 }}
                >
                  下载
                </Button>
              )}
            </div>
          );
        },
      },
    ];
  }, [currentTenantNo, downloadLoadingData]);

  const onCancel = () => {
    fileUrlRef.current = undefined;
    uploadRef.current?.setFileList?.([]);
    form.resetFields();
    setSessionVolume(0);
    setVisible(false);
  };

  const verifyTimeRulesData = [
    {
      validator: async (_: unknown, value: any[]) => {
        if (!value) return Promise.resolve();
        const startTime = value[0] && dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss');
        const endTime = value[1] && dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss');
        const res = await queryBatchExecuteTaskCount({ startTime, endTime, tenantNo: currentTenantNo });
        const num = res?.data?.value || 0;
        setSessionVolume(num);
        if (num > MAX_TASK_NUM) {
          return Promise.reject(`会话量不能超过${MAX_TASK_NUM / 10000}万条`);
        }
        return Promise.resolve();
      },
    },
  ];

  const verifyFileData = [
    {
      validator: async (_: unknown, value: any) => {
        const file = value?.fileList?.[0]?.originFileObj;
        if (file) {
          const formData = new FormData();
          formData.append('file', file);
          const fileRes = await uploadBatchExecuteFile(formData);
          const { fileSize, fileUrl } = fileRes?.data?.value || {};
          if (fileSize > MAX_TASK_NUM) {
            return Promise.reject(`会话量不能超过${MAX_TASK_NUM / 10000}万条`);
          }
          fileUrlRef.current = fileUrl;
          return Promise.resolve();
        }
        return Promise.reject('请上传文件');
      },
    },
  ];

  const onSubmit = async () => {
    const values = await form.validateFields();
    const { timeRange, taskSource, _fileUrl, ...otherValues } = values;
    const params = {
      tenantNo: currentTenantNo,
      taskSource,
      ...otherValues,
    };
    if (taskSource === 1) {
      const startTime = timeRange?.[0] && dayjs(timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
      const endTime = timeRange?.[1] && dayjs(timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
      params.startTime = startTime;
      params.endTime = endTime;
    } else if (taskSource === 2) {
      params.fileUrl = fileUrlRef.current;
    }
    try {
      setLoading(true);
      const res = await createBatchExecuteTask(params);
      if (res?.data?.value) {
        onCancel();
        tableRef.current.fetchData();
        message.success('任务创建成功');
      }
    } finally {
      setLoading(false);
    }
  };

  const request = useCallback(
    async (pagination: any) => {
      try {
        setListLoading(true);
        const res = await queryBatchExecuteTaskList({
          tenantNo: currentTenantNo,
          pageSize: pagination.pageSize,
          pageNum: pagination.current,
        });
        return {
          total: res.data.value?.total,
          data: res.data.value?.list,
        };
      } finally {
        setListLoading(false);
      }
    },
    [currentTenantNo]
  );

  return (
    <PageLayout>
      <div className={styles.container}>
        <Row align="middle" justify="space-between" style={{ marginBottom: 20 }}>
          <Button onClick={() => setVisible(true)} type="primary">
            新建
          </Button>
          <Button
            onClick={() => tableRef.current.fetchData()}
            type="primary"
            ghost
            loading={listLoading}
            icon={<SyncOutlined />}
          >
            刷新
          </Button>
        </Row>
        <EasyTable
          ref={tableRef}
          rowKey={'id'}
          loading={listLoading}
          columns={columns}
          request={request}
          scroll={{ x: 'max-content' }}
        />
        <Modal
          title={'新建批量执行任务'}
          open={visible}
          onCancel={onCancel}
          width={600}
          onOk={onSubmit}
          confirmLoading={loading}
        >
          <Form style={{ marginTop: 24 }} form={form} className={styles.form}>
            <Form.Item label={'任务名称'} name="taskName" style={{ marginLeft: 39 }}>
              <Input placeholder="请输入任务名称" />
            </Form.Item>
            <h1 className={styles.formTitle}>会话范围</h1>
            <Form.Item
              label={'任务来源'}
              name="taskSource"
              initialValue={1}
              rules={[{ required: true, message: '请选择任务来源' }]}
              style={{ marginBottom: 10 }}
            >
              <Radio.Group>
                <Radio value={1}>查询会话开始时间</Radio>
                <Radio value={2}>导入会话ID</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) => prevValues.taskSource !== currentValues.taskSource}
            >
              {({ getFieldValue }) => (
                <>
                  {getFieldValue('taskSource') === 1 && (
                    <>
                      <Form.Item
                        label={'会话开始时间'}
                        name="timeRange"
                        rules={[
                          {
                            required: true,
                            message: '请选择会话开始时间',
                          },
                          ...verifyTimeRulesData,
                        ]}
                        style={{ marginBottom: 10, marginLeft: 0 }}
                      >
                        <RangePicker
                          style={{ width: '100%' }}
                          showTime={{ format: 'HH:mm:ss' }}
                          format="YYYY-MM-DD HH:mm:ss"
                        />
                      </Form.Item>
                      <p style={{ margin: '0 0 24px 108px' }}>
                        共
                        <span style={{ margin: '0 4px', color: antConfig.theme.token.colorPrimary }}>
                          {sessionVolume}
                        </span>
                        条会话
                      </p>
                    </>
                  )}
                  {getFieldValue('taskSource') === 2 && (
                    <>
                      <a
                        style={{ marginLeft: 108 }}
                        href={`${STATIC_URL}/website/cs/ai-puma/template/record_id_template.xlsx`}
                      >
                        下载模板
                      </a>
                      <Form.Item label="" style={{ marginLeft: 80, marginTop: 10 }}>
                        <Upload
                          formProps={{
                            label: '',
                            name: '_fileUrl',
                            rules: verifyFileData,
                          }}
                          ref={uploadRef}
                          hint="导入文件格式需保持与模板保持一致"
                        />
                      </Form.Item>
                    </>
                  )}
                </>
              )}
            </Form.Item>
            <h1 className={styles.formTitle}>执行策略</h1>
            {formColumns
              .filter((v) => ['groupIdList', 'strategyIdList'].includes(v.name))
              .map(({ name, label, type, ...item }) => {
                const options = name === 'strategyIdList' ? transformTreeData(item.treeData) : item.treeData;
                return (
                  <Form.Item
                    key={name}
                    name={name}
                    label={label}
                    rules={[{ required: true, message: `请选择${label}` }]}
                  >
                    <TreeSelect
                      placeholder={`请选择${label}`}
                      {...item}
                      treeData={options}
                      treeCheckable
                      showCheckedStrategy={SHOW_CHILD}
                    />
                  </Form.Item>
                );
              })}
          </Form>
          <p style={{ marginBottom: 24, color: 'red' }}>注意：批量任务可能会影响正常质检，请选择质检业务低峰时间执行</p>
        </Modal>
      </div>
    </PageLayout>
  );
};

export default BatchTest;
