import PageLayout from '@/components/PageLayout';
import { addAiRuleTemplate, deleteAiRuleTemplate, getAiRuleTemplateList, updateAiRuleTemplate } from '@/services/ce';
import { useLocation, useSelector } from '@magi/magi';
import { Button, Dropdown, message, Modal, Row } from 'antd';
import { isEmpty } from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import AiRule from '../strategyDetail/FieldRuleConfig/AiRule';
import styles from './index.scss';

const EMPTY_ID = -1;

const AiTemplate = () => {
  const location: any = useLocation();
  const currentTenantNo = location?.query?.tenantNo;
  const { labelList, riskList } = useSelector((state: { global: any }) => state.global);
  const [currentRule, setCurrentRule] = useState<any>({});
  const [ruleList, setRuleList] = useState<any>([]);
  const aiRuleRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);

  const getList = useCallback(() => {
    getAiRuleTemplateList().then((res) => {
      setRuleList(res.data.value);
    });
  }, []);

  useEffect(() => {
    getList();
  }, []);

  const saveAiRule = async () => {
    const fields = await aiRuleRef.current?.form?.validateFields();
    const api = currentRule.id === EMPTY_ID ? addAiRuleTemplate : updateAiRuleTemplate;
    setLoading(true);
    try {
      const obj = {
        ...currentRule,
        ...fields,
      };
      if (obj.id === EMPTY_ID) {
        delete obj.id;
      }
      await api(obj);
      message.success(obj.id === EMPTY_ID ? '新增成功' : '修改成功');
      if (currentRule.id === EMPTY_ID) {
        setCurrentRule({});
      }
      getList();
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageLayout style={{ backgroundColor: 'unset' }}>
      <div className={styles.container}>
        <div className={styles.menu}>
          <div className={styles.header}>
            <div className={styles.title}>AI规则列表</div>
            <Button
              type="primary"
              ghost
              onClick={() => {
                const obj = { id: EMPTY_ID };
                setCurrentRule(obj);
              }}
            >
              新建规则
            </Button>
          </div>
          <div className={styles.list}>
            {ruleList.map((item: any) => (
              <Dropdown
                key={item.id}
                trigger={['contextMenu']}
                menu={{
                  items: [
                    {
                      key: '1',
                      label: '删除',
                      onClick: () => {
                        Modal.confirm({
                          title: '删除',
                          content: '确定删除该规则吗',
                          onOk: () => {
                            deleteAiRuleTemplate({ ...item }).then(() => {
                              message.success('删除成功');
                              if (currentRule.id === item.id) {
                                setCurrentRule({});
                              }
                              getList();
                            });
                          },
                        });
                      },
                    },
                  ],
                }}
              >
                <div
                  key={item.id}
                  className={`${styles.item} ${currentRule.id === item.id ? styles.active : ''}`}
                  onClick={() => {
                    setCurrentRule(item);
                  }}
                >
                  {item.aiRuleName}
                </div>
              </Dropdown>
            ))}
          </div>
        </div>
        {!isEmpty(currentRule) && (
          <div key={currentRule.id} className={styles.content}>
            <AiRule
              isTemplate
              parentForm={{}}
              parentRowKey={''}
              parentField={{}}
              ruleId={currentRule.id || ''}
              labelList={labelList || []}
              riskList={riskList || []}
              botNo={''}
              strategyId={''}
              ref={aiRuleRef}
              fields={currentRule}
              tenantNo={currentTenantNo}
            />
            <Row className={styles.action}>
              <Button onClick={() => setCurrentRule({})} style={{ marginRight: 8 }}>
                取消
              </Button>
              <Button loading={loading} onClick={saveAiRule} type="primary">
                保存规则
              </Button>
            </Row>
          </div>
        )}
      </div>
    </PageLayout>
  );
};

export default AiTemplate;
