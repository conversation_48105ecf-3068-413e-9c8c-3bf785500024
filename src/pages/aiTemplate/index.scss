.container {
  height: 100%;
  display: flex;
  .menu {
    width: 240px;
    flex-shrink: 0;
    margin-right: 8px;
    padding: 20px;
    height: 100%;
    background-color: #fff;
    border-radius: 16px;
    height: calc(100% - 40px);
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 16px;
      font-weight: bold;
      color: #1d2129;
    }
  }
  .content {
    padding: 20px;
    flex: 1;
    height: calc(100% - 40px);
    background-color: #fff;
    border-radius: 16px;
    position: relative;
  }
  .list {
    margin-top: 16px;
    height: calc(100% - 40px);
    overflow-y: auto;
    margin-bottom: 16px;
    font-size: 14px;
    color: #000000;
  }
  .item {
    cursor: pointer;
    line-height: 14px;
    &:not(:last-child) {
      margin-bottom: 24px;
    }
  }
  .active {
    color: var(--primary-color);
  }
  .action {
    display: flex;
    align-items: center;
    height: 68px;
    position: absolute;
    bottom: 0;
    right: 20px;
  }
}
