.content {
  position: relative;
  z-index: 1;
  padding-top: 110px;
  padding-bottom: 120px;
}
.top {
  display: flex;
  align-items: center;
  justify-content: center;
}
.list {
  display: flex;
  width: 820px;
  flex-wrap: wrap;
  margin: 0 auto;
  margin-top: 64px;
  overflow-y: auto;
  max-height: calc(100vh - 110px - 120px - 67px - 64px);
}
.itemContent {
  margin-top: 8px;
  max-width: 60%;
  overflow: hidden;
}
.item {
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
  width: 380px;
  height: 100px;
  display: flex;
  align-items: center;
  border-radius: 16px;
  margin-right: 20px;
  margin-bottom: 12px;
  box-shadow: 0px 4px 4px 0px rgba(17, 28, 72, 1) inset;
  background: linear-gradient(90deg, #0a1542 0%, rgba(23, 32, 67, 0.9) 100%);
  position: relative;
}
.itemBg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  bottom: 0;
  right: 0;
}
.right {
  position: absolute;
  right: 16px;
}
.botName {
  font-size: 18px;
  color: #fff;
  font-weight: 600;
  white-space: nowrap;
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.botDesc {
  font-size: 14px;
  line-height: 22px;
  color: rgba(255, 255, 255, 0.55);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.itemImg {
  width: 60px;
  height: 60px;
  margin-left: 20px;
  margin-right: 16px;
}
.title {
  font-size: 48px;
  background: linear-gradient(150.03deg, #7052f0 8.76%, #31d7fc 67.4%, #24ffb0 95.8%);

  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 600;
}
.container {
  position: relative;
  height: 100vh;
  width: 100vw;
}
.bg {
  height: 100vh;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
