import { getFirstPage } from '@/components/LeftNav';
import { getRobotList } from '@/services/ce';
import { history, useDispatch, useSelector } from '@magi/magi';
import React, { useCallback, useEffect, useState } from 'react';
import BG from '../../assets/bg.png';
import ItemBg from '../../assets/itemBg.svg';
import Logo from '../../assets/logo.png';
import Right from '../../assets/right.svg';
import styles from './index.scss';

interface IRobot {
  botName: string;
  botNo: string;
  status: string;
  isRedLine?: string;
}
const Landing = () => {
  const { isStrategy } = useSelector((state: { global: any }) => state.global);
  const [list, setList] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    getRobotList().then((res: any) => {
      if (res.data.success) {
        setList(res.data.value);
      }
    });
  }, []);

  const handleClick = useCallback(
    (record: IRobot) => {
      dispatch({
        type: 'global/save',
        payload: {
          currentTenantNo: record.botNo,
        },
      });
      history.push(getFirstPage({ isRedLine: record.isRedLine, tenantNo: record.botNo, isStrategy }));
    },
    [dispatch, isStrategy]
  );

  return (
    <div className={styles.container}>
      <img src={BG} className={styles.bg} />
      <div className={styles.content}>
        <div className={styles.top}>
          <img style={{ width: 62, marginRight: 8 }} src={Logo} />
          <div className={styles.title}>灵眸AI质检</div>
        </div>
        <div className={styles.list}>
          {list.map((item: any) => {
            return (
              <div onClick={() => handleClick(item)} className={styles.item} key={item.botNo}>
                <img className={styles.itemBg} src={ItemBg} />
                <img src={item?.icon?.iconURL} className={styles.itemImg} />
                <div className={styles.itemContent}>
                  <div className={styles.botName}>{item.botName}</div>
                  <div className={styles.botDesc}>{item.description}</div>
                </div>
                <img className={styles.right} src={Right} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Landing;
