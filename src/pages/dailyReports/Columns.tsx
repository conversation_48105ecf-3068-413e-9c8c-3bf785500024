import IconBusiness from '@/assets/icon-business.svg';
import IconClose from '@/assets/icon-close.svg';
import IconFile from '@/assets/icon-file.svg';
import IconPhone from '@/assets/icon-phone.svg';
import IconWarn from '@/assets/icon-warn.svg';
import { queryQualityStatistics } from '@/services/ce';
import { useSelector } from '@magi/magi';
import { Col, Row, Statistic, StatisticProps, Typography } from 'antd';
import Big from 'big.js';
import classnames from 'classnames';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import CountUp from 'react-countup';
import ColumnList from './ColumnList';
import { getTime, MAX_COLUMNS_LEN } from './index';
import styles from './index.scss';

const formatter: StatisticProps['formatter'] = (value) => <CountUp end={Number(value || 0)} separator="," />;

interface IProps {
  tenantNo?: string;
  searchData?: { [key: string]: any };
  columnsData?: { [key: string]: any };
  setDrawerData: React.Dispatch<
    React.SetStateAction<{
      open: boolean;
      data: null;
    }>
  >;
}

const Columns: FC<IProps> = ({ tenantNo, searchData, columnsData, setDrawerData }) => {
  const { whitelistPermission } = useSelector((state: { global: any }) => state.global);
  const [statistics, setStatistics] = useState<{ [key: string]: number }>();

  // 展示权限
  const isNoViolation = useMemo(() => {
    const { noViolation } = whitelistPermission || {};
    return noViolation?.includes(tenantNo);
  }, [tenantNo, whitelistPermission]);

  const handleQueryQualityStatistics = useCallback(async () => {
    if (!tenantNo || !searchData?.time?.length) return;
    const res = await queryQualityStatistics({ tenantNo, ...getTime(searchData?.time, searchData?.timeType) });
    setStatistics(res?.data?.value);
  }, [searchData, tenantNo]);

  useEffect(() => {
    handleQueryQualityStatistics();
  }, [handleQueryQualityStatistics]);

  const dataList = useMemo(() => {
    const { sessionCount, businessCount, qualitySuccessCount, violationCount, violationNumber } = statistics || {};
    return [
      {
        label: '会话量',
        num: sessionCount,
        icon: IconPhone,
      },
      {
        label: '商机量',
        num: businessCount,
        icon: IconBusiness,
      },
      {
        label: '质检成功量',
        num: qualitySuccessCount,
        icon: IconFile,
      },
      {
        label: '违规会话量',
        num: violationCount,
        icon: IconWarn,
      },
      {
        label: '违规次数',
        num: violationNumber,
        icon: IconClose,
      },
    ];
  }, [statistics]);

  const getColumnProps = useCallback(
    (data: any[], noType?: boolean) => {
      const getName = (name?: string) => data?.find((item) => item.id === name)?.name || name;
      const getValue = (val?: number) =>
        Big(val || 0)
          .times(100)
          .toString() + '%';

      const commonData = {
        ...(searchData?.sessionTypes?.length && !noType
          ? {
              tooltip: {
                showTitle: true,
                title: (title: string) => getName(title),
                formatter: (datum: any) => {
                  return { name: datum.type, value: datum.value || 0 };
                },
              },
              isGroup: true,
              seriesField: 'type',
              legend: { offsetY: 5 },
            }
          : {
              tooltip: {
                showTitle: false,
                formatter: (datum: any) => {
                  return { name: getName(datum.id), value: datum.value || 0 };
                },
              },
            }),
        color: ({ type }: { type?: string }) => {
          if (type === '在线') return '#17B26A';
          if (type === '企微') return '#7A5AF8';
          return '#7094F3';
        },
        xAxis: {
          label: {
            formatter: (text: string) => {
              const str = getName(text);
              return str?.length > 8 && !noType ? str?.slice(0, 8) + '...' : str;
            },
          },
        },
      };
      const scaleData = {
        ...commonData,
        tooltip: {
          ...commonData.tooltip,
          formatter: (datum: any) => {
            return {
              name: searchData?.sessionTypes?.length && !noType ? datum.type : getName(datum.id),
              value: getValue(datum.value),
            };
          },
        },
        meta: {
          value: {
            formatter: (v: number) => getValue(v),
          },
        },
      };
      return { commonData, scaleData };
    },
    [searchData]
  );

  const handleColumnProps = useCallback(
    (list: any[], type: 'commonData' | 'scaleData', noType?: boolean) => {
      const newList = list?.map(({ id, ...item }) => ({ ...item, id: id?.toString() }));
      return {
        data: newList,
        ...(getColumnProps(newList, noType)?.[type] || {}),
      };
    },
    [getColumnProps]
  );

  const columnList = useMemo(() => {
    const {
      groupList,
      strategyList,
      ruleList,
      ruleAccuracyList,
      ruleTagAccuracyList,
      seatNameList,
      businessViolationList,
      sessionAccuracyList,
    } = columnsData || {};
    const list = [
      {
        title: '策略分组命中次数',
        tip: '表示有命中任意该分组下策略的会话总量',
        hidden: searchData?.groupIdList?.length && searchData?.strategyIdList?.length,
        ...handleColumnProps(
          groupList?.map((item: any) => ({
            ...item,
            name: item.groupName,
            value: item.groupCount,
            id: item.groupId,
          })),
          'commonData'
        ),
      },
      {
        title: '策略命中次数',
        tip: '表示有命中该策略的会话总量',
        ...handleColumnProps(
          strategyList?.map((item: any) => ({
            ...item,
            name: item.strategyName,
            value: item.strategyCount,
            id: item.strategyId,
          })),
          'commonData'
        ),
      },
      {
        title: '规则命中次数',
        tip: '表示有命中该规则的会话总量',
        ...handleColumnProps(
          ruleList?.map((item: any) => ({
            ...item,
            name: item.ruleName,
            value: item.ruleCount,
            id: item.ruleId,
          })),
          'commonData'
        ),
      },
      {
        title: '规则违规率',
        tip: '规则违规率=违规量/会话量',
        ...handleColumnProps(
          ruleList
            ?.sort((a: any, b: any) => (a?.violationRate > b?.violationRate ? -1 : 1))
            ?.map((item: any) => ({
              ...item,
              _molecule: item.ruleCount,
              _denominator: item.sessionCount,
              name: item.ruleName,
              value: Number(
                Big(item.violationRate?.replace('%', '') || 0)
                  .div(100)
                  .toString()
              ),
              id: item.ruleId,
            })),
          'scaleData'
        ),
      },
      {
        title: '商机违规量',
        tip: '取商机内多个会话违规的总体结果，如：同商机下多个会话多次违反同一规则进行去重，如果先误导后澄清则降级为不规范',
        ...handleColumnProps(
          businessViolationList?.map((item: any) => ({
            ...item,
            name: item.ruleName,
            value: item.businessViolationCount,
            id: item.ruleId,
          })),
          'commonData'
        ),
      },
      {
        title: '商机违规率',
        tip: '商机违规率=商机违规量/商机量',
        ...handleColumnProps(
          businessViolationList
            ?.sort((a: any, b: any) => (a?.businessViolationAccuracy > b?.businessViolationAccuracy ? -1 : 1))
            ?.map((item: any) => ({
              ...item,
              _molecule: item.businessViolationMolecule,
              _denominator: item.businessViolationDenominator,
              name: item.ruleName,
              value: item.businessViolationAccuracy,
              id: item.ruleId,
            })),
          'scaleData'
        ),
      },
      {
        title: '坐席违规次数',
        tip: '表示该坐席违规总次数，一个会话违规多个规则统计多次',
        ...handleColumnProps(
          seatNameList?.map((item: any) => ({
            ...item,
            name: item.seatName,
            value: item.count,
            id: item.seatName,
          })),
          'commonData'
        ),
      },
      {
        title: '规则精确率',
        tip: '规则精确率=(该规则命中次数-标注错误量)/规则命中次数',
        ...handleColumnProps(
          ruleAccuracyList?.map((item: any) => ({
            ...item,
            _molecule: item.molecule,
            _denominator: item.denominator,
            name: item.ruleName,
            value: item.ruleAccuracy,
            id: item.ruleId,
          })),
          'scaleData'
        ),
      },
      {
        title: '规则标注精确率',
        tip: '标注精确率=标注正确量/(标注正确量+标注错误量)',
        ...handleColumnProps(
          ruleTagAccuracyList?.map((item: any) => ({
            ...item,
            _molecule: item.moleculeTag,
            _denominator: item.denominatorTag,
            name: item.ruleName,
            value: item.ruleAccuracy,
            id: item.ruleId,
          })),
          'scaleData'
        ),
      },
    ];
    isNoViolation &&
      list.push({
        title: '会话准确率',
        tip: '会话准确率=(有违规的会话中任意规则人工标注为“正确”+无违规的会话中人工标注为“正确”)/人工标注的会话总量',
        ...handleColumnProps(
          sessionAccuracyList
            ?.map((item: any) => ({
              ...item,
              _molecule: item.tagSuccessCount,
              _denominator: item.sessionTagCount,
              name: item.exactDate,
              value: item.sessionAccuracy,
              id: item.exactDate,
            }))
            .sort((a: any, b: any) => (a?.exactDate > b?.exactDate ? 1 : -1)),
          'scaleData',
          true
        ),
      });
    return list;
  }, [columnsData, searchData, isNoViolation, handleColumnProps]);

  const getColumns = useCallback(
    (index: number): any[] => {
      const list: any[] = [
        { name: '策略分组名称', value: '命中次数' },
        { name: '策略名称', value: '命中次数' },
        { name: '规则名称', value: '命中次数' },
        { name: '规则名称', value: '违规率' },
        { name: '规则名称', value: '违规量' },
        { name: '规则名称', value: '违规率' },
        { name: '坐席姓名', value: '违规次数' },
        { name: '规则名称', value: '精确率' },
        { name: '规则标注名称', value: '精确率' },
      ];
      isNoViolation && list.push({ name: '时间', value: '准确率', noType: true });
      let data: any[] = [
        {
          title: list[index].name,
          dataIndex: 'name',
          width: 120,
          render: (text: string) => <span style={{ wordBreak: 'break-all' }}>{text}</span>,
        },
        {
          title: list[index].value,
          dataIndex: 'value',
          className: styles.tableItemValue,
          render: (text: number, record: any) => {
            const textStr =
              list[index].value.indexOf('率') !== -1
                ? Big(text || 0)
                    .times(100)
                    .toString() +
                  '%' +
                  `(${record._molecule ?? 0}/${record._denominator ?? 0})`
                : text;
            return (
              <span style={{ textAlign: 'center' }}>
                <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 1, tooltip: textStr }}>
                  {textStr}
                </Typography.Paragraph>
              </span>
            );
          },
        },
      ];
      if (searchData?.sessionTypes?.length && !list[index].noType) {
        data = [
          {
            title: '渠道',
            dataIndex: 'type',
            width: 60,
          },
          ...data,
        ];
      }
      return data;
    },
    [searchData, isNoViolation]
  );

  return (
    <>
      <Row gutter={24} className={styles.dataList}>
        {dataList.map(({ label, num, icon }) => (
          <Col key={label}>
            <div className={styles.dataListItem}>
              <img src={icon} />
              <div className={styles.dataListItemText}>
                <Statistic value={num} formatter={formatter} />
                <span>{label}</span>
              </div>
            </div>
          </Col>
        ))}
      </Row>
      <ColumnList
        columnList={columnList}
        popoverClassName={classnames({
          [styles.popoverLong]: !!searchData?.sessionTypes?.length,
        })}
        setDrawerData={setDrawerData}
        getColumns={getColumns}
        maxCountLen={MAX_COLUMNS_LEN}
      />
    </>
  );
};

export default Columns;
