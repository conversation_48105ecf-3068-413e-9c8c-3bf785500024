import { Column, Mix } from '@/components/Charts';
import Table from '@/components/Table';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Col, Popover, Row, Tooltip } from 'antd';
import classnames from 'classnames';
import React, { FC, useCallback, useState } from 'react';
import { MAX_COUNT_VALUE } from './index';
import styles from './index.scss';

interface IProps {
  columnList: any[];
  popoverClassName?: string;
  setDrawerData?: ({ open, data }: { open: boolean; data: any }) => void;
  getColumns: (index: number) => any[];
  maxCountLen?: number;
}

const ColumnList: FC<IProps> = ({ columnList, popoverClassName, setDrawerData, getColumns, maxCountLen }) => {
  const [opens, setOpens] = useState<{ [index: number]: boolean }>({});

  const handleOpenChange = useCallback((newOpen: boolean, index: number) => {
    setOpens({ [index]: newOpen });
  }, []);

  const getColumnData = useCallback(
    (index: number, queryType?: string): any[] => {
      let data: any[] = [
        ...getColumns(index),
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 50,
          render: (_: unknown, record: any) => (
            <Button
              type="link"
              style={{ padding: 0, height: 'auto' }}
              disabled={record.value > MAX_COUNT_VALUE}
              onClick={() => {
                setOpens({});
                setDrawerData?.({ open: true, data: { ...record, queryType } });
              }}
            >
              查看
            </Button>
          ),
        },
      ];
      return data;
    },
    [setDrawerData, getColumns]
  );

  return (
    <Row gutter={24} className={styles.columnsBox}>
      {columnList.map(
        (
          { title, tip, data, hidden, isMix, columnConfig, lineConfig, tooltip, queryType, ...otherData }: any,
          index: number
        ) => (
          <Col xl={12} lg={24} key={title} style={hidden ? { display: 'none' } : {}}>
            <div className={styles.columnsItem}>
              <div className={styles.header}>
                <h1>
                  {title}
                  {!!tip && (
                    <Tooltip title={tip}>
                      <InfoCircleOutlined />
                    </Tooltip>
                  )}
                </h1>
                {!!data?.length && !!setDrawerData && (
                  <Popover
                    rootClassName={classnames(
                      `popover-${getColumnData(index)?.length}`,
                      styles.popover,
                      popoverClassName
                    )}
                    content={
                      <Table
                        virtual
                        rowKey={'id'}
                        className={styles.popoverTable}
                        size="small"
                        columns={getColumnData(index, queryType)}
                        dataSource={data}
                        pagination={false}
                        scroll={{ y: 300 }}
                      />
                    }
                    placement="left"
                    trigger="click"
                    open={opens[index]}
                    onOpenChange={(newOpen) => handleOpenChange(newOpen, index)}
                  >
                    <Button type="link" className={styles.btn}>
                      详情
                    </Button>
                  </Popover>
                )}
              </div>
              {isMix ? (
                <Mix tooltip={tooltip} columnConfig={columnConfig} lineConfig={lineConfig} {...otherData} />
              ) : (
                <Column
                  xField="id"
                  yField="value"
                  data={[...(data || [])].filter((item: any, index: number) =>
                    typeof item.isCol === 'boolean'
                      ? item.isCol
                      : typeof maxCountLen === 'number'
                        ? index < maxCountLen
                        : true
                  )}
                  tooltip={tooltip}
                  {...otherData}
                />
              )}
            </div>
          </Col>
        )
      )}
    </Row>
  );
};

export default ColumnList;
