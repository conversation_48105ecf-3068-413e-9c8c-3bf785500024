.container {
  padding: 20px;

  .operateBox {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      margin: 0;
    }

    .segmented {
      padding: 4px 0;

      :global {
        .ant-segmented-group {
          .ant-segmented-item {
            color: #86909c;
            margin: 0 4px;

            &:hover {
              color: var(--primary-color);
            }

            &.ant-segmented-item-selected {
              color: var(--primary-color);
              box-shadow: 0px 1px 2px 0px #d0d0d0;
            }

            .ant-segmented-item-label {
              width: 30px;
              height: 30px;
              padding: 0;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }
    }
  }
}

.tableItemValue {
  text-align: center !important;
}

.dataList {
  padding-top: 17px;
  display: flex;
  flex-wrap: wrap;

  :global {
    .ant-col {
      flex: 1 1 20%;
      max-width: 20%;
    }
  }

  .dataListItem {
    background: #f8f8f8;
    border-radius: 6px;
    padding: 0 12px;
    min-height: 92px;
    margin-bottom: 1px;
    display: flex;
    align-items: center;
    overflow: hidden;

    img {
      width: 40px;
      height: 40px;
      margin-right: 21px;
    }

    .dataListItemText {
      display: flex;
      flex-direction: column;

      :global {
        .ant-statistic {
          line-height: 32px;
          .ant-statistic-content-value {
            font-size: 28px;
            line-height: 32px;
            font-weight: 700;
            color: #1d2129;
            font-family: 'DIN Alternate';
            word-break: break-all;
          }
        }
      }

      & > span {
        font-size: 14px;
        color: #4e5969;
        word-break: break-all;
      }
    }
  }
}

.columnsBox {
  :global {
    .ant-col {
      padding: 15px 10px !important;
    }
  }
  .columnsItem {
    border: 1px solid #d0d5dd;
    border-radius: 6px;
    padding: 16px;
    box-sizing: border-box;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;

      h1 {
        font-size: 14px;
        color: #1d2129;
        margin: 0;
        line-height: 14px;

        :global {
          .anticon {
            color: #86909c;
            margin-left: 10px;
            cursor: pointer;
          }
        }
      }

      .btn {
        font-size: 12px;
        color: #4e5969;
        padding: 0;
        height: inherit;
        line-height: 12px;
      }
    }
  }
}

.popover {
  :global {
    .ant-popover-content {
      width: 320px;

      .ant-popover-inner {
        padding: 0;
        overflow: hidden;
      }
    }
  }

  &.popoverLong {
    :global {
      .ant-popover-content {
        width: 380px;
      }
    }
  }

  .popoverTable {
    :global {
      .ant-table-small {
        .ant-table-container {
          .ant-table-header .ant-table-cell,
          .ant-table-body .ant-table-cell {
            padding-top: 4px;
            padding-bottom: 4px;
          }
        }
      }

      .ant-table-placeholder {
        .ant-table-cell {
          border-bottom: none;
        }
      }

      .ant-table-tbody {
        .ant-table-row:last-child {
          .ant-table-cell {
            border-bottom: none;
          }
        }
      }
    }
  }
}
