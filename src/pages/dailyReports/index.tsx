import { ReactComponent as <PERSON><PERSON><PERSON> } from '@/assets/barChart.svg';
import { ReactComponent as IconExport } from '@/assets/export.svg';
import { ReactComponent as Tabulation } from '@/assets/tabulation.svg';
import Form, { useForm } from '@/components/Form';
import PageLayout from '@/components/PageLayout';
import Table from '@/components/Table';
import useStrategyRuleSearch, { initialValues } from '@/hooks/useStrategyRuleSearch';
import HitResultDrawer from '@/pages/hitResult/HitResultDrawer';
import { PREFIX } from '@/services';
import { queryQualityDetailsStatistics } from '@/services/ce';
import { download } from '@/utils';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useLocation } from '@magi/magi';
import { Button, Divider, Segmented, Spin, Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import Columns from './Columns';
import styles from './index.scss';

export const MAX_COLUMNS_LEN = 10;
export const MAX_COUNT_VALUE = 10000;

export const getTime = (time: Dayjs[], timeType?: string) => {
  let [startTime, endTime]: (Dayjs | string)[] = time || [];
  startTime && (startTime = `${dayjs(startTime).format('YYYY-MM-DD')} 00:00:00`);
  endTime && (endTime = `${dayjs(endTime).format('YYYY-MM-DD')} 23:59:59`);
  return timeType === '2'
    ? {
        qualityStartTime: startTime && Date.parse(dayjs(startTime) as any),
        qualityEndTime: endTime && Date.parse(dayjs(endTime) as any),
      }
    : { startTime, endTime };
};

const DailyReports = () => {
  const [form] = useForm();
  const location: any = useLocation();
  const { tenantNo: currentTenantNo } = location.query;
  const { columns: strategyRuleSearchList } = useStrategyRuleSearch({
    form,
    tenantNo: currentTenantNo,
    showColumns: ['time', 'session'],
    isTimeType: true,
  });
  const [columnsData, setColumnsData] = useState<{ [key: string]: any }>({});
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [type, setType] = useState('barChart');
  const [drawerData, setDrawerData] = useState({ open: false, data: null });
  const [searchData, setSearchData] = useState<{ [key: string]: any }>();

  const tableColumns = useMemo<any[]>(() => {
    let data = [
      {
        title: '策略分组',
        dataIndex: 'groupName',
      },
      {
        title: '策略分组命中次数',
        dataIndex: 'groupCount',
      },
      {
        title: '策略名称',
        dataIndex: 'strategyName',
      },
      {
        title: '策略命中次数',
        dataIndex: 'strategyCount',
      },
      {
        title: '规则名称',
        dataIndex: 'ruleName',
      },
      {
        title: '规则命中次数',
        dataIndex: 'ruleCount',
      },
      {
        title: '规则违规率',
        dataIndex: 'violationRate',
        render: (text: string, record: any) => `${text ?? ''}(${record.ruleCount ?? 0}/${record.sessionCount ?? 0})`,
      },
      // {
      //   title: '商机违规量',
      //   dataIndex: 'businessViolationCount',
      // },
      // {
      //   title: '商机违规率',
      //   dataIndex: 'businessViolationPercent',
      //   render: (text: string, record: any) =>
      //     `${text ?? ''}(${record.businessViolationMolecule ?? 0}/${record.businessViolationDenominator ?? 0})`,
      // },
      {
        title: '规则精确率',
        dataIndex: 'ruleAccuracyPercent',
        render: (text: string, record: any) => `${text ?? ''}(${record.molecule ?? 0}/${record.denominator ?? 0})`,
      },
      {
        title: '规则标注精确率',
        dataIndex: 'ruleTagAccuracyPercent',
        render: (text: string, record: any) =>
          `${text ?? ''}(${record.moleculeTag ?? 0}/${record.denominatorTag ?? 0})`,
      },
      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        render: (_: unknown, record: any) => (
          <Button
            style={{ padding: 0 }}
            type="link"
            disabled={
              record.groupCount > MAX_COUNT_VALUE ||
              record.strategyCount > MAX_COUNT_VALUE ||
              record.ruleCount > MAX_COUNT_VALUE
            }
            onClick={() => setDrawerData({ open: true, data: record })}
          >
            查看
          </Button>
        ),
      },
    ];
    if (searchData?.sessionTypes?.length) {
      data = [
        {
          title: '渠道',
          dataIndex: 'sessionType',
        },
        ...data,
      ];
    }
    return data;
  }, [searchData]);

  const handleQueryQualityDetailsStatistics = async (params: any) => {
    const { timeType, time, sessionTypes, ...otherParams } = params || {};
    if (!currentTenantNo || !time?.length) return;
    const res = await queryQualityDetailsStatistics({
      tenantNo: currentTenantNo,
      sessionTypes,
      ...getTime(time, timeType),
      ...otherParams,
    });
    const {
      // 全部
      groupList,
      strategyList,
      ruleList,
      ruleAccuracyList,
      ruleTagAccuracyList,
      seatNameList,
      businessViolationList,
      sessionAccuracyList,
      // 电话
      groupPhoneList,
      strategyPhoneList,
      rulePhoneList,
      ruleAccuracyPhoneList,
      ruleTagAccuracyPhoneList,
      seatNamePhoneList,
      businessPhoneViolationList,
      // 在线
      groupOnlineList,
      strategyOnlineList,
      ruleOnlineList,
      ruleAccuracyOnlineList,
      ruleTagAccuracyOnlineList,
      seatNameOnlineList,
      businessOnlineViolationList,
      // 企微
      groupWechatList,
      strategyWechatList,
      ruleWechatList,
      ruleAccuracyWechatList,
      ruleTagAccuracyWechatList,
      seatNameWechatList,
      businessWechatViolationList,
    } = res?.data?.value || {};
    let [startTime, endTime]: (Dayjs | string)[] = time || [];
    startTime && (startTime = dayjs(startTime).format('YYYY-MM-DD'));
    endTime && (endTime = dayjs(endTime).format('YYYY-MM-DD'));
    const newSessionAccuracyList = sessionAccuracyList?.map((item: any) => ({
      ...item,
      exactDate: `${startTime}~${endTime}`,
    }));
    if (!sessionTypes?.length) {
      setColumnsData({
        groupList,
        strategyList,
        ruleList,
        ruleAccuracyList,
        ruleTagAccuracyList,
        seatNameList,
        businessViolationList,
        sessionAccuracyList: newSessionAccuracyList,
      });
      setTableData(ruleList);
    } else {
      let newGroupList: any[] = [],
        newStrategyList: any[] = [],
        newRuleList: any[] = [],
        newRuleAccuracyList: any[] = [],
        newRuleTagAccuracyList: any[] = [],
        newSeatNameList: any[] = [],
        newBusinessViolationList: any[] = [];
      const processSessionType = (
        sessionType: string,
        gList?: any[],
        sList?: any[],
        rList?: any[],
        rAList?: any[],
        rTAList?: any[],
        seatList?: any[],
        businessList?: any[]
      ) => {
        if (sessionTypes?.includes(sessionType)) {
          const commonData = (item: any, index: number) => ({
            ...item,
            type: sessionType,
            isCol: index < MAX_COLUMNS_LEN,
          });
          newGroupList = [...newGroupList, ...(gList || []).map(commonData)];
          newStrategyList = [...newStrategyList, ...(sList || []).map(commonData)];
          newRuleList = [...newRuleList, ...(rList || []).map(commonData)];
          newRuleAccuracyList = [...newRuleAccuracyList, ...(rAList || []).map(commonData)];
          newRuleTagAccuracyList = [...newRuleTagAccuracyList, ...(rTAList || []).map(commonData)];
          newSeatNameList = [...newSeatNameList, ...(seatList || []).map(commonData)];
          newBusinessViolationList = [...newBusinessViolationList, ...(businessList || []).map(commonData)];
        }
      };
      processSessionType(
        '电话',
        groupPhoneList,
        strategyPhoneList,
        rulePhoneList,
        ruleAccuracyPhoneList,
        ruleTagAccuracyPhoneList,
        seatNamePhoneList,
        businessPhoneViolationList
      );
      processSessionType(
        '在线',
        groupOnlineList,
        strategyOnlineList,
        ruleOnlineList,
        ruleAccuracyOnlineList,
        ruleTagAccuracyOnlineList,
        seatNameOnlineList,
        businessOnlineViolationList
      );
      processSessionType(
        '企微',
        groupWechatList,
        strategyWechatList,
        ruleWechatList,
        ruleAccuracyWechatList,
        ruleTagAccuracyWechatList,
        seatNameWechatList,
        businessWechatViolationList
      );
      setTableData(newRuleList);
      setColumnsData({
        groupList: newGroupList,
        strategyList: newStrategyList,
        ruleList: newRuleList,
        ruleAccuracyList: newRuleAccuracyList,
        ruleTagAccuracyList: newRuleTagAccuracyList,
        seatNameList: newSeatNameList,
        businessViolationList: newBusinessViolationList,
        sessionAccuracyList: newSessionAccuracyList,
      });
    }
  };

  const handleQuery = async () => {
    try {
      const values = await form.validateFields();
      setSearchData(values);
      setLoading(true);
      await handleQueryQualityDetailsStatistics(values);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleQuery();
  }, []);

  const exportResult = () => {
    const { time, timeType, ...values } = searchData || {};
    setDownloadLoading(true);
    download(
      `${PREFIX}/api/v1/dailyReport/exportDailyReport`,
      {
        tenantNo: currentTenantNo,
        ...values,
        ...getTime(time, timeType),
      },
      `报表结果.xlsx`
    ).finally(() => {
      setDownloadLoading(false);
    });
  };

  return (
    <PageLayout>
      <div className={styles.container}>
        <Form
          form={form}
          initialValues={initialValues}
          columns={strategyRuleSearchList}
          onQuery={handleQuery}
          onReset={handleQuery}
          loading={loading}
        />
        <Divider style={{ margin: '0 0 17px' }} />
        <div className={styles.operateBox}>
          {type === 'barChart' && (
            <h1 className={styles.title}>
              数据总览
              <Tooltip title={'当前选中日期范围内的数据总览'}>
                <InfoCircleOutlined style={{ marginLeft: 10 }} />
              </Tooltip>
            </h1>
          )}
          {type === 'tabulation' && (
            <Button
              type="primary"
              ghost
              icon={<IconExport style={{ marginTop: 1 }} />}
              loading={downloadLoading}
              onClick={exportResult}
            >
              导出
            </Button>
          )}
          <Segmented
            className={styles.segmented}
            options={[
              {
                label: <BarChart />,
                value: 'barChart',
              },
              {
                label: <Tabulation />,
                value: 'tabulation',
              },
            ]}
            value={type}
            onChange={(value) => setType(value)}
          />
        </div>
        <Spin spinning={loading}>
          {type === 'barChart' && (
            <Columns
              searchData={searchData}
              tenantNo={currentTenantNo}
              columnsData={columnsData}
              setDrawerData={setDrawerData}
            />
          )}
          {type === 'tabulation' && (
            <Table
              rowKey={(record) => `${record.strategyId}-${record.groupId}-${record.ruleId}-${record.sessionType || ''}`}
              style={{ marginTop: 20 }}
              columns={tableColumns}
              dataSource={tableData}
              pagination={false}
            />
          )}
        </Spin>
      </div>
      <HitResultDrawer
        drawerData={drawerData}
        setDrawerData={setDrawerData}
        currentTenantNo={currentTenantNo}
        searchData={searchData}
      />
    </PageLayout>
  );
};

export default DailyReports;
