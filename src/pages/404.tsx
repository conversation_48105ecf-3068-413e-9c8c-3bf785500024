/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-08-03 22:31:06
 * @Descripttion:
 * @LastEditors: xuyang <EMAIL>
 * @LastEditTime: 2022-08-10 11:35:42
 * @FilePath: /micro-services-static/src/pages/404.tsx
 * Copyright (c) 2022 by ZA-客户服务与体验中心, All Rights Reserved.
 */
import { history } from '@magi/magi';
import { Button, Result } from 'antd';
import React from 'react';

const NoFoundPage = () => (
  <Result
    status="404"
    title="404"
    subTitle="Sorry, the page you visited does not exist."
    extra={
      <Button type="primary" onClick={() => history.replace('/')}>
        Back Home
      </Button>
    }
  />
);

export default NoFoundPage;
