import { Pagination } from '@/components/EasyTable';
import PageLayout from '@/components/PageLayout';
import Table from '@/components/Table';
import RuleOptimizeDrawer from '@/pages/ruleOptimizeDetail/RuleOptimizeDrawer';
import { deleteRuleOptimizeTask, queryRuleOptimizeTaskList } from '@/services/ce';
import { SyncOutlined } from '@ant-design/icons';
import { useLocation } from '@magi/magi';
import { Button, Popconfirm, Row, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import AddModal from './AddModal';
import styles from './index.scss';

const STATUS_ENUM = {
  0: '未执行',
  1: '执行中',
  2: '已完成',
};

const RuleOptimize = () => {
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [visible, setVisible] = useState(false);
  const [ruleOptimizeData, setRuleOptimizeData] = useState({ visible, id: undefined });
  const location: any = useLocation();
  const { tenantNo: currentTenantNo } = location.query;

  const handleQuery = async (pagination: any) => {
    setLoading(true);
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      tenantNo: currentTenantNo,
    };
    const res = await queryRuleOptimizeTaskList(params);
    if (res.data.success) {
      const data = res.data.value;
      const { list = [], total } = data || {};
      setData(list);
      setTotal(total);
    }
    setLoading(false);
  };

  useEffect(() => {
    handleQuery(pagination);
  }, [pagination]);

  const refreshList = () => setPagination((preState) => ({ ...preState, current: 1 }));

  const deleteData = async (id: number) => {
    const res = await deleteRuleOptimizeTask(id);
    if (res?.data?.value) {
      refreshList();
    }
  };

  const columns = useMemo<any[]>(() => {
    return [
      {
        title: '任务ID',
        dataIndex: 'id',
        width: 200,
      },
      {
        title: '规则名称',
        dataIndex: 'ruleName',
        width: 250,
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        width: 120,
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreated',
        width: 180,
        render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        render: (text: keyof typeof STATUS_ENUM) =>
          !!STATUS_ENUM[text] && (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span className={styles.statusPoint} style={{ background: text === 2 ? '#27AE60' : '#FFB800' }}></span>
              {STATUS_ENUM[text]}
            </div>
          ),
      },
      {
        title: '优化结论',
        dataIndex: 'conclusion',
        width: 100,
        render: (text: string) =>
          !!text && (
            <Tag className={styles.tag} color={text === '采纳' ? 'success' : 'error'}>
              {text}
            </Tag>
          ),
      },
      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        width: 120,
        render: (_: any, record: any) => {
          return (
            <div>
              <Button
                style={{ marginRight: 20, padding: 0 }}
                onClick={() => setRuleOptimizeData({ visible: true, id: record.id })}
                type="link"
              >
                查看
              </Button>
              {record.status === 2 && (
                <Popconfirm title="确定删除吗？" onConfirm={() => deleteData(record.id)}>
                  <Button type="link" danger style={{ padding: 0 }}>
                    删除
                  </Button>
                </Popconfirm>
              )}
            </div>
          );
        },
      },
    ];
  }, []);

  return (
    <PageLayout>
      <div className={styles.container}>
        <Row align="middle" justify="space-between">
          <Button type="primary" onClick={() => setVisible(true)}>
            新建优化任务
          </Button>
          <Button
            onClick={() => handleQuery(pagination)}
            type="primary"
            ghost
            loading={loading}
            icon={<SyncOutlined />}
          >
            刷新
          </Button>
        </Row>
        <Table
          rowKey={'id'}
          loading={loading}
          style={{ marginTop: 20 }}
          pagination={{
            total,
            current: pagination.current,
            pageSize: pagination.pageSize,
          }}
          onChange={(newPagination) => {
            setPagination({
              current: newPagination.pageSize !== pagination.pageSize ? 1 : newPagination.current || 1,
              pageSize: newPagination.pageSize || 10,
            });
          }}
          columns={columns}
          dataSource={data}
        />
      </div>
      <AddModal setVisible={setVisible} visible={visible} tenantNo={currentTenantNo} refreshList={refreshList} />
      <RuleOptimizeDrawer
        {...ruleOptimizeData}
        onCancel={() => setRuleOptimizeData({ visible: false, id: undefined })}
      />
    </PageLayout>
  );
};

export default RuleOptimize;
