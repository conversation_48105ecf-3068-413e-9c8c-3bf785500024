import Upload from '@/components/Upload';
import { createRuleOptimizeTask, uploadExample } from '@/services/ce';
import { Form, Input, message, Modal } from 'antd';
import { isEmpty } from 'lodash';
import React, { FC, useEffect, useRef, useState } from 'react';

interface IProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  tenantNo: string;
  refreshList: () => void;
}

const AddModal: FC<IProps> = ({ visible, setVisible, tenantNo, refreshList }) => {
  const [form] = Form.useForm();
  const uploadRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);

  const onSubmit = async () => {
    const values = await form.validateFields();
    const file = uploadRef.current.fileList?.[0]?.originFileObj;
    if (isEmpty(file)) return message.error('请先上传文件');
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      const urlRes = await uploadExample(formData);
      const testSetUrl = urlRes?.data;
      const res = await createRuleOptimizeTask({ tenantNo, testSetUrl, ...values });
      if (res?.data?.value) {
        setVisible(false);
        refreshList();
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!visible) {
      uploadRef.current?.setFileList?.([]);
      form.resetFields();
    }
  }, [visible, form]);

  return (
    <Modal
      title={'新建优化任务'}
      open={visible}
      onCancel={() => setVisible(false)}
      width={600}
      onOk={onSubmit}
      confirmLoading={loading}
      okText="提交"
    >
      <Form style={{ marginTop: 24 }} form={form}>
        <Form.Item name="ruleName" label="规则名称" rules={[{ required: true, message: '请输入规则名称' }]}>
          <Input placeholder="请输入规则名称" />
        </Form.Item>
        <Form.Item name="ruleDesc" label="规则描述" rules={[{ required: true, message: '请输入规则描述' }]}>
          <Input.TextArea placeholder="请输入规则描述" autoSize={{ minRows: 4 }} />
        </Form.Item>
        <Form.Item label="导入测试集">
          <Upload ref={uploadRef} hint="导入文件格式需保持与测试集一致" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddModal;
