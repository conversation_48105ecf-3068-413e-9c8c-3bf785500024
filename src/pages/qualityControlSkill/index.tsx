import { getAdminUrl } from '@/config.env';
import { headerHeight } from '@/utils/constants';
import { getEnv, isIntl, ssoServiceName } from '@/utils/sso';
import { useLocation, useSelector } from '@magi/magi';
import React, { useEffect, useMemo, useState } from 'react';

const iframeStyle = {
  border: 'none',
  height: `calc(100vh - ${headerHeight})`,
  width: '100%',
  background: '#fff',
};

const QualityControlSkill = () => {
  const location: any = useLocation();
  const { currentTenantNo: _currentTenantNo } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = _currentTenantNo || location?.query?.tenantNo;
  const [token, setToken] = useState('');

  useEffect(() => {
    const access_token = localStorage.getItem('ATLANTIS_SESSION_ID');
    setToken(encodeURIComponent(access_token || ''));
  }, []);
  console.log('token', token);
  const curIframeUrl = useMemo(() => {
    if (getEnv() !== 'prd' && !isIntl()) {
      return `${getAdminUrl()}/?serviceName=${ssoServiceName}#/skillList?botNo=${currentTenantNo}&iframeStyle=true`;
    }
    return `${getAdminUrl()}/?token=${token}&serviceName=${ssoServiceName}#/skillList?botNo=${currentTenantNo}`;
  }, [token, currentTenantNo]);
  console.log('curIframeUrl', curIframeUrl);
  return (
    <iframe title="seker" src={curIframeUrl} style={iframeStyle}>
      <p>欢迎登录灵眸平台</p>
    </iframe>
  );
};

export default QualityControlSkill;
