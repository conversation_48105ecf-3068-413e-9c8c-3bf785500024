:global {
  .ant-dropdown-menu-item.ant-dropdown-menu-item-only-child.tr-toolbar-column-setting-item:last-child {
    display: none;
  }
}

.container {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: var(--bg-secondary);
  position: relative;

  // 左侧分组容器
  .leftTreeContainer {
    width: 200px;
    height: 100%;
    transition: width 0.3s ease;
    overflow: hidden;
    border-right: 2px solid #E7E7E7;
    // padding-right: 10px;

    &.collapsed {
      width: 0;
    }
  }

  // 收起展开按钮
  .collapseButton {
    position: absolute;
    left: 200px;
    top: 30px;
    transform: translateY(-50%);
    width: 16px;
    height: 32px;
    // background-color: #fff;
    // border: 1px solid #d9d9d9;
    border-left: none;
    border-radius: 0 4px 4px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: left 0.3s ease;
    // box-shadow: 1px 0 4px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: #f5f5f5;
      border-color: #4285f4;
    }

    .anticon {
      font-size: 10px;
      color: #666;
    }
  }

  // 当左侧收起时，按钮位置调整
  .leftTreeContainer.collapsed+.collapseButton {
    left: 0;
  }

  .content {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    background-color: var(--bg-secondary);
    margin-left: 0; // 确保内容区域紧贴分组区域

    .actionBar {
      padding: 20px 24px 16px;
      background-color: var(--bg-primary);
      border-bottom: 1px solid var(--border-primary);

      .ant-btn {
        height: 40px;
        padding: 0 20px;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        border-radius: var(--radius-base);

        .anticon {
          font-size: 16px;
        }
      }

      .ant-btn-primary {
        box-shadow: var(--shadow-sm);
      }
    }

    // 内容区域表格容器样式
    :global {
      .tr-container {
        margin: 0;
        background-color: var(--bg-primary);
        border-radius: 0;
        box-shadow: none;
        border-top: 1px solid var(--border-primary);

        .tr-header {
          padding: 20px 24px;
          border-bottom: 1px solid var(--border-primary);
          background-color: var(--bg-primary);
        }

        .tr-toolbar {
          padding: 16px 24px;
          background-color: var(--bg-primary);
          border-bottom: 1px solid var(--border-primary);
        }

        .ant-table-wrapper {
          .ant-table {
            .ant-table-container {
              .ant-table-thead {
                .ant-table-cell {
                  background-color: var(--bg-secondary);
                  border-bottom: 1px solid var(--border-primary);
                  color: var(--text-primary);
                  font-weight: 600;
                  font-size: 14px;
                  padding: 16px 24px;
                }
              }

              .ant-table-tbody {
                .ant-table-cell {
                  border-bottom: 1px solid var(--border-secondary);
                  color: var(--text-primary);
                  padding: 16px 24px;
                  font-size: 14px;
                }

                .ant-table-row {
                  &:hover {
                    .ant-table-cell {
                      background-color: var(--bg-hover);
                    }
                  }
                }
              }
            }
          }
        }

        // 分页样式
        .ant-pagination {
          padding: 16px 24px;
          background-color: var(--bg-primary);
          border-top: 1px solid var(--border-primary);

          .ant-pagination-item {
            border-color: var(--border-primary);

            a {
              color: var(--text-primary);
            }

            &:hover {
              border-color: var(--primary-color);

              a {
                color: var(--primary-color);
              }
            }

            &.ant-pagination-item-active {
              background-color: var(--primary-color);
              border-color: var(--primary-color);

              a {
                color: white;
              }
            }
          }

          .ant-pagination-prev,
          .ant-pagination-next {
            .ant-pagination-item-link {
              border-color: var(--border-primary);
              color: var(--text-secondary);

              &:hover {
                border-color: var(--primary-color);
                color: var(--primary-color);
              }
            }
          }
        }
      }
    }
  }
}

.strategyDetailDrawer {
  :global {
    .ant-drawer-content-wrapper {
      box-shadow: none !important;

      .ant-drawer-header {
        display: none;
      }

      .ant-drawer-body {
        background-color: var(--bg-secondary);
      }

      .ant-spin-container {
        &>div {
          padding-top: 0;
        }
      }
    }
  }
}