import { addGroup, doDeleteGroup, updateGroup } from '@/services/ce';
import { MoreOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { history, useLocation, useSelector } from '@magi/magi';
import { Button, Dropdown, Form, Input, Modal, Tooltip, Tree } from 'antd';
import classNames from 'classnames';
import { cloneDeep, isArray } from 'lodash';
import React, { FC, useEffect, useMemo, useState } from 'react';
import styles from './index.scss';

const setSelectableBasedOnChildren = (tree?: any[]) => {
  const newTree = cloneDeep(tree || []);
  const traverse = (node: any) => {
    // 只有当真正有子分组时才设置为不可选择
    const hasRealChildren = isArray(node.children) && node.children.length > 0;

    if (hasRealChildren) {
      node.selectable = false;
      node.children.forEach(traverse);
    } else {
      node.selectable = true;
      // 确保没有子分组的节点不显示展开线条
      delete node.children;
    }
    return node;
  };
  return traverse(newTree);
};

// 过滤树数据的函数
const filterTreeData = (tree: any[], searchValue: string): any[] => {
  if (!searchValue) return tree;

  const filterNode = (node: any): any => {
    const isMatch = node.title?.toLowerCase().includes(searchValue.toLowerCase());
    const filteredChildren = Array.isArray(node.children) ? node.children.map(filterNode).filter(Boolean) : [];

    if (isMatch || filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren,
      };
    }
    return null;
  };

  return tree.map(filterNode).filter(Boolean);
};

enum EEdit {
  EDIT = 1,
  ADD_GROUP = 2,
  ADD_SUB_GROUP = 3,
}

const MODAL_TITLE_MAP = {
  [EEdit.EDIT]: '编辑分组名称',
  [EEdit.ADD_GROUP]: '新增同级',
  [EEdit.ADD_SUB_GROUP]: '新增子级',
};

interface IProps {
  rebuildGroup: () => Promise<void>;
  currentTenantNo: string;
  selectedKeys: (string | number)[];
  isRedLinePage?: boolean;
}

const LeftTree: FC<IProps> = ({ rebuildGroup, currentTenantNo, selectedKeys, isRedLinePage }) => {
  const location: any = useLocation();
  const { groupList, strategyGroupCountMap, groupMap } = useSelector((state: { global: any }) => state.global);
  const [editForm] = Form.useForm();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentEdit, setCurrentEdit] = useState<any>({});
  const [editGroupLoading, setEditGroupLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    if (!editModalVisible) {
      editForm.resetFields();
      setCurrentEdit({});
    }
  }, [editModalVisible]);

  const handleSelect = (selectedKeys: any[]) => {
    if (selectedKeys && selectedKeys[0]) {
      history.push(`${location.pathname || '/strategyConfig'}?tenantNo=${currentTenantNo}&groupId=${selectedKeys[0]}`);
    }
  };

  useEffect(() => {
    let currentGroupId = location.query.groupId;
    if (!currentGroupId || currentGroupId === 'undefined' || !groupMap?.[currentGroupId]) {
      currentGroupId = Object.keys(groupMap || {})?.[0] || '';
      handleSelect([currentGroupId]);
    }
  }, [location.query.groupId, groupMap]);

  const groupListTree = useMemo(() => {
    const processedTree = groupList?.map(setSelectableBasedOnChildren) || [];
    return filterTreeData(processedTree, searchValue);
  }, [groupList, searchValue]);

  console.log(groupListTree);

  const groupKey = useMemo(() => {
    if (typeof groupList === 'undefined') {
      return '0';
    }
    return '1';
  }, [groupList]);

  const addRootGroup = () => {
    const node = {
      parentId: -1,
      editType: EEdit.ADD_GROUP,
    };
    setCurrentEdit(node);
    setEditModalVisible(true);
  };

  const handleEditGroup = async () => {
    const { title } = await editForm.validateFields();
    const { editType } = currentEdit;
    setEditGroupLoading(true);
    try {
      if (editType === EEdit.EDIT) {
        await updateGroup({
          id: currentEdit.id,
          groupName: title,
          tenantNo: currentTenantNo,
          parentId: currentEdit.parentId,
        });
      }
      if (editType === EEdit.ADD_GROUP || editType === EEdit.ADD_SUB_GROUP) {
        const params = {
          tenantNo: currentTenantNo,
          parentId: editType === EEdit.ADD_GROUP ? currentEdit.parentId : currentEdit.id,
          groupName: title,
        };
        await addGroup(params);
      }
      await rebuildGroup();
    } finally {
      setEditGroupLoading(false);
      setEditModalVisible(false);
    }
  };

  const handleRenderTitle = (node: {
    title: {} | null | undefined;
    level: number;
    id: string | number;
    isRedLine?: string;
    children?: any[];
  }) => {
    const isRedLine = node.isRedLine === '1';
    const childrenCount = node.children?.length || 0;
    const strategyCount = strategyGroupCountMap?.[node.id] || 0;

    const show = (editType: EEdit) => {
      setCurrentEdit(Object.assign({}, node, { editType }));
      if (editType === EEdit.EDIT) {
        editForm.setFieldValue('title', node.title);
      }
      setEditModalVisible(true);
    };

    const editName = (e: { domEvent: { stopPropagation: () => void } }) => {
      e.domEvent.stopPropagation();
      show(EEdit.EDIT);
    };

    const addGroup = (e: { domEvent: { stopPropagation: () => void } }) => {
      e.domEvent.stopPropagation();
      show(EEdit.ADD_GROUP);
    };

    const addChildren = (e: { domEvent: { stopPropagation: () => void } }) => {
      e.domEvent.stopPropagation();
      show(EEdit.ADD_SUB_GROUP);
    };

    const deleteGroup = (e: { domEvent: { stopPropagation: () => void } }, node: { id: number }) => {
      e.domEvent.stopPropagation();
      Modal.confirm({
        content: '确定删除吗？',
        okButtonProps: { loading: editGroupLoading },
        onOk: async () => {
          setEditGroupLoading(true);
          try {
            await doDeleteGroup(node.id, currentTenantNo);
            await rebuildGroup();
            if (node.id === selectedKeys[0]) {
              history.replace(`${location.pathname || '/strategyConfig'}?tenantNo=${currentTenantNo}`);
            }
          } finally {
            setEditGroupLoading(false);
          }
        },
      });
    };

    const dropdownItems = [
      {
        label: '编辑名称',
        key: '1',
        onClick: (e: any) => editName(e),
      },
      {
        label: '添加同级',
        key: '2',
        onClick: (e: any) => addGroup(e),
      },
      {
        label: '添加子级',
        key: '3',
        onClick: (e: any) => addChildren(e),
        hidden: node.level >= 3 || strategyGroupCountMap?.[node.id],
      },
      {
        label: '删除',
        key: '4',
        onClick: (e: any) => deleteGroup(e, node as any),
      },
    ].filter((item) => !item.hidden);

    // 显示的标题文本，只显示子分组数量
    const displayTitle = childrenCount > 0 ? `${node.title} (${childrenCount})` : node.title;

    return (
      <div className={styles.treeNodeContainer}>
        <Tooltip title={node.title}>
          <div className={classNames(styles.treeTitle, { red: isRedLine })}>{displayTitle}</div>
        </Tooltip>
        {!isRedLine && (
          <Dropdown
            trigger={['hover']}
            menu={{
              items: dropdownItems,
            }}
            placement="bottomRight"
          >
            <div className={styles.moreActions} onClick={(e) => e.stopPropagation()}>
              <MoreOutlined />
            </div>
          </Dropdown>
        )}
      </div>
    );
  };

  return (
    <>
      <div className={styles.leftTree}>
        <div className={styles.leftTreeHeader}>策略分组</div>
        <div className={styles.searchContainer}>
          <Input
            placeholder="搜索分组"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            prefix={<SearchOutlined className={styles.searchIcon} />}
            allowClear
            className={styles.searchInput}
          />
        </div>
        <div className={styles.treeContent}>
          <Tree
            key={groupKey}
            defaultExpandAll={true}
            selectedKeys={selectedKeys}
            titleRender={handleRenderTitle as any}
            showLine
            onSelect={handleSelect}
            treeData={groupListTree || []}
            blockNode
          />
        </div>
        <div className={styles.leftTreeFooter}>
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={addRootGroup}
            disabled={isRedLinePage}
            className={styles.addGroupButton}
            block
          >
            新建分组
          </Button>
        </div>
      </div>
      <Modal
        title={MODAL_TITLE_MAP[currentEdit.editType as EEdit] || ''}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => handleEditGroup()}
        confirmLoading={editGroupLoading}
      >
        <Form style={{ marginTop: 24 }} form={editForm}>
          <Form.Item name="title" label="分组名称" rules={[{ required: true, message: '请输入分组名称' }]}>
            <Input maxLength={32} showCount placeholder="请输入分组名称" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default LeftTree;
