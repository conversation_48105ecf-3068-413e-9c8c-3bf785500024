import { getStrategyConfigDraftList } from '@/services/ce';

export const searchApi = async (
  {
    current: pageNum,
    tenantNo,
    pageSize,
    ...restParams
  }: { current: number; pageSize: number; tenantNo: string; [key: string]: any },
  sorter
) => {
  const requestParams = {
    pageNum,
    pageSize,
    tenantNo,
    status: 1,
    ...restParams,
  };
  const res = await getStrategyConfigDraftList(requestParams);
  return {
    data: res?.data?.value?.list,
    total: res?.data?.value?.total,
  };
};

/**
 * 导出调用日志
 * @param {*} data
 * @returns
 */
// export const exportCallRecords = (data = {}) => {
//   // 写成fetch获取文件流,并且post
//   return fetch(`${botPrefix}/admin/sessionResponse/export/CALL_RECORDS`, {
//     method: "POST",
//     body: JSON.stringify(data),
//     headers: {
//       "Content-Type": "application/json",
//       "Cache-Control": "no-cache",
//       "X-Usercenter-Session": localStorage.getItem("ATLANTIS_SESSION_ID") || ""
//     }
//   })
//     .then((res) => {
//       res.blob().then((blob) => {
//         let link = document.createElement("a")
//         link.href = window.URL.createObjectURL(blob)
//         // 昵称为

//         link.download = `调用日志-${dayjs().format("YYYY-MM-DD-HH:mm:ss")}.xlsx`
//         link.click()
//       })
//       return true
//     })
//     .catch((e) => {
//       console.log("下载失败:", e)
//       return false
//     })
// }
