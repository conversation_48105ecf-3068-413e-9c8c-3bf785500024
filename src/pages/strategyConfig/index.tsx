import PageLayout from '@/components/PageLayout';
import TableRender from '@/components/TableRender';
import '@/latex.scss';
import {
  deleteDraft,
  fetchListConfigColumns,
  getGroupTree,
  getStraConfigList,
  moveStrategyConfig,
  updateFeedbackColumns,
} from '@/services/ce';
import { disableSelectedOptions, formatGroupTree } from '@/utils';
import { headerHeight } from '@/utils/constants';
import { DislikeOutlined, LikeOutlined, MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined } from '@ant-design/icons';
import { useDispatch, useLocation, useSelector } from '@magi/magi';
import { Button, Cascader, Dropdown, Form, Image, message, Modal, Popconfirm, Tag, Tooltip, Typography } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep, initial, isEmpty, List } from 'lodash';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import StrategyDetailDrawer from '../strategyDetail/StrategyDetailDrawer';
import styles from './index.scss';
import LeftTree from './LeftTree';
import { searchApi } from './request';
import getSchema from './schema';
import StrategyConfigTemplate from './template';

export const getImgSrc = (context: string) => {
  if (typeof context !== 'string') return;
  const matches = context.match(/<img src="([^"]*)"/);
  return matches?.[1];
};

interface IProps {
  isRedLine?: string;
  currentTenantNo?: string;
}

const StrategyConfig: FC<IProps> = ({ currentTenantNo: cTenantNo, isRedLine }) => {
  const location: any = useLocation();
  const { groupList, groupMap } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = cTenantNo || location?.query?.tenantNo;
  const [moveForm] = Form.useForm();
  const dispatch = useDispatch();
  const [columnsSettingValue, setColumnsSettingValue] = useState<any[]>([]); // 列设置
  const [currentDetail, setCurrentDetail] = useState({});
  const [groupIdList, setGroupIdList] = useState([]);
  const [columns, setColumns] = useState([]); // 列
  const [templateVisible, setTemplateVisible] = useState(false);
  const [moveModalData, setMoveModalData] = useState<{ visibale: boolean; data?: any }>({ visibale: false });
  const [moveGroupLoading, setMoveGroupLoading] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
  const [pagination, setPagination] = useState(() => {
    return {
      current: 1,
      pageSize: 10,
    };
  });
  const [currentSelectColumns, setCurrentSelectColumns] = useState([]);
  const [leftTreeCollapsed, setLeftTreeCollapsed] = useState(false); // 左侧分组收起状态
  const tableRef = useRef<any>();

  const redLinePageData = useMemo(() => {
    // 判断isRedLine是否是红线机器人，如果不是则判断当前的策略分组是否为红线，用于查看不可编辑新增
    if (isRedLine === '1') return { tenantNo: currentTenantNo, isRedLine: false };
    const currentGroupId = location.query.groupId;
    return { tenantNo: groupMap?.[currentGroupId]?.tenantNo, isRedLine: groupMap?.[currentGroupId]?.isRedLine === '1' };
  }, [location.query.groupId, groupMap, isRedLine, currentTenantNo]);

  useEffect(() => {
    if (!moveModalData.visibale) {
      moveForm.resetFields();
    }
  }, [moveModalData]);

  useEffect(() => {
    if (!currentTenantNo || !groupList) return;
    getSelectColumns(currentTenantNo);
    localStorage.setItem('currentTenantNo', currentTenantNo);
  }, [currentTenantNo, groupList]);

  useEffect(() => {
    if (!currentTenantNo) return;
    getStraConfigList({
      tenantNo: currentTenantNo,
    }).then((res) => {
      if (res.data.success) {
        const data = res.data.value.map((item: any) => ({ label: item.groupName, value: item.id, ...item })) || [];
        setGroupIdList(data);
      }
    });
    return () => {
      setCurrentDetail({});
      tableRef.current?.form?.resetFields();
    };
  }, [currentTenantNo]);

  const handleDelete = async (record: { id: number }) => {
    const res = await deleteDraft(record.id);
    if (res.data.success) {
      message.success('删除成功');
      tableRef.current.refresh();
    }
  };

  const actionRow = {
    title: '操作',
    key: 'action',
    hidden: false,
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
    render: (_: any, record: any) => {
      return redLinePageData.isRedLine ? (
        <Button
          onClick={() => {
            setCurrentDetail({
              tenantNo: currentTenantNo,
              id: record.id,
            });
          }}
          style={{ marginRight: 20, padding: 0 }}
          type="link"
        >
          查看
        </Button>
      ) : (
        <div>
          <Button
            onClick={() => {
              setMoveModalData({
                visibale: true,
                data: record,
              });
            }}
            style={{ marginRight: 20, padding: 0 }}
            type="link"
          >
            移动
          </Button>
          <Button
            style={{ marginRight: 20, padding: 0 }}
            onClick={() => {
              setCurrentDetail({
                tenantNo: currentTenantNo,
                id: record.id,
              });
            }}
            type="link"
          >
            编辑
          </Button>
          <Popconfirm title="确定删除吗？" onConfirm={() => handleDelete(record)}>
            <Tooltip title={record.published === 1 ? '已发布的策略不可以删除' : ''}>
              <Button type="link" disabled={!!record.strategyId} danger style={{ padding: 0 }}>
                删除
              </Button>
            </Tooltip>
          </Popconfirm>
        </div>
      );
    },
  };

  const onColumnsSettingChange = (setting: List<unknown> | null | undefined) => {
    const removeActionList: any[] = initial(setting);
    setColumnsSettingValue([...removeActionList, actionRow]);
    const displayFieldList: { fieldCode: any; fieldName: any; fieldNo: any; sort: any; hidden: string }[] = [];
    removeActionList.forEach((item) => {
      columns.forEach((column: any) => {
        if (item.key === column.key) {
          displayFieldList.push({
            fieldCode: column.dataIndex,
            fieldName: column?.title,
            fieldNo: column.key,
            sort: column?.sort,
            hidden: item?.hidden ? 'Y' : 'N',
          });
        }
      });
    });
    updateFeedbackColumns({
      displayFieldList: displayFieldList.map((item, index) => ({ ...item, sort: index + 1 })),
      displayType: 1,
      tenantNo: currentTenantNo,
    });
  };

  const formatColumns = (columns: any) => {
    const _columns = cloneDeep(columns);
    const statusIndex = _columns.findIndex((item: { dataIndex: string }) => item.dataIndex === 'status');
    if (statusIndex > -1) {
      const statusColumn = _columns.splice(statusIndex, 1)[0];
      statusColumn.fixed = 'right';

      _columns.push(statusColumn);
    }
    _columns.push(actionRow);
    setColumnsSettingValue(_columns);

    // 列具体配置
    _columns.forEach((item: any) => {
      if (item.dataIndex === 'feedbackResult') {
        item.render = (text: number) => {
          return text === 1 ? (
            <LikeOutlined style={{ color: 'blue', fontSize: '16px' }} />
          ) : (
            <DislikeOutlined style={{ color: 'red', fontSize: '16px' }} />
          );
        };
      }
      if (['strategyRuleName', 'strategyName', 'strategyDefine'].includes(item.dataIndex)) {
        item.width = 200;
        item.render = (text: string) => {
          return (
            <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text }}>
              {text}
            </Typography.Paragraph>
          );
        };
      }
      if (['responseContext', 'requestContext', 'sensitiveWords', 'errMsg'].includes(item.dataIndex)) {
        item.align = 'left';
        item.render = (text: string | string[]) => {
          const imgSrc = getImgSrc(text as string);
          return imgSrc ? (
            <Image height={58} src={imgSrc} />
          ) : (
            <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text }}>
              {item.dataIndex === 'sensitiveWords' ? (text as string[])?.join(',') || '' : text}
            </Typography.Paragraph>
          );
        };
      }
      if (item.dataIndex === 'modelType') {
        item.render = (text = []) => text.join(',');
      }
      if (['gmtCreated', 'gmtModified'].includes(item.dataIndex)) {
        item.render = (text: string | number | Date | dayjs.Dayjs | null | undefined) => {
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        };
      }
      if (item.dataIndex === 'status') {
        item.align = 'left';
        item.render = (status: number) => {
          if (status === 1) {
            return <span style={{ color: '#10c287' }}>启用</span>;
          } else if (status === 0) {
            return <span style={{ color: 'red' }}>禁用</span>;
          }
          return '未知';
        };
      }
      if (item.dataIndex === 'published') {
        item.render = (published: number) => {
          return <span>{published === 1 ? '已发布' : '未发布'}</span>;
        };
      }
      if (item.dataIndex === 'groupId') {
        item.align = 'left';
        item.render = (text: string | number) => {
          return groupMap[text]?.groupName || '';
        };
      }
      if (item.dataIndex === 'feedbackTag') {
        item.render = (text: string) => {
          return text
            ? text.split(',').map((item) => (
                <Tag color="processing" key={item}>
                  {item}
                </Tag>
              ))
            : null;
        };
      }
      if (item.dataIndex === 'success') {
        item.render = (status: boolean) => {
          if (status === true) {
            return '是';
          } else if (status === false) {
            return '否';
          }
          return '未知';
        };
      }
    });
    return _columns;
  };

  const getSelectColumns = async (currentTenantNo: any) => {
    const params = {
      tenantNo: currentTenantNo,
      displayType: 1,
    };
    fetchListConfigColumns(params).then((res) => {
      if (res.data.success) {
        setCurrentSelectColumns(res.data.value);
      }
    });
  };

  useEffect(() => {
    setColumns(
      formatColumns(
        currentSelectColumns.map((item: any) => {
          return {
            ...item,
            key: item.fieldCode,
            title: item.fieldName,
            dataIndex: item.fieldCode,
            sort: item.sort,
            hidden: item.hidden === 'N' ? false : true,
          };
        })
      )
    );
  }, [groupMap, currentSelectColumns, redLinePageData.isRedLine]);

  useEffect(() => {
    let currentGroupId: any = location.query.groupId;
    if (!currentGroupId || currentGroupId == 'undefined' || isEmpty(groupMap)) return;
    currentGroupId = Number(currentGroupId);
    setSelectedKeys([currentGroupId]);
    setTimeout(() => {
      tableRef.current && tableRef.current.refresh();
    });
  }, [location.query.groupId, groupMap]);

  const rebuildGroup = async () => {
    const res = await getGroupTree(currentTenantNo);
    if (res.data.success && res.data.value) {
      const { tree, map } = formatGroupTree(res.data.value.strategyGroupDTOList);
      dispatch({
        type: 'global/save',
        payload: {
          groupList: tree,
          groupMap: map,
          strategyGroupCountMap: res.data.value.strategyGroupCountMap,
        },
      });
    }
  };

  const handleMoveGroup = async () => {
    const { groupIds } = await moveForm.validateFields();
    setMoveGroupLoading(true);
    try {
      const res = await moveStrategyConfig({
        originGroupId: selectedKeys[0],
        newGroupId: groupIds?.[groupIds?.length - 1],
        strategyId: moveModalData.data?.strategyId,
        draftsId: moveModalData.data?.id,
        tenantNo: currentTenantNo,
      });
      if (res?.data?.success) {
        await rebuildGroup();
        setMoveModalData({ visibale: false });
      }
    } finally {
      setMoveGroupLoading(false);
    }
  };

  return (
    <PageLayout>
      <div className={styles.container}>
        {/* 左侧分组区域 */}
        <div className={`${styles.leftTreeContainer} ${leftTreeCollapsed ? styles.collapsed : ''}`}>
          <LeftTree
            isRedLinePage={redLinePageData.isRedLine}
            rebuildGroup={rebuildGroup}
            currentTenantNo={currentTenantNo}
            selectedKeys={selectedKeys}
          />
        </div>

        {/* 收起展开按钮 */}
        <div className={styles.collapseButton} onClick={() => setLeftTreeCollapsed(!leftTreeCollapsed)}>
          {leftTreeCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </div>

        {!!selectedKeys[0] && !(groupMap[selectedKeys[0]]?.children?.length > 0) && (
          <div className={styles.content}>
            {!redLinePageData.isRedLine && (
              <div className={styles.actionBar}>
                {isRedLine === '1' ? (
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setCurrentDetail({
                        tenantNo: currentTenantNo,
                        groupId: selectedKeys[0],
                      });
                    }}
                  >
                    新建策略
                  </Button>
                ) : (
                  <Dropdown
                    trigger={['hover']}
                    menu={{
                      items: [
                        {
                          label: '自定义新增',
                          key: 'custom',
                          onClick: () => {
                            setCurrentDetail({
                              tenantNo: currentTenantNo,
                              groupId: selectedKeys[0],
                            });
                          },
                        },
                        {
                          label: '从模版新增',
                          key: 'template',
                          onClick: () => setTemplateVisible(true),
                        },
                      ],
                    }}
                  >
                    <Button type="primary" icon={<PlusOutlined />}>
                      新建策略
                    </Button>
                  </Dropdown>
                )}
              </div>
            )}
            <TableRender
              ref={tableRef}
              pageData={pagination}
              setPageData={setPagination}
              search={{ schema: getSchema(groupIdList, groupList), searchOnMount: false }}
              request={async (params) => {
                return await searchApi(
                  {
                    ...params,
                    current: params.current || pagination.current,
                    pageSize: params.pageSize || pagination.pageSize,
                    tenantNo: redLinePageData.tenantNo,
                    groupIds: [selectedKeys[0]],
                    ...(redLinePageData.isRedLine ? { currentTenantNo } : {}),
                  },
                  null
                );
              }}
              columns={columns}
              toolbarAction={
                {
                  enabled: ['columnsSetting'],
                  columnsSettingValue,
                  onColumnsSettingChange,
                } as any
              }
            />
          </div>
        )}
        <Modal
          title={'移动至'}
          open={moveModalData.visibale}
          onCancel={() => setMoveModalData({ visibale: false })}
          onOk={() => handleMoveGroup()}
          confirmLoading={moveGroupLoading}
        >
          <Form style={{ marginTop: 24 }} form={moveForm}>
            <Form.Item name="groupIds" label="策略分组" rules={[{ required: true, message: '请选择策略分组' }]}>
              <Cascader
                options={disableSelectedOptions(groupList, selectedKeys, isRedLine === '1')}
                placeholder="请选择策略分组"
                fieldNames={{ label: 'title', value: 'value', children: 'children' }}
              />
            </Form.Item>
            <p style={{ marginBottom: 24, color: 'red' }}>
              注意：移动策略会对执行中的任务和结果造成影响，移动后新增的质检任务将按照新分组来执行和保存数据！
            </p>
          </Form>
        </Modal>
      </div>
      <StrategyDetailDrawer
        currentDetail={currentDetail}
        setCurrentDetail={setCurrentDetail}
        refresh={() => tableRef.current.refresh()}
        isSubTitle
        rootClassName={`strategy-detail-drawer ${styles.strategyDetailDrawer}`}
        rootStyle={{ marginTop: headerHeight }}
        isRedLine={redLinePageData.isRedLine ? '1' : isRedLine}
        onlyRead={redLinePageData.isRedLine}
      />
      <StrategyConfigTemplate
        open={templateVisible}
        onConfirm={(data: any) => {
          setTemplateVisible(false);
          setCurrentDetail({
            tenantNo: currentTenantNo,
            groupId: selectedKeys[0],
            templateData: data,
          });
        }}
        onCancel={() => setTemplateVisible(false)}
      />
    </PageLayout>
  );
};

export default StrategyConfig;
