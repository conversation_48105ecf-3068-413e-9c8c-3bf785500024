import { getAllTreeList, getFirstLevelKeys } from '@/utils';
import { TreeSelect } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React from 'react';

const { SHOW_CHILD } = TreeSelect;

export const currentDayStartSecond = dayjs().startOf('day'); // 00:00:00
export const currentDayEndSecond = dayjs().endOf('day'); // 23:59:59

// 状态枚举, 0: 失败, 1: 成功
export const statusEnum = [
  { status: '全部', value: '' },
  { status: '启用', value: 1 },
  { status: '禁用', value: 0 },
];

const getSchema = (groupIdList = [], groupList = []) => ({
  type: 'object',
  labelWidth: 90,
  properties: {
    strategyIds: {
      title: '策略名称',
      type: 'string',
      widget: 'treeSelect',
      span: 8,
      placeholder: '请选择策略名称',
      props: {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        maxTagCount: 'responsive',
        treeData: getAllTreeList(groupIdList, { titleKey: 'strategyName', valueKey: 'id' }) || [],
        treeDefaultExpandedKeys: getFirstLevelKeys(groupIdList, { titleKey: 'strategyName', valueKey: 'id' }),
        fieldNames: { value: 'id', label: 'strategyName', children: 'children' },
        treeTitleRender: (node: any) => (
          <span className={classNames({ red: node?.isRedLine === '1' })}>{node?.strategyName}</span>
        ),
        filterTreeNode: (input: any, option: any) => {
          return (option?.strategyName ?? '').toLowerCase().includes(input.toLowerCase());
        },
      },
    },
    strategyCode: {
      title: '编号',
      type: 'string',
      placeholder: '请输入编号',
      span: 8,
    },
    status: {
      title: '状态',
      type: 'string',
      widget: 'select',
      placeholder: '请选择是否成功状态',
      span: 8,
      props: {
        options: statusEnum,
        defaultValue: 1,
        fieldNames: { label: 'status', value: 'value' },
      },
    },
  },
});
export default getSchema;
