import EasyTable, { Pagination } from '@/components/EasyTable';
import { getStrategyTemplateDetail, getStrategyTemplateList } from '@/services/ce';
import { message, Modal, Radio } from 'antd';
import { isEmpty } from 'lodash';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

interface IProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (params: any) => void;
}

const StrategyConfigTemplate = (props: IProps) => {
  const { open, onCancel, onConfirm } = props;
  const tableRef = useRef<any>(null);
  const [current, setCurrent] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const templateColumns = useMemo(() => {
    return [
      {
        title: '',
        key: 'action',
        width: 50,
        render: (_: any, record: any) => {
          return <Radio onChange={() => setCurrent(record)} checked={current.id === record.id}></Radio>;
        },
      },
      {
        title: '策略名称',
        dataIndex: 'strategyName',
        key: 'strategyName',
      },
      {
        title: '关联规则显示名称',
        dataIndex: 'strategyRuleName',
        key: 'strategyRuleName',
      },
    ];
  }, [current]);

  const handleSelect = async () => {
    if (isEmpty(current)) {
      message.warning('请先选择模版');
      return;
    }
    setLoading(true);
    try {
      const res = await getStrategyTemplateDetail(current.id);
      const data = res.data.value.configData;
      onConfirm(data);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setCurrent({});
  }, [open]);

  const request = useCallback(async (pagination: Pagination) => {
    const { current, pageSize } = pagination;
    const res = await getStrategyTemplateList({
      pageNum: current,
      pageSize,
    });
    return {
      total: res.data.value.total,
      data: res.data.value.list,
    };
  }, []);

  return (
    <Modal
      confirmLoading={loading}
      onOk={handleSelect}
      destroyOnClose
      title="选择策略模版"
      width={800}
      open={open}
      onCancel={onCancel}
    >
      <EasyTable style={{ marginTop: 24 }} ref={tableRef} columns={templateColumns} request={request} />
    </Modal>
  );
};

export default StrategyConfigTemplate;
