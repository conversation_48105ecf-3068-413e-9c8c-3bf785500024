import Back from '@/assets/back.svg';
import {
  feedbackQualityRealTimeResults,
  feedbackQualityResults,
  getNoViolation,
  getQualityRealTimeTaskResult,
  getQualityTaskResult,
} from '@/services/ce';
import { copy } from '@/utils';
import { CheckCircleOutlined } from '@ant-design/icons';
import { ESessionType, MSG_ID_PREFIX } from '@/utils/constants';
import { history, useLocation, useSelector } from '@magi/magi';
import { Button, Divider, Form, message, Switch, Tabs } from 'antd';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import AnnotatedLog from './AnnotatedLog';
import BaseInfo from './BaseInfo';
import ChatHistory from './ChatHistory';
import ConsultInfo from './ConsultInfo';
import Controls from './Controls';
import styles from './index.module.scss';
import Player from './Player';
import ServiceInfo, { clearUserRedlineStatus, getUserRedlineStatus, setUserRedlineStatus } from './ServiceInfo';
// import useChatScroll from './useChatScroll';

//定义消息类型
interface IItem {
  type: number;
  subType: number;
  toId: number;
  fromId: number;
  content: string;
  feature: string;
  msgId: string;
  goBack?: () => void;
}

export const TIMEOUT_NUM = 2000;

const ConsultRecord: FC<any> = ({ id, tenantNo, isContainer, goBack, isRealTime, idData, onPrevBtn, onNextBtn }) => {
  const location: any = useLocation();
  const {
    currentTenantNo: _currentTenantNo,
    userInfo,
    whitelistPermission,
  } = useSelector((state: { global: any }) => state.global);
  const [resultForm] = Form.useForm();
  const [chatList, setChatList] = useState<IItem[]>([]);
  const [recordLists, setRecordLists] = useState<any[]>([]);
  const [baseInfoLists, setBaseInfoLists] = useState<any[]>([]);
  const [seatName, setSeatName] = useState<string>();
  const [serviceInfo, setServiceInfo] = useState<any>();
  const [heightLightMsgId, setHeightLightMsgId] = useState<any>(null);
  const [isSimple, setIsSimple] = useState(false);
  const [currentMsgId, setCurrentMsgId] = useState<any>(null);
  const [feekbackLoading, setFeekbackLoading] = useState(false);
  const [editBoolData, setEditBoolData] = useState<any>({});
  const [tagResult, setTagResult] = useState<string>();
  const [activeKey, setActiveKey] = useState('result');
  const scrollRef = useRef<any>(null);
  const playerRef = useRef<any>(null);
  const lastMsgId = useRef<any>(null);
  const consultRecordRef = useRef<any>(null);
  const isScrollRef = useRef(false);
  const timerRef = useRef<any>();
  const fetchTimerRef = useRef<any>();

  const _currentSessionId = useMemo(() => id || location?.query?.id, [id, location?.query?.id]);

  const [currentSessionID, setCurrentSessionID] = useState<any>(_currentSessionId);

  const isIframe = useMemo(() => location?.query?.isIframe === 'true' || false, [location?.query?.isIframe]);

  const currentTenantNo = useMemo(
    () => tenantNo || _currentTenantNo || location?.query?.tenantNo,
    [tenantNo, _currentTenantNo, location?.query?.tenantNo]
  );

  // 展示权限
  const isNoViolation = useMemo(() => {
    const { noViolation } = whitelistPermission || {};
    return noViolation?.includes(currentTenantNo);
  }, [currentTenantNo, whitelistPermission]);

  const showFeedbackBtn = useMemo(() => {
    return !!recordLists?.some(({ ruleTagging }) => !ruleTagging);
  }, [recordLists]);

  const isEdit = useMemo(() => {
    return !!Object.entries(editBoolData).some(([key]) => editBoolData[key]);
  }, [editBoolData]);

  const hitList = useMemo(() => recordLists?.map(({ hitRule }) => hitRule)?.filter(Boolean), [recordLists]);

  const onCancel = useCallback(() => {
    resultForm.resetFields();
    setEditBoolData({});
  }, [resultForm]);

  const changeSessionId = useCallback(
    (val: string) => {
      playerRef.current?.initPlayer();
      setCurrentMsgId(null);
      setHeightLightMsgId(null);
      onCancel();
      setCurrentSessionID(val);
    },
    [onCancel]
  );

  useEffect(() => {
    changeSessionId(_currentSessionId);
  }, [_currentSessionId]);

  useEffect(() => {
    return () => {
      lastMsgId.current = null;
      isScrollRef.current = false;
      clearTimeout(timerRef.current);
      clearTimeout(fetchTimerRef.current);
    };
  }, [currentSessionID]);

  const openViolationModal = useCallback((...args: any[]) => {
    consultRecordRef.current?.openViolationModal(...args);
  }, []);

  const handleGetQualityTaskResult = useCallback(
    (isUpdate?: boolean, userRedlineStatus?: '0' | '1') => {
      return (isRealTime ? getQualityRealTimeTaskResult : getQualityTaskResult)({
        sessionId: currentSessionID,
        tenantNo: currentTenantNo,
        userRedlineStatus: userRedlineStatus || getUserRedlineStatus(isRealTime) || undefined,
      }).then((res) => {
        if (res.data.success) {
          if (userRedlineStatus === '1') {
            setUserRedlineStatus(userRedlineStatus, isRealTime);
          } else if (userRedlineStatus === '0') {
            clearUserRedlineStatus(isRealTime);
          }
          const qualityMsgDTOList = res.data?.value?.qualityMsgDTOList || [];
          const qualityTaskDetailDTOList = res.data?.value?.qualityTaskDetailDTOList || [];
          const baseInfoDTOList = res.data?.value?.baseInfoDTOList || [];
          if (!isUpdate) {
            setBaseInfoLists(baseInfoDTOList);
          } else {
            setBaseInfoLists((preState) => {
              const resData = preState?.find((v) => v.fieldName === '语音文件');
              return baseInfoDTOList.map((item: any) => {
                if (item.fieldName === '语音文件' && resData?.fieldValue) {
                  return resData;
                }
                return item;
              });
            });
          }
          const keywordsMap = new Map();
          qualityTaskDetailDTOList?.forEach((item: any) => {
            item.qualityTaskKeyWordDTOList?.forEach(({ keyWordName, msgId }: any) => {
              if (!keywordsMap.has(msgId)) {
                keywordsMap.set(msgId, new Set());
              }
              keywordsMap.get(msgId).add(keyWordName);
            });
          });
          setChatList(
            qualityMsgDTOList?.map((item: any) => ({
              ...item,
              sensitiveStr: Array.from(keywordsMap.get(item.msgId) || []),
            }))
          );
          setRecordLists(qualityTaskDetailDTOList);
          setSeatName(res.data?.value?.seatName);
          setServiceInfo(res.data?.value?.serviceInfo);
          setTagResult(res.data?.value?.tagResult);
        }
      });
    },
    [currentSessionID, currentTenantNo, isRealTime]
  );

  useEffect(() => {
    handleGetQualityTaskResult();
  }, [handleGetQualityTaskResult]);

  const onScroll = useCallback(() => {
    clearTimeout(timerRef.current);
    isScrollRef.current = true;
    timerRef.current = setTimeout(() => {
      isScrollRef.current = false;
    }, 150);
  }, []);

  useEffect(() => {
    scrollRef.current?.addEventListener('scroll', onScroll);
    return () => {
      scrollRef.current?.removeEventListener('scroll', onScroll);
    };
  }, [onScroll]);

  const handleFeedbackQualityResults = useCallback(async () => {
    const values = await resultForm.validateFields();
    const params = Object.entries(values)
      .map(([id, val]: any) => ({
        id,
        ruleId: recordLists?.find((v: any) => String(v.id) === String(id))?.ruleId,
        sessionId: recordLists?.find((v: any) => String(v.id) === String(id))?.sessionId,
        strategyId: recordLists?.find((v: any) => String(v.id) === String(id))?.strategyId,
        tenantNo: currentTenantNo,
        operator: userInfo?.name || userInfo?.adAccount,
        ...val,
        label: val?.label?.join(',') || '',
      }))
      .filter(({ ruleTagging }) => !!ruleTagging);
    setFeekbackLoading(true);
    (isRealTime ? feedbackQualityRealTimeResults : feedbackQualityResults)(params)
      .then((res) => {
        if (res.data.value) {
          message.success('提交成功！');
          clearTimeout(fetchTimerRef.current);
          fetchTimerRef.current = setTimeout(() => {
            handleGetQualityTaskResult(true);
            setEditBoolData({});
            setFeekbackLoading(false);
          }, TIMEOUT_NUM);
        } else {
          setFeekbackLoading(false);
        }
      })
      .catch(() => {
        setFeekbackLoading(false);
      });
  }, [resultForm, recordLists, currentTenantNo, handleGetQualityTaskResult, userInfo, isRealTime]);

  const handleGetNoViolation = useCallback(async () => {
    try {
      setFeekbackLoading(true);
      const res = await getNoViolation({ sessionId: currentSessionID, tenantNo: currentTenantNo, tagResult: '1' });
      if (res.data.success) {
        message.success('提交成功');
        clearTimeout(fetchTimerRef.current);
        fetchTimerRef.current = setTimeout(() => {
          handleGetQualityTaskResult(true);
          setFeekbackLoading(false);
        }, TIMEOUT_NUM);
      } else {
        setFeekbackLoading(false);
      }
    } catch {
      setFeekbackLoading(false);
    }
  }, [currentSessionID, currentTenantNo]);

  const sessionType = useMemo(() => {
    return baseInfoLists.find((item: any) => item.fieldName?.includes('会话类型'))?.fieldValue || '';
  }, [baseInfoLists]);

  const audioUrl = useMemo(() => {
    if (sessionType !== ESessionType.CALL) return '';
    return baseInfoLists.find((item: any) => item.fieldName === '语音文件')?.fieldValue || '';
  }, [baseInfoLists, sessionType]);

  const handleChildValue = useCallback(
    (value: { msgId: string; line?: string | number }, isPlay: boolean) => {
      try {
        const { msgId: _msgId, line } = value;
        const msgId = _msgId || chatList?.find((_: unknown, index: number) => index === Number(line))?.msgId;
        !isPlay && setHeightLightMsgId(msgId);
        const selector = `${MSG_ID_PREFIX}${msgId}`;
        const dom = document.querySelector(`#${selector}`);
        if (dom && !isScrollRef.current) {
          dom.scrollIntoView({
            behavior: 'smooth',
            block: isIframe ? 'nearest' : 'start',
          });
        }
      } catch (error) {
        console.log(`handleChildValue: ${error}`);
      }
    },
    [isIframe, chatList]
  );

  /**文本滚动 */
  const handleTimeUpdate = useCallback(
    ({ time, status, follow }) => {
      if (!status || !follow) {
        setCurrentMsgId(null);
        return;
      }
      const msgId = chatList.find((item: any) => {
        const currentTime = time * 1000;
        return currentTime >= item.beginTimeOffset && currentTime <= item.endTimeOffset;
      })?.msgId;
      setCurrentMsgId(msgId);
      if (lastMsgId.current !== msgId) {
        lastMsgId.current = msgId;
        if (msgId) {
          handleChildValue({ msgId }, true);
        }
      }
    },
    [chatList, handleChildValue]
  );

  const onPlayAudio = useCallback((beginTimeOffset: number) => {
    if (typeof beginTimeOffset !== 'number') return;
    playerRef.current?.onCurrentTimePlay(beginTimeOffset);
  }, []);

  const onPause = useCallback(() => {
    playerRef.current?.onPause();
  }, []);

  /**TODO：多会话合并后，通过此hook来定位当前屏幕下的最后一条会话 */
  // useChatScroll({
  //   ref: scrollRef,
  //   list: chatList,
  // });

  const copyText = useCallback(() => {
    const arr = chatList.map((item: any) => {
      if (item.side === 1) {
        return `客户：${item.text}`;
      }
      if (item.side === 2 || item.side === 3) {
        return `客服：${item.text}`;
      }
    });
    const text = arr.join('\n');
    copy(text);
  }, [chatList]);

  const tabItems = useMemo(() => {
    return [
      {
        key: 'result',
        label: '结果',
        children: (
          <>
            <p style={{ color: '#666666', fontWeight: 600, fontSize: 14, marginTop: 0 }}>
              共违反<span style={{ color: 'red' }}>{hitList.length || 0}</span>个规则：{hitList?.join('，') || '-'}
            </p>
            <ConsultInfo
              ref={consultRecordRef}
              onChildValue={handleChildValue}
              recordLists={recordLists}
              form={resultForm}
              editBoolData={editBoolData}
              setEditBoolData={setEditBoolData}
              sessionId={currentSessionID}
              handleGetQualityTaskResult={handleGetQualityTaskResult}
              currentTenantNo={currentTenantNo}
              seatName={seatName}
              isRealTime={isRealTime}
            />
          </>
        ),
      },
      {
        key: 'log',
        label: '标注日志',
        children: (
          <AnnotatedLog
            tenantNo={currentTenantNo}
            sessionId={currentSessionID}
            isFetch={activeKey === 'log'}
            isRealTime={isRealTime}
          />
        ),
      },
    ];
  }, [
    hitList,
    handleChildValue,
    recordLists,
    resultForm,
    editBoolData,
    currentSessionID,
    handleGetQualityTaskResult,
    currentTenantNo,
    seatName,
    activeKey,
    isRealTime,
  ]);

  const ControlsRender = useMemo(() => {
    return (
      (!isIframe || isContainer) && !!idData && <Controls onPrevBtn={onPrevBtn} onNextBtn={onNextBtn} idData={idData} />
    );
  }, [isContainer, onPrevBtn, onNextBtn, idData]);

  return (
    <div className={styles.consultContainer} style={isIframe ? { height: '100vh', padding: 0 } : {}}>
      {!!serviceInfo?.serviceId && (
        <ServiceInfo
          goBack={() => (goBack ? goBack() : history.goBack())}
          isContainer={isContainer}
          isIframe={isIframe}
          serviceInfo={serviceInfo}
          sessionId={currentSessionID}
          setSessionId={changeSessionId}
          ControlsRender={ControlsRender}
          handleGetQualityTaskResult={handleGetQualityTaskResult}
          tenantNo={currentTenantNo}
          isRealTime={isRealTime}
        />
      )}
      {!!audioUrl && (
        <Player
          ref={playerRef}
          isIframe={isIframe}
          list={chatList}
          currentSessionID={currentSessionID}
          onTimeUpdate={handleTimeUpdate}
          url={audioUrl}
          goBack={goBack}
          isContainer={isContainer}
          isService={serviceInfo?.serviceId}
          ControlsRender={ControlsRender}
        />
      )}
      <div className={styles.consultRrecord} style={isIframe ? { margin: 0 } : {}}>
        <div className={styles.consultMessage}>
          <div className={styles.consultTitle}>
            <div style={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
              {!audioUrl && !serviceInfo?.serviceId && (!isIframe || isContainer) && (
                <img
                  onClick={() => (goBack ? goBack() : history.goBack())}
                  style={{ marginRight: 10, cursor: 'pointer' }}
                  src={Back}
                />
              )}
              会话详情
              <Button onClick={copyText} size="small" style={{ marginLeft: 8, fontSize: 12 }}>
                复制会话
              </Button>
              {!audioUrl && !serviceInfo?.serviceId && (!isIframe || isContainer) && ControlsRender}
            </div>
            {sessionType === ESessionType.CALL && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: 14, marginRight: 8 }}>精简{isSimple ? '打开' : '关闭'}</span>
                <Switch size="small" checked={isSimple} onChange={(e) => setIsSimple(e)} />
              </div>
            )}
          </div>
          <div ref={scrollRef} className={styles.scrollWrapper}>
            <ChatHistory
              currentMsgId={currentMsgId!}
              isSimple={isSimple}
              heightLightMsgId={heightLightMsgId}
              list={chatList}
              hasAudio={!!audioUrl}
              seatName={seatName}
              onPlayAudio={onPlayAudio}
              onPause={onPause}
              openViolationModal={openViolationModal}
            />
          </div>
        </div>
        <div className={styles.consultInfo}>
          <div className={styles.consultTitle}>质检结果</div>
          <div className={styles.scrollWrapper}>
            <BaseInfo baseInfoLists={baseInfoLists} />
            <Divider style={{ borderColor: '#EFF1F4' }} />
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                style={{
                  marginRight: 8,
                  display: 'flex',
                  fontSize: 24,
                  color: '#A28FF5',
                }}
              >
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M14.25 3C14.25 2.86193 14.1381 2.75 14 2.75H7C5.48122 2.75 4.25 3.98122 4.25 5.5V19.5C4.25 21.0188 5.48122 22.25 7 22.25H17C18.5188 22.25 19.75 21.0188 19.75 19.5V9.64706C19.75 9.50899 19.6381 9.39706 19.5 9.39706H15C14.5858 9.39706 14.25 9.06127 14.25 8.64706V3ZM15 12.75C15.4142 12.75 15.75 13.0858 15.75 13.5C15.75 13.9142 15.4142 14.25 15 14.25H9C8.58579 14.25 8.25 13.9142 8.25 13.5C8.25 13.0858 8.58579 12.75 9 12.75H15ZM15 16.75C15.4142 16.75 15.75 17.0858 15.75 17.5C15.75 17.9142 15.4142 18.25 15 18.25H9C8.58579 18.25 8.25 17.9142 8.25 17.5C8.25 17.0858 8.58579 16.75 9 16.75H15Z"
                    fill="currentColor"
                  />
                  <path
                    d="M15.75 3.32414C15.75 3.13964 15.9426 3.0225 16.0862 3.13839C16.2071 3.236 16.3158 3.35036 16.4085 3.47955L19.4217 7.67745C19.4903 7.77302 19.416 7.89706 19.2983 7.89706H16C15.8619 7.89706 15.75 7.78513 15.75 7.64706V3.32414Z"
                    fill="currentColor"
                  />
                </svg>
              </span>
              <span style={{ fontSize: 16, fontWeight: 600, color: '#000b15' }}>详情</span>
            </div>
            <Tabs
              className={styles.consultInfoTabs}
              activeKey={activeKey}
              items={tabItems}
              onChange={(key) => setActiveKey(key)}
            />
          </div>
          <div
            className={styles.consultFooter}
            style={
              ((showFeedbackBtn || isEdit) && activeKey === 'result') || (isNoViolation && !recordLists?.length)
                ? {}
                : { display: 'none' }
            }
          >
            {!!recordLists?.length ? (
              <>
                <Button onClick={onCancel}>取消</Button>
                <Button
                  type="primary"
                  onClick={handleFeedbackQualityResults}
                  loading={feekbackLoading}
                  disabled={!isEdit}
                >
                  提交
                </Button>
              </>
            ) : (
              <Button
                type="primary"
                onClick={handleGetNoViolation}
                icon={!!tagResult && <CheckCircleOutlined />}
                loading={feekbackLoading}
                disabled={!!tagResult}
                className={styles.consultFooterBtn}
              >
                {tagResult ? '已确认：全部正确' : '该会话全部正确'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultRecord;
