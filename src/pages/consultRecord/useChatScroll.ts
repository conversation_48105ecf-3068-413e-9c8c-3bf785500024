import { getNumFromPx } from '@/utils';
import { last } from 'lodash';
import { MutableRefObject, useEffect, useRef, useState } from 'react';
import { AudioTextType } from './ChatHistory/services/types/manualQuality';

interface IProps {
  ref: MutableRefObject<HTMLDivElement>;
  list: AudioTextType[];
}
type RowHeightInfoType = { height: number; msgId: string; totalHeight: number };
const useChatScroll = (props: IProps) => {
  const { ref, list } = props;
  const rowHeightInfo = useRef<RowHeightInfoType[]>([]);
  const scrollTop = useRef<number>(0);
  const [activeMsgId, setActiveMsgId] = useState<string>('');
  useEffect(() => {
    if (list.length === 0) {
      return;
    }
    const onScroll = (e: any) => {
      const list = rowHeightInfo.current;
      if (ref.current.clientHeight === ref.current.scrollHeight) {
        const msgId = last(list)?.msgId || '';
        setActiveMsgId(msgId);
        return;
      }
      scrollTop.current = ref.current.scrollTop;
      const target = ref.current.clientHeight + ref.current.scrollTop;
      let closest = null;

      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (item.totalHeight < target) {
          if (closest === null || item.totalHeight > closest.totalHeight) {
            closest = item;
          }
        }
      }
      const msgId = closest?.msgId;
      setActiveMsgId(msgId || '');
    };
    scrollTop.current = ref.current.scrollTop;
    const msgDomList = Array.from(ref.current.querySelectorAll('.msgRow'));
    const arr: RowHeightInfoType[] = [];
    msgDomList.forEach((dom, index) => {
      const msgId = dom.getAttribute('data-rowid') || '';
      let height = dom.clientHeight;
      if (index === 0) {
        const paddingTop = getNumFromPx(window.getComputedStyle(ref.current).paddingTop || '0px');
        height += paddingTop;
      }
      if (index === msgDomList.length - 1) {
        const paddingBottom = getNumFromPx(window.getComputedStyle(ref.current).paddingBottom || '0px');
        height += paddingBottom;
      }
      let totalHeight = arr[index - 1]?.totalHeight + arr[index - 1]?.height || 0;

      arr.push({ height, msgId, totalHeight });
    });
    rowHeightInfo.current = arr;
    onScroll({});
    ref.current && ref.current.addEventListener('scroll', onScroll);
    return () => {
      ref.current && ref.current.removeEventListener('scroll', onScroll);
    };
  }, [list]);
  return {
    activeMsgId,
  };
};

export default useChatScroll;
