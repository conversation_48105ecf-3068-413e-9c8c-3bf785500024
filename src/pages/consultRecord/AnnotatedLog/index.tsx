import { queryQualityResultTagLog, queryRealTimeQualityResultTagLog } from '@/services/ce';
import { List } from 'antd';
import React, { FC, memo, useCallback, useEffect, useState } from 'react';
import styles from './index.scss';

interface IProps {
  tenantNo: string;
  sessionId: string;
  isFetch?: boolean;
  isRealTime?: boolean;
}

const AnnotatedLog: FC<IProps> = ({ tenantNo, sessionId, isFetch = true, isRealTime }) => {
  const [logList, setLogList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const handleQualityResultTagLog = useCallback(async () => {
    try {
      setLoading(true);
      const res = await (isRealTime ? queryRealTimeQualityResultTagLog : queryQualityResultTagLog)({
        tenantNo,
        sessionId,
      });
      if (res.data.success) {
        setLogList(res.data?.value || []);
      }
    } finally {
      setLoading(false);
    }
  }, [tenantNo, sessionId, isRealTime]);

  useEffect(() => {
    isFetch && handleQualityResultTagLog();
  }, [handleQualityResultTagLog, isFetch]);

  const renderItem = useCallback((item) => {
    return (
      <List.Item key={item.ruleId + item.gmtCreated}>
        <List.Item.Meta
          title={item.ruleName}
          description={
            <>
              <div>
                <span>{item.creator}</span>
                {item.optContent}
              </div>
              <p>{item.gmtCreated}</p>
            </>
          }
        />
      </List.Item>
    );
  }, []);

  return <List loading={loading} className={styles.logList} dataSource={logList} renderItem={renderItem} />;
};

export default memo(AnnotatedLog);
