import { appealStatus } from '@/services/ce';
import { Button, Divider, Form, Input, message, Modal, Radio } from 'antd';
import classNames from 'classnames';
import React, { FC, Fragment, memo, useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.module.scss';

const appealStatusOptions = [
  { label: '申诉成功', value: 'success' },
  { label: '驳回申诉', value: 'reject' },
];

interface IProps {
  hitRuleList?: any[];
  serviceId: string;
  tenantNo: string;
  handleResult?: () => Promise<unknown>;
}

const Appeal: FC<IProps> = ({ tenantNo, hitRuleList, serviceId, handleResult }) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const appealData = useMemo(() => {
    return {
      isShowAppealBtn: hitRuleList?.some((v: any) => v.hasAppeal),
      isAppeal: hitRuleList?.every((v: any) => !v.appealStatus),
    };
  }, [hitRuleList]);

  const onSubmit = useCallback(async () => {
    const values = await form.validateFields();
    try {
      setLoading(true);
      const res = await appealStatus({
        tenantNo,
        serviceId,
        appealList: values.appealList?.map((v: any) => ({
          ruleId: v.ruleId,
          appealStatus: v.appealStatus,
          reason: v.appealReason,
          ...(v.appealStatus === 'reject' ? { rejectReason: v.rejectReason } : {}),
        })),
      });
      if (res?.data?.success) {
        handleResult?.()?.then?.(() => {
          message.success('申诉成功');
          setLoading(false);
          setVisible(false);
        });
      } else {
        message.error('申诉失败');
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  }, [serviceId, tenantNo, form, handleResult]);

  useEffect(() => {
    if (visible && hitRuleList?.length) {
      form.setFieldsValue({
        appealList: hitRuleList?.map((v: any) => ({
          ruleId: v.ruleId,
          ruleName: v.ruleName,
          appealReason: v.appealReason,
          appealStatus: v.appealStatus || undefined,
          rejectReason: v.rejectReason,
        })),
      });
    } else {
      form.resetFields();
    }
  }, [hitRuleList, form, visible]);

  return appealData.isShowAppealBtn ? (
    <>
      <Button type="primary" size="small" className={styles.appealBtn} onClick={() => setVisible(true)}>
        {appealData.isAppeal ? '申诉' : '查看申诉'}
      </Button>
      <Modal
        title={appealData.isAppeal ? '申诉处理' : '查看申诉'}
        onCancel={() => setVisible(false)}
        open={visible}
        destroyOnClose
        footer={appealData.isAppeal ? undefined : null}
        confirmLoading={loading}
        onOk={onSubmit}
        className={styles.modal}
        okText="提交"
      >
        <Form form={form} className={styles.form}>
          <Form.List name="appealList">
            {(fields) => (
              <>
                {fields.map(({ key, name, ...restField }, index) => (
                  <Fragment key={key}>
                    <Form.Item label={`申诉规则${index + 1}`} {...restField} name={[name, 'ruleName']}>
                      <p>{hitRuleList?.[name]?.ruleName}</p>
                    </Form.Item>
                    <Form.Item label={`申诉原因${index + 1}`} {...restField} name={[name, 'appealReason']}>
                      <p>{hitRuleList?.[name]?.appealReason || '-'}</p>
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'appealStatus']}
                      label={`处理结果${index + 1}`}
                      rules={appealData.isAppeal ? [{ required: true, message: '请选择' }] : undefined}
                      className={classNames({ [styles.appealStatus]: appealData.isAppeal })}
                    >
                      {appealData.isAppeal ? (
                        <Radio.Group options={appealStatusOptions} />
                      ) : (
                        <p>{appealStatusOptions.find((v) => v.value === hitRuleList?.[name]?.appealStatus)?.label}</p>
                      )}
                    </Form.Item>
                    <Form.Item noStyle shouldUpdate>
                      {({ getFieldValue }) => {
                        const appealStatus = getFieldValue(['appealList', name, 'appealStatus']);
                        return (
                          <>
                            {appealStatus === 'reject' && (
                              <Form.Item
                                {...restField}
                                name={[name, 'rejectReason']}
                                label={`驳回原因${index + 1}`}
                                rules={appealData.isAppeal ? [{ required: true, message: '请输入' }] : undefined}
                                className={classNames({ [styles.appealStatus]: appealData.isAppeal })}
                              >
                                {appealData.isAppeal ? (
                                  <Input.TextArea placeholder="请输入" rows={3} />
                                ) : (
                                  <p>{hitRuleList?.[name]?.rejectReason || '-'}</p>
                                )}
                              </Form.Item>
                            )}
                          </>
                        );
                      }}
                    </Form.Item>
                    {index !== fields.length - 1 && <Divider />}
                  </Fragment>
                ))}
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </>
  ) : null;
};

export default memo(Appeal);
