.container {
  border-radius: 8px;
  background-color: #fff;
  margin-left: 20px;
  margin-right: 20px;
  width: calc(100% - 40px - 32px);
  padding: 10px 16px 0;
  margin-bottom: 8px;
  color: #1d2129;
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
  }
  .switch {
    display: flex;
    align-items: center;
    margin-left: 20px;
    font-size: 12px;
    white-space: nowrap;
    :global {
      .ant-switch {
        margin-right: 5px;
      }
    }
  }
  .title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .popover {
      :global {
        .ant-popover-inner {
          padding: 4px;
          .ant-popover-inner-content {
            max-height: 210px;
            overflow-y: auto;
          }
        }
      }
      .popoverItem {
        color: #0e121b;
        font-size: 14px;
        line-height: 32px;
        padding: 0 8px;
        &:not(:last-child) {
          margin-bottom: 4px;
        }
      }
    }
    .defaultTag {
      border-color: #e1e4ea;
      background: #f5f7fa;
      color: #2b303b;
      &:last-child {
        margin-right: 0;
      }
    }
    .returnTag {
      border-color: #ffd5c0;
      background: #fff1eb;
      color: #d05e25;
    }
    :global {
      .ant-tag {
        font-size: 12px;
        font-weight: 500;
        cursor: default;
      }
    }
  }
  :global {
    .ant-tabs {
      margin-top: 8px;
      .ant-tabs-nav {
        margin-bottom: 0 !important;
        &::before {
          border: none;
        }
      }
    }
  }
}

.modal {
  :global {
    .ant-modal-body {
      overflow-y: auto;
      max-height: 400px;
    }
  }
}
.appealBtn {
  &:global(.ant-btn) {
    background: #ffa06b;
    margin-left: 10px;
    box-shadow: none !important;
    font-size: 14px;
    &:hover,
    &:active {
      background: #ffa06b !important;
    }
  }
}

.form {
  :global {
    .ant-form-item {
      margin-bottom: 2px;
      p {
        margin: 0;
      }
    }
  }
  .appealStatus {
    margin-bottom: 24px;
    :global {
      .ant-form-item-label {
        margin-left: -8px;
      }
    }
  }
}
