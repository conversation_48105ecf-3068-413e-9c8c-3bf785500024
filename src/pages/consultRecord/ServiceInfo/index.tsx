import Back from '@/assets/back.svg';
import { Popover, Switch, Tabs, Tag, Tooltip } from 'antd';
import React, { FC, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TIMEOUT_NUM } from '../index';
import Appeal from './Appeal';
import styles from './index.module.scss';

export const USER_REDLINE_STATUS = 'userRedlineStatus';
export const REAL_TIME_USER_REDLINE_STATUS = 'realTimeUserRedlineStatus';
export const getUserRedlineStatus = (isRealTime?: boolean) => {
  return localStorage.getItem(isRealTime ? REAL_TIME_USER_REDLINE_STATUS : USER_REDLINE_STATUS) || undefined;
};
export const setUserRedlineStatus = (value: string, isRealTime?: boolean) => {
  localStorage.setItem(isRealTime ? REAL_TIME_USER_REDLINE_STATUS : USER_REDLINE_STATUS, value);
};
export const clearUserRedlineStatus = (isRealTime?: boolean) => {
  localStorage.removeItem(isRealTime ? REAL_TIME_USER_REDLINE_STATUS : USER_REDLINE_STATUS);
};

interface IProps {
  tenantNo: string;
  serviceInfo?: any;
  sessionId?: string;
  isIframe?: boolean;
  isContainer?: boolean;
  goBack?: () => void;
  setSessionId?: (key: string) => void;
  ControlsRender?: React.ReactNode;
  handleGetQualityTaskResult?: (...args: any[]) => void;
  isRealTime?: boolean;
}

const ServiceInfo: FC<IProps> = ({
  tenantNo,
  serviceInfo,
  sessionId,
  isIframe,
  isContainer,
  goBack,
  setSessionId,
  ControlsRender,
  handleGetQualityTaskResult,
  isRealTime,
}) => {
  const [showEllipsis, setShowEllipsis] = useState(false);
  const [isSwitchLoading, setIsSwitchLoading] = useState(false);
  const [checked, setChecked] = useState(getUserRedlineStatus(isRealTime) === '1');
  const containerRef = useRef<HTMLDivElement>(null);
  const containerListRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<any>(null);

  const tabItems = useMemo(() => {
    return serviceInfo?.sessionIds?.map((id: any) => {
      return {
        key: id,
        label: `${serviceInfo?.sessionMap[id] === '2' ? '回访' : ''}会话ID ${id}`,
        children: null,
      };
    });
  }, [JSON.stringify(serviceInfo?.sessionIds || []), JSON.stringify(serviceInfo?.sessionMap || {})]);

  const hitRuleList = useMemo(() => {
    return (
      (typeof serviceInfo?.hitRules === 'string'
        ? serviceInfo?.hitRules?.split(',')?.map((v: string) => ({ ruleId: v, ruleName: v }))
        : serviceInfo?.hitRules) || []
    );
  }, [serviceInfo?.hitRules]);

  const appealHitRuleList = useMemo(() => {
    return hitRuleList.filter((v: any) => v.hasAppeal);
  }, [hitRuleList]);

  const returnVisitHitRuleList = useMemo(() => {
    return serviceInfo?.returnVisitHitRules?.split(',') || [];
  }, [serviceInfo?.returnVisitHitRules]);

  const handleResult = useCallback(
    (userRedlineStatus?: '0' | '1') => {
      return new Promise((resolve) => {
        clearTimeout(timerRef.current);
        timerRef.current = setTimeout(() => {
          resolve(true);
          handleGetQualityTaskResult?.(true, userRedlineStatus);
        }, TIMEOUT_NUM);
      });
    },
    [handleGetQualityTaskResult]
  );

  const onSwitchChange = useCallback(
    (isChecked: boolean) => {
      setIsSwitchLoading(true);
      handleResult(isChecked ? '1' : '0')
        .then(() => {
          setChecked(isChecked);
        })
        .finally(() => setIsSwitchLoading(false));
    },
    [handleResult]
  );

  const checkOverflow = useCallback(() => {
    if (containerRef.current && containerListRef.current) {
      const containerWidth = containerRef.current.offsetWidth;
      const containerListWidth = containerListRef.current.offsetWidth;
      setShowEllipsis(containerListWidth > containerWidth);
    }
  }, []);

  useEffect(() => {
    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [hitRuleList]);

  return (
    <div
      className={styles.container}
      style={isIframe ? { marginLeft: 0, marginRight: 0, width: 'calc(100% - 32px)' } : {}}
    >
      <div className={styles.header}>
        <div className={styles.title}>
          {(!isIframe || isContainer) && (
            <img onClick={goBack} style={{ marginRight: 10, cursor: 'pointer' }} src={Back} />
          )}
          <span style={{ marginRight: 8, whiteSpace: 'nowrap' }}>商机ID：{serviceInfo?.serviceId}</span>
          {!!returnVisitHitRuleList?.length && (
            <Popover
              placement="bottom"
              overlayClassName={styles.popover}
              getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
              content={
                <>
                  {returnVisitHitRuleList.map((str: string, index: number) => (
                    <div className={styles.popoverItem} key={`${str}__${index}`}>
                      {str}
                    </div>
                  ))}
                </>
              }
            >
              <Tag className={styles.returnTag}>回访结果({returnVisitHitRuleList.length})</Tag>
            </Popover>
          )}
          <Tooltip
            {...(showEllipsis ? {} : { open: false })}
            title={hitRuleList.map((v: any) => v.ruleName).join('，')}
            placement="bottom"
            overlayStyle={{ maxWidth: '30vw' }}
          >
            <div ref={containerRef} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
              <span ref={containerListRef}>
                {hitRuleList.map((item: any, index: number) => (
                  <Tag className={styles.defaultTag} key={`${item.ruleId}__${index}`}>
                    {item.ruleName}
                  </Tag>
                ))}
              </span>
            </div>
          </Tooltip>
          <Appeal
            tenantNo={tenantNo}
            hitRuleList={appealHitRuleList}
            serviceId={serviceInfo?.serviceId}
            handleResult={handleResult}
          />
          {ControlsRender}
        </div>
        <div className={styles.switch}>
          <Switch size="small" checked={checked} loading={isSwitchLoading} onChange={onSwitchChange} />
          {checked ? '查看全部规则' : '隐藏业务规则'}
        </div>
      </div>
      <Tabs activeKey={sessionId} items={tabItems} onChange={(key) => setSessionId?.(key)} />
    </div>
  );
};

export default memo(ServiceInfo);
