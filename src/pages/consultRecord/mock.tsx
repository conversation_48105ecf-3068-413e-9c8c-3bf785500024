const MESSAGE_LIST_MOCK = 
    [
        {
            "msgId": "91fa62ca2b614714a9066c13b401b530",
            "source": "SYSTEM",
            "destination": "c0021641706",
            "messageType": "NTF",
            "date": 1711449998892,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "永祺为您服务",
            "contentType": 685,
            "ext": "",
            "others": null,
            "isRobot": 0
        },
        {
            "msgId": "240bce76c8f4429fac4462017855a52b",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711449999064,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "hi，你好，我是欢迎语\n",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "cs1703137880010",
            "source": "2311090000004675",
            "destination": "c0019947297",
            "messageType": "MSG",
            "date": 1703137880200,
            "category": "OFFLINE",
            "roomId": "c0019947297",
            "content": "",
            "contentType": 106,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "isCloseSession": "true",
                        "cardImageUrl": "https://imadmin2.zhongan.io/image/file/9e2eb0031510405d8a03242517695914",
                        "cardTitle": "xiancan测试服务卡片1",
                        "closeSessionAlertTit": "提示",
                        "closeSessionAlertContent": "即将离开聊天框，你可通过回退按键或人工入口，再次进入咨询",
                        "cardId": "2312200000000339",
                        "cardRedirectUrl": "https://static.zhongan.com/website/cs/video/zfblp.mp4?msgInfo=cs1703137880010_150060413414839",
                        "cardPivotalInfo1": "",
                        "cardPivotalInfo2": "",
                        "closeSessionAlertBtn1": "取消",
                        "closeSessionAlertBtn2": "我知道了",
                        "msgInfo": "cs1703137880010_150060413414839",
                        "cardAssistDesc": "desc 1234",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "isCloseSession": "true",
                    "cardImageUrl": "https://imadmin2.zhongan.io/image/file/9e2eb0031510405d8a03242517695914",
                    "cardTitle": "xiancan测试服务卡片1",
                    "closeSessionAlertTit": "提示",
                    "closeSessionAlertContent": "即将离开聊天框，你可通过回退按键或人工入口，再次进入咨询",
                    "cardId": "2312200000000339",
                    "cardRedirectUrl": "https://static.zhongan.com/website/cs/video/zfblp.mp4?msgInfo=cs1703137880010_150060413414839",
                    "cardPivotalInfo1": "",
                    "cardPivotalInfo2": "",
                    "closeSessionAlertBtn1": "取消",
                    "closeSessionAlertBtn2": "我知道了",
                    "msgInfo": "cs1703137880010_150060413414839",
                    "cardAssistDesc": "desc 1234",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "cs1703137882171",
            "source": "2311090000004675",
            "destination": "c0019947297",
            "messageType": "MSG",
            "date": 1703137882310,
            "category": "OFFLINE",
            "roomId": "c0019947297",
            "content": "",
            "contentType": 106,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "cardId": "1910080000003896",
                        "cardRedirectUrl": "众安百万防癌医疗险?msgInfo=cs1703137882171_150060413414839",
                        "cardPivotalInfo1": "",
                        "cardPivotalInfo2": "",
                        "cardImageUrl": "https://imadmin2.zhongan.io/image/file/244022ff547d42cd9ecb19cf42fdd683",
                        "cardTitle": "众安百万医疗险",
                        "msgInfo": "cs1703137882171_150060413414839",
                        "cardAssistDesc": "众安百万医疗险",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "cardId": "1910080000003896",
                    "cardRedirectUrl": "众安百万防癌医疗险?msgInfo=cs1703137882171_150060413414839",
                    "cardPivotalInfo1": "",
                    "cardPivotalInfo2": "",
                    "cardImageUrl": "https://imadmin2.zhongan.io/image/file/244022ff547d42cd9ecb19cf42fdd683",
                    "cardTitle": "众安百万医疗险",
                    "msgInfo": "cs1703137882171_150060413414839",
                    "cardAssistDesc": "众安百万医疗险",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "773a533bc3ee4468b36d7b2a5ef8e2a1",
            "source": "SYSTEM",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450000593,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": " 开始进入 前置托管 ",
            "contentType": 697,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "hostedScene": "",
                        "hostedTime": "1",
                        "aiStatus": "0",
                        "bizType": "9",
                        "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"\",\"postContent\":\"\"}",
                        "textArea": "[]",
                        "bizArea": "[]",
                        "params": ""
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "hostedScene": "",
                    "hostedTime": "1",
                    "aiStatus": "0",
                    "bizType": "9",
                    "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"\",\"postContent\":\"\"}",
                    "textArea": "[]",
                    "bizArea": "[]",
                    "params": ""
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "72b29ad0d7974adb9e0944837d40c250",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450003207,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "您好，您已进入众安保险人工服务，我是永祺，很高兴为您服务，请问有什么可以帮您！",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "543e8809-cce6-4d84-ac60-ab92270cbfcb",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450014478,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "你好！我想退保",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "145ab6d036d64f19ae938ee29a132223",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450039339,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "亲，麻烦提供一下手机号，以便我们更好地为您服务。",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "28446cf5-1ca2-4d2d-bf18-2eb66f454c07",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450041896,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "***********",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "c7c0cba3bf7e4bcdb79ed6b5795da870",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450061933,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "亲，请您再提供一下您的保单号或者身份证号，这样我们可以更好地为您服务。",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "42aac98b-eb8d-4627-9cc6-a41a1d915093",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450064373,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "310000200009109272",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "4c00a5469d604d11b37be5cd980b889c",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450091710,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "您好，请问是这个保单吗？\n保单号：IH1100014663289417\n产品名称：惠医保（年缴版）\n投保日期：2024-03-09 00:00:00",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "9dc5f58f-2e7b-4e85-aa62-2d7b29eb07f4",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450094670,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "对，是这个",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "4cec553fb4254092809fefbd7d3ab45d",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450117436,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "亲 请问您是投保人本人吗",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "2e4766a1cfa34b89bbfcacf9df9944a2",
            "source": "SYSTEM",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450118028,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "前置托管完成",
            "contentType": 697,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "hostedScene": "AUTOMATED_HOSTING",
                        "hostedTime": "99",
                        "aiStatus": "8",
                        "bizType": "9",
                        "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"退保\",\"postContent\":\"\"}",
                        "textArea": "[{\"label\":\"进线意图\",\"value\":\"客户想要办理退保相关的业务\"}]",
                        "bizArea": "[{\"label\":\"手机号\",\"value\":\"***********\"},{\"label\":\"证件号\",\"value\":\"310000200009109272\"},{\"label\":\"保单号\",\"value\":\"IH1100014663289417\"}]",
                        "params": "{\"nextHostedScene\": \"BUSINESS_HOSTING\", \"sceneId\": 59, \"sceneName\": \"退保\"}"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "hostedScene": "AUTOMATED_HOSTING",
                    "hostedTime": "99",
                    "aiStatus": "8",
                    "bizType": "9",
                    "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"退保\",\"postContent\":\"\"}",
                    "textArea": "[{\"label\":\"进线意图\",\"value\":\"客户想要办理退保相关的业务\"}]",
                    "bizArea": "[{\"label\":\"手机号\",\"value\":\"***********\"},{\"label\":\"证件号\",\"value\":\"310000200009109272\"},{\"label\":\"保单号\",\"value\":\"IH1100014663289417\"}]",
                    "params": "{\"nextHostedScene\": \"BUSINESS_HOSTING\", \"sceneId\": 59, \"sceneName\": \"退保\"}"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "6f5ac71558214654aa3c1e8cb0769e77",
            "source": "SYSTEM",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450118834,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": " 开始进入 业务托管 ",
            "contentType": 697,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "hostedScene": "",
                        "hostedTime": "1",
                        "aiStatus": "0",
                        "bizType": "9",
                        "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"\",\"postContent\":\"\"}",
                        "textArea": "[]",
                        "bizArea": "[]",
                        "params": ""
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "hostedScene": "",
                    "hostedTime": "1",
                    "aiStatus": "0",
                    "bizType": "9",
                    "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"\",\"postContent\":\"\"}",
                    "textArea": "[]",
                    "bizArea": "[]",
                    "params": ""
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "2ad42540-f622-4665-93bf-6981923ca315",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450119587,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "是的",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "5d3259439c404db899b91e8623a73bd3",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450141255,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "您好，请问您现在是出于什么原因考虑要退保呢？",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "07dfd032-8fdf-48ed-8aec-a91eab064e82",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450143881,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "退保，不想使用了",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "ca0586724df541b091de85b81d41321c",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450167466,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "保险建议您提前配置，可以分担生活中的风险，退保后若再投保需要重新审核，若不符合健康告知则无法投保，此保险保障全面性价比高，请问您愿意继续持有保单吗?",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "c6180431-20aa-4bb7-bff5-62132f83ea05",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450170112,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "麻烦帮我退保，一会我有事，麻烦快些",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "9fced5e425d640dca02b3b5e0867f4a8",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450193406,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "您好，截止目前您的保单可退金额是151.83元，现在为您处理退保，可以吗？",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "d8dd88b4-48a7-42e5-9690-e8c36a3cae57",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450195116,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "可以",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "633f66c72ced4ea892d365a31851f93b",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450218768,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "亲，为了方便您更快完成退保，我们提供了自助退保服务，您可以接受自助办理退保吗？",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "5d445610-9a44-4cde-8213-12f7f7f64e7c",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450221196,
            "category": "OFFLINE",
            "roomId": "c0021641706",
            "content": "不接受，我不会自助，你帮我办一下",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "sensitives": [],
                "others": [
                    {
                        "usedAsr": "N"
                    }
                ]
            },
            "others": [
                {
                    "usedAsr": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "acc4630886f846f0a86147071be632ea",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450246544,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "为保障您保单的信息安全，需要您配合上传身份证正反面照片",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "a1ff2770-d827-4df7-9457-7757fd72c300",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450248375,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "https://im2-test.zhongan.io/image/file/70d7f8ee2fa24102870848a99668471d.jpeg?width=undefined&height=undefined",
            "contentType": 101,
            "ext": "",
            "others": null,
            "isRobot": 0
        },
        {
            "msgId": "60be39b52ddd49c392f649a7b2c15f20",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450280011,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "亲，非常感谢您提供了身份证正面照片，现在还需要您上传身份证反面照片，以便我们更快地为您办理退保。",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "1b85696a-a00f-4719-83af-9aca31e5f8e1",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "MSG",
            "date": 1711450283118,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "https://im2-test.zhongan.io/image/file/9f4f123555424ecbac4436c32e4c6a52.png?width=undefined&height=undefined",
            "contentType": 101,
            "ext": "",
            "others": null,
            "isRobot": 0
        },
        {
            "msgId": "0824d9e15aa14ea4bf8284ffe04f04b6",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450311366,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "您的退保已受理，我司将于1~3个工作日内完成退保操作，烦请您后续留意退款到账情况。",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "8669ea824c2d4c2682a7218a62e9d92a",
            "source": "SYSTEM",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450312024,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "业务托管完成",
            "contentType": 697,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "hostedScene": "BUSINESS_HOSTING",
                        "hostedTime": "169",
                        "aiStatus": "8",
                        "bizType": "9",
                        "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"退保\",\"postContent\":\"\"}",
                        "textArea": "[{\"label\":\"进线意图\",\"value\":\"客户想要办理退保相关的业务\"}]",
                        "bizArea": "[{\"label\":\"手机号\",\"value\":\"***********\"},{\"label\":\"证件号\",\"value\":\"310000200009109272\"},{\"label\":\"保单号\",\"value\":\"IH1100014663289417\"}]",
                        "params": "{\"nextHostedScene\": \"POST_CONVERSATION_HOSTING\", \"sceneId\": 59, \"sceneName\": \"退保\"}"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "hostedScene": "BUSINESS_HOSTING",
                    "hostedTime": "169",
                    "aiStatus": "8",
                    "bizType": "9",
                    "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"退保\",\"postContent\":\"\"}",
                    "textArea": "[{\"label\":\"进线意图\",\"value\":\"客户想要办理退保相关的业务\"}]",
                    "bizArea": "[{\"label\":\"手机号\",\"value\":\"***********\"},{\"label\":\"证件号\",\"value\":\"310000200009109272\"},{\"label\":\"保单号\",\"value\":\"IH1100014663289417\"}]",
                    "params": "{\"nextHostedScene\": \"POST_CONVERSATION_HOSTING\", \"sceneId\": 59, \"sceneName\": \"退保\"}"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "628e9c7a8b5649878b2b420e6b47693a",
            "source": "SYSTEM",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450312868,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": " 开始进入 结尾托管 ",
            "contentType": 697,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "hostedScene": "",
                        "hostedTime": "1",
                        "aiStatus": "0",
                        "bizType": "9",
                        "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"\",\"postContent\":\"\"}",
                        "textArea": "[]",
                        "bizArea": "[]",
                        "params": ""
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "hostedScene": "",
                    "hostedTime": "1",
                    "aiStatus": "0",
                    "bizType": "9",
                    "headerArea": "{\"labelContent\":[\"\"],\"preContent\":\"\",\"textContent\":\"\",\"postContent\":\"\"}",
                    "textArea": "[]",
                    "bizArea": "[]",
                    "params": ""
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "82c771bcc42c4333a894357eb1ae59a4",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450313381,
            "category": "OFFLINE",
            "roomId": "c0021641706",
            "content": "很荣幸为您服务，请问还有其他需要为您效劳的吗?",
            "contentType": 100,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "aiRemark": "智能托管",
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "aiRemark": "智能托管",
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "6ea104525c574ea48c2d288bb667a6cb",
            "source": "2307040000000299",
            "destination": "c0021641706",
            "messageType": "MSG",
            "date": 1711450581754,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "长时间没有收到您的回复，稍后将退出人工客服",
            "contentType": 613,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "msgRemark": "4"
                    }
                ]
            },
            "others": [
                {
                    "msgRemark": "4"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "e30b3fe6b62a464e8ea5213c26ea84d3",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450581754,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "用户无应答，即将关闭会话",
            "contentType": 666,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "status": "1"
                    }
                ]
            },
            "others": [
                {
                    "status": "1"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "3c1619865b5d47ffb165b9b233cf8b24",
            "source": "SYSTEM",
            "destination": "c0021641706",
            "messageType": "NTF",
            "date": 1711450612407,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "请评价本次人工服务",
            "contentType": 6811,
            "ext": {
                "evaluation": true,
                "others": [
                    {
                        "dislikeLabels": "[{\"dislikeLabelContent\":\"态度恶劣\",\"dislikeLabelDetails\":[{\"id\":1709799069772,\"label\":\"不用心\"},{\"id\":1709799073972,\"label\":\"态度差\"},{\"id\":1709799090784,\"label\":\"效率低\"},{\"id\":1709799098546,\"label\":\"功能乱\"},{\"id\":1709799108306,\"label\":\"狗的很\"}]}]"
                    },
                    {
                        "satisfiedLabels": "[{\"id\":1709799036300,\"label\":\"态度好\"},{\"id\":1709799041041,\"label\":\"负责任\"},{\"id\":1709799049107,\"label\":\"反应快\"},{\"id\":1709799117303,\"label\":\"人好看\"},{\"id\":1709799125102,\"label\":\"语言温柔\"},{\"id\":1709799133675,\"label\":\"服务周到\"}]"
                    },
                    {
                        "feedbackRequired": "N"
                    },
                    {
                        "solutionFeedback": "{\"2312190000000210\":\"没结局\"}"
                    },
                    {
                        "solutionInviteContent": "评价吧"
                    },
                    {
                        "solutionRequired": "N"
                    }
                ]
            },
            "others": [
                {
                    "dislikeLabels": "[{\"dislikeLabelContent\":\"态度恶劣\",\"dislikeLabelDetails\":[{\"id\":1709799069772,\"label\":\"不用心\"},{\"id\":1709799073972,\"label\":\"态度差\"},{\"id\":1709799090784,\"label\":\"效率低\"},{\"id\":1709799098546,\"label\":\"功能乱\"},{\"id\":1709799108306,\"label\":\"狗的很\"}]}]"
                },
                {
                    "satisfiedLabels": "[{\"id\":1709799036300,\"label\":\"态度好\"},{\"id\":1709799041041,\"label\":\"负责任\"},{\"id\":1709799049107,\"label\":\"反应快\"},{\"id\":1709799117303,\"label\":\"人好看\"},{\"id\":1709799125102,\"label\":\"语言温柔\"},{\"id\":1709799133675,\"label\":\"服务周到\"}]"
                },
                {
                    "feedbackRequired": "N"
                },
                {
                    "solutionFeedback": "{\"2312190000000210\":\"没结局\"}"
                },
                {
                    "solutionInviteContent": "评价吧"
                },
                {
                    "solutionRequired": "N"
                }
            ],
            "isRobot": 0
        },
        {
            "msgId": "792487b77d594f80921ea1266477c209",
            "source": "SYSTEM",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450612551,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "邀请客户评价",
            "contentType": 681,
            "ext": {
                "evaluation": false
            },
            "isRobot": 0
        },
        {
            "msgId": "cdced5091046400b8fc8f61f82abda02",
            "source": "SYSTEM",
            "destination": "c0021641706",
            "messageType": "NTF",
            "date": 1711450612696,
            "category": "OFFLINE",
            "roomId": "c0021641706",
            "content": "本次会话已超时结束",
            "contentType": 613,
            "ext": "",
            "others": null,
            "isRobot": 0
        },
        {
            "msgId": "e1aa90a1c245419395e303a6a23e6141",
            "source": "c0021641706",
            "destination": "2307040000000299",
            "messageType": "NTF",
            "date": 1711450613264,
            "category": "HISTORY",
            "roomId": "c0021641706",
            "content": "客户长时间未回复，本次会话已超时结束",
            "contentType": 666,
            "ext": {
                "evaluation": false,
                "others": [
                    {
                        "status": "3"
                    }
                ]
            },
            "others": [
                {
                    "status": "3"
                }
            ],
            "isRobot": 0
        }
    ];
const MOCK_CONSULT_RECORD_DETAIL = [{
userId: `12345`,
hitRule: `因坐席自身原因导致投诉`,
recordAnalyze: `客户多次表达对客服的不满， 归因于客服的服务技巧、态度、推诿、个人能力等问题`,
isHit: `命中`,
level: `高风险`,
sensitiveWord: `百万医疗`,
score: `-5`,
status: 1,//待联调
timestamp: "2024-3-27 15:31:30",
},
{
    userId: `123456`,
    hitRule: `坐席态度恶劣`,
    recordAnalyze: `客户多次表达对客服的不满， 归因于客服的服务技巧、态度、推诿、个人能力等问题`,
    isHit: `未命中`,
    level: `高风险`,
    sensitiveWord: `非常满意`,
    score: `-6`,
    status: 2,
},
{
    userId: `123457`,
    hitRule: `因坐席自身原因导致投诉`,
    recordAnalyze: `客户多次表达对客服的不满， 归因于客服的服务技巧、态度、推诿、个人能力等问题`,
    isHit: `命中`,
    level: `高风险`,
    sensitiveWord: `医疗`,
    score: `-10`,
    status: 3,
    timestamp: "2024-3-27 13:31:36",
    },
    {
        userId: `123456789`,
        hitRule: `坐席态度恶劣`,
        recordAnalyze: `客户多次表达对客服的不满， 归因于客服的服务技巧、态度、推诿、个人能力等问题`,
        isHit: `未命中`,
        level: `高风险`,
        sensitiveWord: `尊享e升`,
        score: `-6`,
        status: 0,
        timestamp: "2024-3-27 15:31:33",
    },
    {
        userId: `123457000111`,
        hitRule: `因坐席自身原因导致投诉`,
        recordAnalyze: `客户多次表达对客服的不满， 归因于客服的服务技巧、态度、推诿、个人能力等问题`,
        isHit: `命中`,
        level: `高风险`,
        sensitiveWord: `客服`,
        score: `-10`,
        status: 0,
        },
        {
            userId: `123457000`,
            hitRule: `因坐席自身原因导致投诉`,
            recordAnalyze: `客户多次表达对客服的不满， 归因于客服的服务技巧、态度、推诿、个人能力等问题`,
            isHit: `命中`,
            level: `高风险`,
            sensitiveWord: `服务`,
            score: `-10`,
            status: 0,
            },

];

const MOCK_CONSULT_lIST_MSG =[
        {
          "beginTime": "2024-03-27 16:55:15",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "hi，你好，我是欢迎语\n",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "47ed2e80e343427d834e31cff8cacaa6",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
            "beginTime": "2024-03-27 19:25:10",
            "beginTimeOffset": "",
            "endTimeOffset": "",
            "text": "https://imadmin2.zhongan.io/image/file/9324071e8bb843adaf9b185333b8ebb8.jpeg",
            "silenceDuration": "",
            "sensitiveStr": [],
            "side": 2,
            "isContainSens": "",
            "isContainRealSens": "",
            "msgId": "cs3437a2a4-c69f-4d85-8432-1a3d18783a4f-1711538710765",
            "custormName": " 5970",
            "servantName": "闫坤 2307100000014163",
            "source": "2307100000014163",
            "messageType": 2,
            "showSensitives": []
          },
        {
          "beginTime": "2024-03-27 16:55:17",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": " 开始进入 前置托管 ",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "14705027a6994be29212ace656187832",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "SYSTEM",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:55:19",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "您好，您已进入众安保险人工服务，我是永祺，很高兴为您服务，请问有什么可以帮您！",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "f4f059048cca42d0afff220a20d0a790",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:55:50",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "百万医疗怎么卖？",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 1,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "8afef4e7-1a8d-4b97-8e40-c9ebc568e43a",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "c0021759793",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:56:01",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "前置托管完成",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "ef4c0077f8714ef98b68663bf9d495a8",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "SYSTEM",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:56:15",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "请问您想要咨询的是【无忧保成人综合意外（百万意外医疗版）】吗？",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "cs50be6eec-8fe1-4158-b08d-7697cfbec4b8-1711529775928",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:56:23",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "对的，还想问下尊享e升",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 1,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "1ceb3afb-f902-481d-a970-25e3b476e4b7",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "c0021759793",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:46",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": " 开始进入 结尾托管 ",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "a631c21a070e426c997d5b1408f35ed7",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "SYSTEM",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:49",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "很荣幸为您服务，请问还有其他需要为您效劳的吗?",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "edb20adc98f94deab063a83ff991821c",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:51",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "没问题了",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 1,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "9a49d460-c1e3-4131-a179-d471d5522dfa",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "c0021759793",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:56",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "好的呢。",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "fd4d014fca414ed1ad329a4fbb1a06c6",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:57",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "如果您没有其他的问题，我这边即将结束与您的会话",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "d7617cfa9853436eb2f73dd1b8a992cf",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:58",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "感谢您的咨询，麻烦您对[我的个人服务] 进行评价，点亮“非常满意”鼓励下我吧，祝您生活愉快，再见。",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "1cc135c3a156459688e291d823b54164",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:58",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "请评价本次人工服务",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "ada9bf872dda467591f8f3f17763e37e",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "SYSTEM",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:57:59",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "邀请客户评价",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "bac453f6f83e4507bd10133d72ea8128",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "SYSTEM",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:58:00",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "8b10619d5e524610b6453978bf7bb62a",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 16:58:00",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "这是我司人工客服的微信，您添加后可免排队咨询保险购买、条款咨询、进度查询等服务。专人为您解答，请点击卡片添加微信管家（企微工作时间9:00-18:00）",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "108c5cf5917e44c0978d26300f5919d7",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 17:02:08",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "用户无应答，即将关闭会话",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 1,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "51df2cf5cd0d45509acc3284475d9e2e",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "c0021759793",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 17:02:29",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "该客户已对本次会话做出了评价",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "ffd5fd56870a47858c083b82ab631829",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "SYSTEM",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 17:03:07",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 2,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "19dddd7493eb4e668b889b57730f0f1f",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "2307040000000299",
          "messageType": 1,
          "showSensitives": []
        },
        {
          "beginTime": "2024-03-27 17:03:07",
          "beginTimeOffset": "",
          "endTimeOffset": "",
          "text": "客户长时间未回复，本次会话已超时结束",
          "silenceDuration": "",
          "sensitiveStr": [],
          "side": 1,
          "isContainSens": "",
          "isContainRealSens": "",
          "msgId": "a95f2603d3fa481e8f875cc6c1d9c355",
          "custormName": " 4179",
          "servantName": "沈一线 2307040000000299",
          "source": "c0021759793",
          "messageType": 1,
          "showSensitives": []
        }
      ]
export {
    MESSAGE_LIST_MOCK,
    MOCK_CONSULT_RECORD_DETAIL,
    MOCK_CONSULT_lIST_MSG
  };