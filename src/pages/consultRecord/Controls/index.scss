.controls {
  display: flex;
  align-items: center;
  margin-left: 24px;

  :global {
    .ant-btn {
      width: 20px;
      height: 20px;
      border: none;
      background: #f8f8f8;
      border-radius: 4px;
      padding: 0;
      &.ant-btn-disabled,
      &:disabled {
        background: #f8f8f8 !important;
        svg {
          color: #c9cdd4;
        }
      }
      &:not(:last-child) {
        margin-right: 8px;
      }
      svg {
        width: 10px;
        height: 5px;
        color: #4e5969;
      }
      &:not(:disabled) {
        &:hover {
          box-shadow: none;
          background: #eff1f4 !important;
        }
        &:active {
          box-shadow: none;
          background: #f2f2fe !important;
          svg {
            color: var(--primary-color);
          }
        }
      }
    }
  }
}
