import { ReactComponent as <PERSON><PERSON><PERSON> } from '@/assets/jiantou.svg';
import { Button, Tooltip } from 'antd';
import React, { FC, memo } from 'react';
import styles from './index.scss';

interface IProps {
  onPrevBtn?: () => void;
  onNextBtn?: () => void;
  idData?: any;
}

const Controls: FC<IProps> = ({ onPrevBtn, onNextBtn, idData }) => {
  return (
    <div className={styles.controls}>
      <Tooltip title="上一条">
        <Button disabled={!idData?.preId} onClick={onPrevBtn}>
          <Jiantou />
        </Button>
      </Tooltip>
      <Tooltip title="下一条">
        <Button disabled={!idData?.nextId} onClick={onNextBtn}>
          <Jiantou style={{ transform: 'rotate(180deg)' }} />
        </Button>
      </Tooltip>
    </div>
  );
};

export default memo(Controls);
