import ConsultRecord from '@/pages/consultRecord';
import { drawerWidth, headerHeight } from '@/utils/constants';
import { Drawer, DrawerProps } from 'antd';
import classnames from 'classnames';
import React, { FC, useCallback, useEffect, useState } from 'react';
import styles from './index.module.scss';

interface IProps extends DrawerProps {
  drawerData: { open: boolean; id: number | null; serviceId?: string; curIndex?: number };
  setDrawerData: (
    value: React.SetStateAction<{
      open: boolean;
      id: null;
      serviceId?: string;
      curIndex?: number;
    }>
  ) => void;
  currentTenantNo?: string;
  isRealTime?: boolean;
  listData?: any[];
}

const ConsultRecordDrawer: FC<IProps> = ({
  rootClassName,
  drawerData,
  setDrawerData,
  currentTenantNo,
  mask = false,
  isRealTime,
  listData,
}) => {
  const [idData, setIdData] = useState<any>({});

  const getAdjacentIds = useCallback(() => {
    const { id, serviceId, curIndex } = drawerData || {};
    if (!listData?.length || !id)
      return { nextId: null, preId: null, nextServiceId: null, preServiceId: null, preIndex: null, nextIndex: null };

    const currentIndex = curIndex ?? listData?.findIndex((item) => item.sessionId === id);
    if (currentIndex === -1)
      return { nextId: null, preId: null, nextServiceId: null, preServiceId: null, preIndex: null, nextIndex: null };

    const findAdjacentId = (start: number, end: number, step: number) => {
      for (let i = start; i !== end; i += step) {
        const item = listData?.[i] || {};
        if (serviceId) {
          if (item.serviceId && item.serviceId !== serviceId) {
            return { id: item.sessionId, serviceId: item.serviceId, curIndex: i };
          }
        } else if (item.sessionId !== id) {
          return { id: item.sessionId, serviceId: item.serviceId, curIndex: i };
        }
      }
      return { id: null, serviceId: null, curIndex: null };
    };
    const { id: preId, serviceId: preServiceId, curIndex: preIndex } = findAdjacentId(currentIndex - 1, -1, -1);
    const {
      id: nextId,
      serviceId: nextServiceId,
      curIndex: nextIndex,
    } = findAdjacentId(currentIndex + 1, listData?.length, 1);
    return { nextId, nextServiceId, preId, preServiceId, preIndex, nextIndex };
  }, [drawerData, listData]);

  useEffect(() => {
    setIdData(listData ? getAdjacentIds() : null);
  }, [getAdjacentIds]);

  const onPrevBtn = useCallback(() => {
    if (!idData?.preId) return;
    setDrawerData((preState) => ({
      ...preState,
      id: idData.preId,
      serviceId: idData.preServiceId,
      curIndex: idData.preIndex,
    }));
  }, [setDrawerData, idData?.preId]);

  const onNextBtn = useCallback(() => {
    if (!idData?.nextId) return;
    setDrawerData((preState) => ({
      ...preState,
      id: idData.nextId,
      serviceId: idData.nextServiceId,
      curIndex: idData.nextIndex,
    }));
  }, [setDrawerData, idData?.nextId]);

  return (
    <Drawer
      rootClassName={classnames(styles.consultDrawer, rootClassName)}
      rootStyle={{ marginTop: headerHeight }}
      maskClosable={false}
      title={'会话详情'}
      width={drawerWidth}
      open={drawerData.open}
      push={false}
      mask={mask}
      onClose={() => setDrawerData({ open: false, id: null })}
    >
      {drawerData.open && (
        <ConsultRecord
          id={drawerData.id}
          tenantNo={currentTenantNo}
          isRealTime={isRealTime}
          goBack={() => setDrawerData({ open: false, id: null })}
          isContainer
          idData={idData}
          onPrevBtn={onPrevBtn}
          onNextBtn={onNextBtn}
        />
      )}
    </Drawer>
  );
};

export default ConsultRecordDrawer;
