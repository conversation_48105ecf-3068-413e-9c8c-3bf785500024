.container {
  flex: auto;
  display: flex;
  flex-flow: column nowrap;
  position: relative;
  overflow: hidden;
}

.consultContainer {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 20px - var(--header-height));
  padding-top: 20px;
  .topLine {
    padding: 16px;
    padding-bottom: 6px;
  }
}

.overwrap {
  flex: none;
  max-height: 154px;
  overflow-y: auto;
  overflow-x: hidden;
  background: #f1faf6;
  border-bottom: 1px solid #e5e5e5;
}
.consultRrecord {
  flex: 1;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 20px;
  overflow-y: auto;
  display: flex;
  .consultMessage,
  .consultInfo {
    border-radius: 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
  }
  .consultTitle {
    height: 40px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eff1f4;
    padding: 0 16px;
    justify-content: space-between;
    font-weight: 600;
  }
  .consultMessage {
    width: calc((100% - 8px) * 0.65);
    margin-right: 8px;
  }
  .consultInfo {
    width: calc((100% - 8px) * 0.35);
    .consultInfoTabs {
      :global {
        .ant-tabs-tab {
          padding: 11px 0;
          font-weight: 500;
          &:not(.ant-tabs-tab-active) {
            color: #4e5969;
          }
        }
      }
    }
  }
  .scrollWrapper {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }
  .consultFooter {
    margin: 0 16px;
    padding: 12px 0 16px;
    border-top: 1px solid #eff1f4;
    text-align: right;

    :global {
      .ant-btn {
        margin-left: 16px;
        &.ant-btn-default {
          background: #f2f3f5;
          border-color: #f2f3f5;
          color: #4e5969;

          &:hover {
            background: #f2f3f5;
            border-color: #f2f3f5;
          }
        }
      }
    }
    .consultFooterBtn {
      &:disabled {
        background: var(--primary-color);
        color: #fff;
        border: none;
        opacity: 0.65;
      }
    }
  }
}

.consultDrawer {
  :global {
    .ant-drawer-content-wrapper {
      box-shadow: none !important;
      .ant-drawer-header {
        display: none;
      }
      .ant-drawer-content {
        .ant-drawer-body {
          padding: 0;
          background-color: #f2f3f5;
        }
      }
    }
  }
}
