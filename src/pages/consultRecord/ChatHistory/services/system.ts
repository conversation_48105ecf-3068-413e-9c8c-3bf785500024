import { request } from 'utils/request';
import { Payload } from './types';
import {
  AddPersonEnterType,
  BatchDeleteType,
  PersonDeleteType,
  QualityPersonType,
  QueryPersonEnterType,
  QueryPersonType,
  SeatAddEnterType,
  SeatInfoDataType,
  SeatInfoEnterType,
  UpdatePersonEnterType,
} from './types/system';

/**
 * 新增质检员
 */
export function fetchAddPerson(params: Partial<AddPersonEnterType>) {
  return request.post<Payload>('/api/v1/lionk/person/add', params);
}

/**
 * 质检人员分页查询
 * @param params
 * @returns
 */
export function fetchQueryPerson(params: Partial<QueryPersonEnterType>) {
  return request.post<Payload<QueryPersonType>>('/api/v1/lionk/person/page', params);
}

/**
 * 查询质检员
 * @returns
 */
export function fetchQualityPerson() {
  return request.get<Payload<QualityPersonType[]>>('/api/v1/lionk/person/qualityPerson/list');
}

/**
 * 更新质检员
 * @param params
 * @returns
 */
export function fetchUpdatePerson(params: Partial<UpdatePersonEnterType>) {
  return request.post<Payload>('/api/v1/lionk/person/update', params);
}

/**
 * 坐席信息列表
 * @param params
 * @returns
 */
export function fetchSeatList(params: Partial<SeatInfoEnterType>) {
  return request.post<Payload<SeatInfoDataType>>('/api/v1/lionk/seat/page', params);
}

/**
 * 新增坐席
 * @param params
 * @returns
 */
export function fetchSeatAdd(params: Partial<SeatAddEnterType>) {
  return request.post<Payload>('/api/v1/lionk/seat/add', params);
}

/**
 * 坐席编辑
 * @param params
 * @returns
 */
export function fetchSeatEdit(params: Partial<SeatAddEnterType>) {
  return request.post<Payload>('/api/v1/lionk/seat/update', params);
}

/**
 * 删除坐席
 * @param params
 * @returns
 */
export function fetchBatchDelete(params: Partial<BatchDeleteType>) {
  return request.post<Payload>('/api/v1/lionk/seat/batchDelete', params);
}

/**
 * 删除权限
 * @param params
 * @returns
 */
export function fetchPersonDelete(params: Partial<PersonDeleteType>) {
  return request.post<Payload>('/api/v1/lionk/person/delete', params);
}
/**
 * 下载模板
 * @returns
 */
export function fetchTemplateDownload() {
  return request.post(
    '/api/v1/lionk/seat/excel/template/download',
    {},
    {
      responseType: 'arraybuffer',
      headers: { 'auto-message': 'nerver' },
    }
  );
}

/**
 * 批量导入专项任务配置excel
 * @param data
 * @returns
 */
export function batchImportSpecial(data: FormData) {
  return request.post<Payload>('/api/v1/lionk/seat/batchImport-special', data, {
    headers: { 'content-type': 'multipart/form-data' },
  });
}

/**
 * 专项任务配置----批量excel模板的下载
 * @returns
 */
export function batchDownloadImportSpecial() {
  return request.post(
    '/api/v1/lionk/seat/excel/template/download/batchImport-special',
    {},
    {
      responseType: 'arraybuffer',
      headers: { 'auto-message': 'nerver' },
    }
  );
}
