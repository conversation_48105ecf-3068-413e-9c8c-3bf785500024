import { request } from 'utils/request';
import { Payload } from './types';
import {
  AllocationListType,
  CustomFieldListAction,
  CustomFieldListAdd,
  CustomFieldListType,
  DicAddEnterType,
  TagUpdataEnterType,
  TemplateBatchDeleteType,
  TemplateDetailType,
  TemplateListEnterType,
  TemplateListType,
} from './types/configure';

/**
 * 配置管理-标签管理-编辑标签
 * @param status
 */
export function fetchTagUpdata(params: Partial<TagUpdataEnterType>) {
  return request.post<Payload>(`/api/v1/lionk/dic/tag/update`, params);
}

/**
 * 配置管理-标签管理-新增标签
 * @param status
 */
export function fetchTagAdd(params: Partial<TagUpdataEnterType>) {
  return request.post<Payload>(`/api/v1/lionk/dic/tag/add`, params);
}

/**
 * 配置管理-标签管理-问题类型
 * @param status
 */
export function fetchDicUpdata(params: Partial<TagUpdataEnterType>) {
  return request.post<Payload>(`/api/v1/lionk/dic/update`, params);
}

/**
 * 配置管理-标签管理-问题类型
 * @param status
 */
export function fetchDicAdd(params: Partial<DicAddEnterType>) {
  return request.post<Payload>(`/api/v1/lionk/dic/add`, params);
}

/**
 * 配置管理-评分表-评分表查询
 * @param status
 */

export function fetchTemplateList(params: Partial<TemplateListEnterType>) {
  return request.post<Payload<TemplateListType>>(`/api/v1/lionk/template/page`, params);
}

/**
 * 配置管理-评分表-评分表详情
 * @param status
 */

export function fetchTemplateDetail(bizTemplateId: number) {
  return request.get<Payload<TemplateDetailType>>(`/api/v1/lionk/template/query`, {
    params: { bizTemplateId },
  });
}

export function fetchAddTemplate(params: Partial<TemplateDetailType>) {
  return request.post<Payload>(`/api/v1/lionk/template/addTemplate`, params);
}
/**
 *
 * @param params 配置管理-评分表-批量删除
 * @returns
 */
export function fetchTemplateBatchDelete(params: Partial<TemplateBatchDeleteType>) {
  return request.post<Payload>(`/api/v1/lionk/template/batch/delete`, params);
}

/**
 *
 * @param params 配置管理-评分表-设为默认模版
 * @returns
 */

export function fetchSetDefaultTemplate(params: { templateId: number }) {
  return request.post<Payload>(`/api/v1/lionk/template/install/default/template`, params);
}

/**
 * 配置管理-自动分配策略 -根据条件获取对应任务分配策略租户配置表
 * @returns
 */
export function fetchAllocation() {
  return request.get<Payload<AllocationListType[]>>(`/api/v1/lionk/dic/allocation/tenantid`);
}

/**
 * 配置管理-自动分配策略 -编辑
 * @returns
 */

export function fetchAllocationUpdate(params: Partial<AllocationListType>) {
  return request.post<Payload>(`/api/v1/lionk/dic/allocation/update`, params);
}

/**
 * 配置管理-自定义字段 -列表
 * @returns
 */
export function fetchCustomFieldList(params: Partial<CustomFieldListType>) {
  return request.post<Payload>(`/api/v1/lionk/config/field/page`, params);
}

/**
 * 配置管理-自定义字段 -新增or修改
 * @returns
 */
export function fetchCustomFieldAddOrUpdate(params: Partial<CustomFieldListAdd>) {
  return request.post<Payload>(`/api/v1/lionk/config/field/add`, params);
}

/**
 * 配置管理-自定义字段 -生失效
 * @returns
 */
export function fetchCustomFieldAction(params: Partial<CustomFieldListAction>) {
  return request.post<Payload>(`/api/v1/lionk/config/field/effectiveAndInvalid`, params);
}
