/*
 * @Author: your name
 * @Date: 2022-01-06 17:56:22
 * @LastEditTime: 2022-01-13 10:55:45
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /za-ark-puma-static/src/services/foreign.ts
 */
import { request } from 'utils/request';
import { Payload } from './types';
import {
  AppealChatHistoryType,
  AppealTemplateEnterType,
  listTaskEnterType,
  listTaskType,
  TaskAppealDetailType,
  TaskAppealEnterType,
  TaskApproveEnterType,
  UserInfoType,
} from './types/foreign';
import { TemplateMessages } from './types/manualQuality';

/**
 * 质检查询-任务数量查询
 * @param
 * @returns
 */
export function fetchCount() {
  return request.get('/api/v1/piranha/task/outdated/count');
}

/**
 * 质检查询-最近任务详情查询
 * @param
 * @returns
 */
export function fetchNearDetail() {
  return request.get<Payload<TaskAppealDetailType>>('/api/v1/piranha/task/outdated/get');
}
/**
 * 质检查询-任务分页查询
 * @param params
 * @returns
 */
export function fetchListTask(params: Partial<listTaskEnterType>) {
  return request.post<Payload<listTaskType>>('/api/v1/piranha/task/listTask', params);
}
/**
 * 质检查询-详情
 * @param taskId
 * @returns
 */
export function fetchTaskDetail(taskId: number) {
  return request.get<Payload<TaskAppealDetailType>>('/api/v1/piranha/task/getDetail', {
    params: { taskId },
  });
}
/**
 * 申诉
 * @param params
 * @returns
 */
export function fetchTaskAppeal(params: Partial<TaskAppealEnterType>) {
  return request.post<Payload<UserInfoType>>(`/api/v1/piranha/task/appeal`, params);
}

/**
 * 审批
 * @param params
 * @returns
 */
export function fetchTaskApprove(params: Partial<TaskApproveEnterType>) {
  return request.post<Payload>(`/api/v1/piranha/task/approve`, params);
}

export function fetchUserInfo() {
  return request.post<Payload<UserInfoType>>(`/api/v1/piranha/user/userinfo`);
}

/**
 *获取语音文本
 * @param uniqueId
 * @returns
 */
export function fetchAppealChatHistory(uniqueId: string) {
  return request.get<Payload<AppealChatHistoryType[]>>(`/api/v1/piranha/task/getAudioText`, {
    params: { uniqueId },
  });
}

export function fetchAppealTemplate(params: Partial<AppealTemplateEnterType>) {
  return request.post<Payload<TemplateMessages[]>>(`/api/v1/piranha/task/queryTemplateMessageResult`, params);
}
