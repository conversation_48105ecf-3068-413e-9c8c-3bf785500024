import { request } from 'utils/request';
import { Payload } from './types';

/**
 * 获取根因质检事业部
 * @returns
 */
export function fetchAllOrgList() {
  return request.get<Payload>(`/api/v1/lionk/custom/approval-item/allOrg`);
}

/**
 * 获取根因质检根因类目
 * @returns
 */
export function fetchRootCausesByOrg(orgId: number) {
  return request.get<Payload>(`/api/v1/lionk/custom/approval-item/listRootCauseTree/${orgId}`);
}
