export interface QualityTaskEnter {
  begainTaskDate: string;
  bizLines: string[];
  definePoolIds: number;
  isQuality: number;
  limit: number;
  notDefinePoolIds: number[];
  qualityTaskLeader: number;
  seatCodes: string[];
  sortMap: SortMap;
  status: number;
  statusItem: number;
  taskId: number;
  taskType: number;
  teamIds: string[];
  tenantId: number;
  userId: number;
  userName: string;
}

interface SortMap {
  additionalProp1: string;
  additionalProp2: string;
  additionalProp3: string;
}
export interface QualityTaskType {
  assignOrg?: number; // 投诉质检带出事业部用于根因质检默认字段
  audioSessions: AudioSession[];
  bizCagetory: string;
  bizCagetoryName: string;
  custormGender: number;
  custormId: string;
  custormName: string;
  custormNick: string;
  custormPhone: string;
  deptGroupId: string;
  deptGroupName: string;
  groupId: string;
  groupName: string;
  id: number;
  policyNo: string;
  rootGroupId: string;
  rootGroupName: string;
  seatCode: string;
  servantName: string;
  source: string;
  tenantId: number;
  taskCode: string;
  extraData: any;
  taskComplete: TaskComplete;
  taskQualityStatus: string;
  complaintReturnVisitDTO: ComplaintReturnVisitDTO;
  sessionType: number[];
  complainantCertNo: number;
  taskPolicy: PolicyExtraType;
  taskOperLog: TaskOperLog[];
  poolDefineId: number;
  poolDefineName: string;
  statusItem: number;
}
export interface TaskOperLog {
  bizDesc: string;
  bizType: number;
  content: string;
  gmtCreated: string;
  id: number;
  operId: number;
  operName: string;
  operTime: string;
  taskId: number;
  tenantId: number;
}

export interface ComplaintReturnVisitDTO {
  approveType: number;
  reason: string;
  rejectedType: number;
  remark: string;
  preHandlerName: string;
  preHandlerGroupName: string;
}
export interface TaskComplete {
  beFollowType: number;
  comment: string;
  custormName: string;
  followReason: string;
  qualityResult: string;
  status: number;
  tags: Tag[];
  problemType: string;
  taskId: number;
  summary: string;
  reQualityFlag: number;
  rootCauseInfo?: any[];
  approveExtra: string;
}

interface Tag {
  tagId: number;
  tagName: string;
  taskId: number;
  taskQualityResultId: number;
  tenantId: number;
  type: number;
}
export interface AudioSession {
  bizCagetory: string;
  bizCagetoryName: string;
  blendKey: string;
  callType: number;
  custormId: string;
  custormName: string;
  custormPhone: string;
  dataType: number;
  deptGroupId: string;
  deptGroupName: string;
  endTime: string | number;
  groupId: string;
  groupName: string;
  id: number;
  mainUniqueId: string;
  ossLink: string;
  pageTag: number;
  provider: number;
  rootGroupId: string;
  rootGroupName: string;
  satisfaction: number;
  seatCode: string;
  servantName: string;
  startTime: string | number;
  // 是否被质检过 0 否 1 是
  isQuality: number;
  // 当前任务状态1 待初检 2 初检完成 3 复检完成 4 申诉/回访预审 5 申诉 6回访 7专项质检
  status: number;

  tagList: string[];
  tags: string;
  tenantId: number;
  totalDuration: number;
  uniqueId: string;
  taskId: number;
}
export interface TaskDetailType {
  avgResponseDuration: number;
  bizCagetory: string;
  bizCagetoryName: string;
  blendKey: string;
  callType: number;
  channelName: string;
  custormId: string;
  custormName: string;
  custormPhone: string;
  dataType: number;
  deptGroupId: string;
  deptGroupName: string;
  endTime: string;
  firstResponseDuration: number;
  groupId: string;
  groupName: string;
  id: number;
  isQuality: number;
  mainUniqueId: string;
  ossLink: string;
  pageTag: number;
  provider: number;
  rootGroupId: string;
  rootGroupName: string;
  satisfaction: number;
  seatCode: string;
  servantName: string;
  sourceChannel: string;
  startTime: string;
  status: number;
  tagList: string[];
  tags: string;
  taskId: number;
  tenantId: number;
  totalDuration: number;
  uniqueId: string;
  access: string;
  channelType: string;
  remark: string;
  label: string;
  accessName: string;
  extraData: any;
}

export interface QualityResultType {
  data: Datum[];
  violation: string;
  violationId: number;
}

interface Datum {
  keywords: string;
  msgId: string;
  msgTime: string;
  name: string;
  msg: string;
  subType: string;
  beginTimeOffset: number;
  endTimeOffset: number;
}

export interface AudioTextType {
  id?: number;
  beginTimeOffset: number;
  endTimeOffset: number;
  beginTime: string;
  custormName: string;
  isContainRealSens: string;
  isContainSens: string;
  messageType: number;
  msgId: string;
  sensitiveStr: string[];
  sensitives: Sensitive[];
  servantName: string;
  transName: string;
  side: number;
  silenceDuration: number;
  source: string;
  text: string;
  showSensitives?: string[];
  url?: string;
  isHit?: string;
}

// 联动
export interface AiQualityResult {
  data: Array<AiQualityResultDataList>;
  violation: string;
  violationId: number;
  status: number;
}

export interface AiQualityResultDataList {
  msgId: string;
  msg: string;
  msgTime: string;
  beginTimeOffset: number;
  endTimeOffset: number;
}

interface Sensitive {
  annoyType: string;
  effectiveObject: number;
  qaId: string;
  remindContent: string;
  subType: string;
  violateType: string;
  warnContent: string[];
  warnTime: string;
  warnTip: string;
  warnType: number;
}
export interface TaskType {
  beFollowType: number;
  comment: string;
  summary: string;
  reQualityFlag: number;
  custormName: string;
  followReason: string;
  status: number;
  tags: Tag[];
  taskId: number;
  rejectedDTO: RejectedDTO;
  isPreInspection: number;
}
export interface RejectedDTO {
  approveType: number;
  reason: string;
  rejectedType: number;
  remark: string;
  taskcode: string;
  taskId: number;
}
interface Tag {
  tagId: number;
  tagName: string;
  taskId: number;
  tenantId: number;
  type: number;
}
export interface TemplateTypeEnter {
  id?: number;
  taskId: number;
  status: number;
  seatCode?: string;
  queue?: string;
  templateMold?: number;
  tenantId?: number;
  taskType: number;
}
export interface TemplateType {
  checked: number;
  children: any[];
  conditionRelationExpression: string;
  conditionRelationType: string;
  creator: string;
  descri: string;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  isScoreUpper: string;
  modifier: string;
  nodeName: string;
  parentId: number;
  qualityResult: string;
  remark: string;
  score: number;
  scoreMode: number;
  scoreType: number;
  status: string;
  step: number;
  subScore: number | string;
  templateId: number;
  templateMessages: TemplateMessage[];
  templateType: number;
  tenantId: number;
  violationShow: string;
  readOnly: number;
  allowSelect: boolean;
  pachiras: any[];
  summary: string;
  fastModels: any[];
}

export interface TemplateMessage {
  id?: number;
  message: string;
  msgId: string;
  nodeId: number;
  nodeName: string;
  subScore?: number | string;
}
export interface TemplateEnterType {
  qualityPerson?: string;
  qualityPersonId?: number;
  qualityResult?: string;
  qualityType: number;
  scores: Score[];
  seatCode: string;
  seatName: string;
  sessionId: number;
  status: number;
  templateId: number;
  templateMessages?: TemplateMessages[];
}
export interface TemplateMessages {
  id?: number;
  message: string;
  msgId: string;
  nodeId: number;
  nodeName: string;
  subScore?: number | string;
}
export interface Score {
  checked: number;
  nodeId: number;
  nodeName: string;
  remark: string;
  score: number;
  subScore: number;
  violationShow: string;
  pachiras: any[];
  fastModels: any[];
}

export interface DicTagType {
  childrens: null[];
  depth: number;
  id: number;
  parentId: number;
  tagName: string;
  tenantId: number;
}
export interface ProblemType {
  dicName: string;
  dicNameEng: string;
  dicValue: string;
  sort: number;
  id: number;
  tenantId: number;
}
export interface QualityDetailType {
  bizCagetoryName: string;
  dataType: number;
  deptGroupName: string;
  groupName: string;
  qualityResult: string;
  qualityResultDetails: string;
  remark: string;
  resultId: number;
  rootGroupName: string;
  seatCode: string;
  seatName: string;
  templateType: number;
  uniqueId: string;
}
export interface PolicysList {
  key: number;
  applyNo: string;
  campaignId: number;
  channelId: number;
  channelPolicyNo: string;
  effectiveDate: string;
  expiryDate: string;
  gmtCreated: string;
  insurantList: InsurantList[];
  insurantName: string;
  insureDate: string;
  insuredId: string;
  insuredNo: string;
  isPresent: string;
  issueDate: string;
  oldPolicyStatus: number;
  oldPolicyType: number;
  parentPolicyNo: string;
  payFrequency: number;
  policyEndReason: number;
  policyHolder: InsurantList;
  policyHolderName: string;
  policyId: number;
  policyNo: string;
  policyStatus: number;
  policyType: number;
  premium: string;
  productId: number;
  productPackageCode: string;
  productPackageId: number;
  productPackageName: string;
  sumInsured: string;
  uwOperator: string;
  channelName: string;
}

interface InsurantList {
  birthday: string;
  certificateNo: string;
  certificateType: string;
  email: string;
  gender: string;
  holderRelation: string;
  id: number;
  oldCertificateType: number;
  phoneNo: string;
  userId: number;
  userName: string;
}

export interface WoAssignList {
  key: number;
  agentDeptId: number;
  agentDeptName: string;
  agentId: number;
  agentName: string;
  agentType: number;
  agentTypeDesc: string;
  annexDTOList: AnnexDTOList[];
  appointmentTime: string;
  appointmentTimeEnd: string;
  assignCategory: number;
  assignItem: number;
  assignNo: string;
  assignOwner: string;
  assignTime: string;
  assignType: number;
  assignUrgency: number;
  assistDept: string;
  assistId: string;
  audiTime: string;
  auditLogList: AuditLogList[];
  bizDeptId: number;
  bizDeptName: string;
  branchStatus: number;
  branchStatusStr: string;
  cancelTime: string;
  certNo: string;
  certType: number;
  channel: string;
  channelAccountUserId: number;
  channelId: number;
  closeTime: string;
  comment: string;
  complaintDetail: ComplaintDetail;
  contactPhone: string;
  content: string;
  creator: string;
  customEmail: string;
  customId: number;
  customName: string;
  detailData: string;
  expectCloseTime: string;
  fileUrlList: string[];
  firstTypeId: number;
  firstTypeName: string;
  fiveTypeId: number;
  fiveTypeName: string;
  fourTypeId: number;
  fourTypeName: string;
  gmtCreated: string;
  gmtModified: string;
  handleContent: string;
  handleExtraMap: HandleExtraMap;
  handleTime: string;
  handlerDept: string;
  handlerDeptName: string;
  handlerEmail: string;
  handlerId: string;
  handlerName: string;
  hidePhone: string;
  id: number;
  isDeleted: string;
  isRapidest: number;
  lastHandTime: string;
  lastHandTimeInterval: number;
  lastHandTimeIntervalHour: number;
  mainUniqueId: string;
  modifier: string;
  originHandlerDept: string;
  originHandlerId: string;
  packageDefId: number;
  packageDefName: string;
  phone: string;
  policyDetail: HandleExtraMap;
  policyNo: string;
  productItem: number;
  productItemStr: string;
  productLine: string;
  rapidestDate: string;
  recordId: number;
  regTime: string;
  rejTime: string;
  rejectReason: number;
  rejectReasonStr: string;
  relationDTOList: RelationDTOList[];
  reportNo: string;
  secondLevelLineName: string;
  secondTypeId: number;
  secondTypeName: string;
  serviceSource: number;
  sessionId: string;
  sixTypeId: number;
  sixTypeName: string;
  sourceBizNo: string;
  sourceExtraContent: string;
  status: number;
  statusStr: string;
  subSessionId: string;
  suspendTime: string;
  tempId: number;
  tenantId: number;
  thirdTypeId: number;
  thirdTypeName: string;
  timeInterval: number;
  timeIntervalHour: number;
  timeOut: number;
  tradeNo: string;
  urgeCount: number;
  vehicleFrameNo: string;
  vehicleLicencePlateNo: string;
  woSource: number;
  workPlace: number;
}

interface RelationDTOList {
  agentId: number;
  agentName: string;
  agentType: number;
  agentTypeDesc: string;
  assignId: number;
  assignItem: number;
  assignNo: string;
  assignType: number;
  assignUrgency: number;
  contactPhone: string;
  customName: string;
  firstTypeId: number;
  firstTypeName: string;
  fiveTypeId: number;
  fiveTypeName: string;
  fourTypeId: number;
  fourTypeName: string;
  gmtCreated: string;
  handlerDept: string;
  handlerDeptName: string;
  handlerId: string;
  handlerName: string;
  id: number;
  phone: string;
  policyNo: string;
  productItem: number;
  reAssignId: number;
  secondTypeId: number;
  secondTypeName: string;
  sixTypeId: number;
  sixTypeName: string;
  status: number;
  thirdTypeId: number;
  thirdTypeName: string;
}

interface HandleExtraMap {}

interface ComplaintDetail {
  assignId: number;
  compCategory: number;
  compCategoryExt1: number;
  compCategoryExt2: number;
  compCategoryExt3: number;
  compNature: number;
  compType: number;
  compensation: number;
  id: number;
  satisfactionResult: number;
}

interface AuditLogList {
  content: string;
  mainId: number;
  operDeptId: number;
  operDeptName: string;
  operId: number;
  operName: string;
  operTime: string;
  operTimeStr: string;
  operType: string;
  operTypeNum: number;
}

interface AnnexDTOList {
  assignId: number;
  bizCategory: string;
  creator: string;
  gmtCreated: string;
  id: number;
  linkAddress: string;
  modifier: string;
  name: string;
  ossKey: string;
  recordId: number;
  remark: string;
  size: number;
  source: number;
  type: string;
  uniqueId: string;
}
export interface QualityTaskEnterType {
  begainStartTime: string;
  begainTaskDate: string;
  bizCagetorys: string[];
  claimStatus: number;
  custormPhone: string;
  dataTypes: number[];
  deptGroupIds: string[];
  endStartTime: string;
  endTaskDate: string;
  groupIds: string[];
  hander: string;
  pageNum: number;
  pageSize: number;
  poolDefineIds: number[];
  rootGroupIds: string[];
  satisfactions: number[];
  servantName: string;
  statusItems: number[];
  statusList: number[];
  taskCode: string;
  taskLeaderName: string;
  tenantId: number;
  userIds: number[];
  taskType: number;
}

export interface QualityTaskListType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: TaskChildrenListType[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
  taskDate: string;
  dataType: [];
  poolDefineName: string;
  satisfaction?: number;
  taskCode: string;
}

export interface TaskChildrenListType {
  key: number;
  bizCagetory: string;
  bizCagetoryName: string;
  blockReason: number;
  custormGender: number;
  custormId: string;
  custormName: string;
  custormNick: string;
  custormPhone: string;
  deptGroupId: string;
  deptGroupName: string;
  extraData: string;
  groupId: string;
  groupName: string;
  handleTime: string;
  handler: string;
  id: number;
  policyNo: string;
  qualityTaskLeader: number;
  role: number;
  rootGroupId: string;
  rootGroupName: string;
  seatCode: string;
  servantName: string;
  source: string;
  status: number;
  statusItem: number;
  taskCode: string;
  taskDate: string;
  taskLeaderName: string;
  tenantId: number;
}
export interface ConversationType {
  key?: number;
  agentName: string;
  answerTime: string;
  callType: string;
  calleeNumber: string;
  cno: string;
  deptGroupName: string;
  endReason: string;
  endTime: string;
  gmtCreated: string;
  id: number;
  joinQueueTime: string;
  mainCallType: string;
  mainUniqueId: string;
  qno: string;
  queueName: string;
  recordFile: string;
  rootGroupName: string;
  sipCause: string;
  startTime: string;
  status: string;
  totalDuration: number;
  tsiFile: string;
  uniqueId: string;
  workplace: string;
}

export interface TaskAssignmentEnter {
  qualityTaskLeader: number;
  taskIds: number[];
  taskLeaderName: string;
  tenantId: number;
}

export interface TaskAssignment {
  failNum: number;
  successNum: number;
}

export interface QualityTaskCollect {
  appealNum: number;
  appealRedayNum: number;
  qualityNum: number;
  reQualityNum: number;
  visitNum: number;
}

export interface SessionCollect {
  certNo: string;
  id: number;
  mainUniqueId: string;
  phone: string;
}
export interface PreCheckTaskEnter {
  approveType: number;
  reason: string;
  rejectedType: number;
  taskCode: string;
  taskId: number;
  handler: string;
  rootGroupName: string;
}
export interface MessageType {
  key: number;
  canReSend: boolean;
  content: string;
  isDecode: boolean;
  isRetry: boolean;
  msgSentId: string;
  receiptStatus: string;
  receiverNo: string;
  sendTime: string;
  source: string;
  submitStatus: string;
  templateNo: string;
}
export interface ExtraPhoneType {
  extra: string;
  extraType: string;
  gmtCreated: string;
  id: number;
  isDeleted: string;
  taskId: number;
  tenantId: number;
}
export interface ComplaintHistoryType {
  key: number;
  complainant: string;
  complainantCertNo: string;
  complainantPhone: string;
  creator: string;
  endDate: string;
  gmtCreated: string;
  gmtModified: string;
  historyWoId: number;
  id: number;
  lastResults: string;
  modifier: string;
  ocsCategory: number;
  ocsCategoryStr: string;
  ocsCreator: string;
  ocsNo: string;
  policyNo: string;
  reasonAppeal: string;
  startDate: string;
  status: number;
  statusStr: string;
  woId: number;
  woNo: string;
}

export interface ComplaintPolicyType {
  key: number;
  bizTeam: string;
  campaignDefId: number;
  campaignDefName: string;
  codeBizDept: string;
  durationEnd: string;
  durationStart: string;
  id: number;
  installmentNo: string;
  packageDefId: number;
  packageDefName: string;
  policyHolder: string;
  policyHolderName: string;
  policyInsured: string;
  policyInsuredDate: string;
  policyInsuredName: string;
  policyNo: string;
  policyStatus: number;
  policyStatusName: string;
  productCategory: string;
  productId: number;
  productName: string;
  productType: string;
  renewal: string;
  renewalCount: number;
  saleChannelId: number;
  saleChannelName: string;
  woId: number;
  woNo: string;
}

export interface ComplaintLogType {
  key: number;
  category: number;
  content: string;
  custReplyId: number;
  id: number;
  name: string;
  operId: number;
  operName: string;
  operOrgId: number;
  operOrgName: string;
  operTime: string;
  replyId: number;
  type: number;
  woId: number;
  woNo: string;
}
export interface AppeaseType {
  key: number;
  appeaseAccount: string;
  appeaseAccountName: string;
  appeaseMode: number;
  appeaseModeStr: string;
  appeaseMoney: string;
  appeaseRemitteeName: string;
  appeaseType: number;
  appeaseTypeStr: string;
  bankBranchCode: string;
  bankCode: string;
  city: string;
  creator: string;
  creatorId: string;
  deleted: string;
  expReportNumber: string;
  gmtCreated: string;
  id: number;
  isExamine: string;
  jwt: string;
  lineNumber: number;
  newAdd: string;
  payDate: string;
  payMsg: string;
  payeeRelation: number;
  payeeRelationStr: string;
  province: string;
  remarks: string;
  returnVisit: number;
  status: number;
  statusStr: string;
  updated: string;
  woId: number;
  woIds: number[];
  woNo: string;
  workPlace: number;
  workPlaceName: string;
  workPlaceStr: string;
}

export interface PolicyExtraType {
  channelName: string;
  policyNo: string;
  productId: string;
  productName: string;
  taskId: number;
}
export interface SeatListSinType {
  deptGroupName: string;
  groupName: string;
  qualityResult: string;
  qualityResultDetails: string;
  readOnly: string;
  remark: string;
  resultId: number;
  rootGroupName: string;
  seatCode: string;
  seatName: string;
  taskCommitCode: string;
}

export interface SeatListType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: SeatListSinType[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}
