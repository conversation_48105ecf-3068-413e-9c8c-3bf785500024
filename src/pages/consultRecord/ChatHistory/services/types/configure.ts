export interface TagUpdataEnterType {
  creator: string;
  depth: number;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  isDeleted: string;
  modifier: string;
  parentId: number;
  tagName: string;
  tenantId: number;
}

export interface DicAddEnterType {
  creator: string;
  dicCategory: string;
  dicCategoryVal: string;
  dicName: string;
  dicValue: string;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  isDeleted: string;
  isEnable: number;
  modifier: string;
  parentId: number;
  sort: number;
  tenantId: number;
}

export interface TemplateListEnterType {
  bizLines: string[];
  modifier: string;
  pageNum: number;
  pageSize: number;
  taskStatuss: number[];
  templateMolds: number[];
  templateName: string;
  templateTeams: string[];
  tenantId: number;
}

export interface TemplateListType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: TemplateList[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface TemplateList {
  bizName: string;
  channelName: string;
  gmtModified: string;
  id: number;
  isDefault: number;
  modifier: string;
  taskStatusName: string;
  templateCallTypeName: string;
  templateId: number;
  templateMoldName: string;
  templateName: string;
  templateTeamName: string;
  templatePoolName: string;
  templateWeight: number;
}

export interface TemplateDetailType {
  addTemplate: AddTemplate;
  effectTime: number;
  bizLines: string[];
  bizTemplateId: number;
  channels: number[];
  creator: string;
  fullMark: number;
  modifier: string;
  taskStatuss: number[];
  templateCallTypes: number[];
  templateId: number;
  templateMolds: number[];
  templateName: string;
  templatePoolIds: string[];
  templateTeams: string[];
  templateType: number;
  templateWeight: number;
  tenantId: number;
  isDefault: number;
  queues: string[];
}

export interface AddTemplate {
  children: null[];
  conditionRelationExpression: string;
  conditionRelationType: string;
  creator: string;
  descri: string;
  id: number;
  isScoreUpper: string;
  modifier: string;
  nodeName: string;
  parentId: number;
  score: number;
  scoreMode: string;
  scoreType: number;
  step: number;
  violationShow: string;
}

export interface TemplateBatchDeleteType {
  ids: number[];
  modifier: string;
  tenantId: number;
}

export interface AllocationListType {
  creator: string;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  modifier: string;
  qualityAllocation: string;
  status: number;
  tenantId: number;
  poolDefineId?: number;
}

export interface CustomFieldListType {
  pageNum: number;
  pageSize: number;
}

export interface CustomFieldListAdd {
  id?: number;
  fieldName: string;
  fieldKey: string;
  fieldType: number;
  fieldData?: string;
  fieldDataName?: string;
  status?: number;
  required: string;
  fieldValueList?: Array<{
    fieldValueKey: string;
    fieldValueName: string;
  }>;
}

export interface CustomFieldListAction {
  id: number;
  changeType: number;
}
