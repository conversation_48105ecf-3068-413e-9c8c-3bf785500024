export interface ComparseEnterType {
  endQualityDate: string;
  pachiraIds: string[];
  pageNum: number;
  pageSize: number;
  startQualityDate: string;
  tenantId: number;
  type: number;
}
export interface ComparseCustormPhone {
  taskId?: number | string;
}

export interface ComparseMsgSeat {
  taskId?: number;
  seatCode: string;
  servantName: string;
}

export interface ComparseListType {
  accuracy: number;
  commonHitNum: number;
  id: number;
  pachiraHitNum: number;
  pachiraId: string;
  pachiraName: string;
  precision: number;
  qualityDate: string;
  qualityHitNum: number;
  recall: number;
  taskNum: number;
  tenantId: number;
}

export interface ComparseConfiginfo {
  dataEchoStatus: number;
  checked: number;
  bizTemplateId: number;
  nodeId: number;
  nodeName: string;
  pachiraId: string;
  pachiraName: string;
  score: number;
  scoreType: number;
  step: number;
  subScore: number;
  templateId: string;
  templateName: string;
  tenantId: number;
}

export interface ComparseOnlineOff {
  gmtModified: string;
  modifier: string;
  pachiraId: string;
  templateId: string;
  tenantId: number;
}

export interface ComparseOnlineUP {
  creator: string;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  isDeleted: string;
  modifier: string;
  nodeId: number;
  nodeName: string;
  pachiraId: string;
  pachiraName: string;
  remark: string;
  status: number;
  subScore: number;
  templateId: string;
  tenantId: number;
  dataEchoStatus: number;
}

export interface ComparseDetailEnterType {
  endQualityDate: string;
  pachiraId: string;
  pageNum: number;
  pageSize: number;
  startQualityDate: string;
  tenantId: number;
  type: number;
}

export interface ComparseDetailType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: ComparseDetailListType[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface ComparseDetailListType {
  id: number;
  pachireId: string;
  pachireName: string;
  qualityDate: string;
  qualityResult: string;
  remark: string;
  subScoreInfo: string;
  taskCode: string;
  taskId: number;
  tenantId: number;
  type: number;
  uniqueId: string;
}
