export interface listTaskEnterType {
  bizCategorys: string[];
  checkEndTime: string;
  checkStartTime: string;
  dataEndTime: string;
  dataStartTime: string;
  dataType: number;
  pageNum: number;
  pageSize: number;
  seatName: string;
  seatUniqueId: string;
  status: number[];
}

export interface listTaskType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: ListTask[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface ListTask {
  appealStep: number;
  bizCategory: string;
  bizCategoryName: string;
  blendKey: string;
  checkPerson: string;
  checkPersonName: string;
  checkTime: string;
  comments: string;
  dataEndTime: string;
  dataStartTime: string;
  dataType: number;
  dataTypeName: string;
  detail: string;
  handleTime: string;
  handler: string;
  handlerName: string;
  historys: History[];
  id: number;
  mainUniqueId: string;
  reason: string;
  remark: string;
  result: string;
  seatName: string;
  seatUniqueId: string;
  status: number;
  statusDesc: number;
  taskCode: string;
  teamId: number;
  tenantId: number;
  totalDuration: number;
  uniqueId: string;
}

interface History {
  handler: string;
  handlerEndTime: string;
  handlerId: number;
  handlerStartTime: string;
  remark: string;
  status: number;
  taskId: number;
  tenantId: number;
}
export interface TaskAppealDetailType {
  appealStep: number;
  bizCategory: string;
  bizCategoryName: string;
  blendKey: string;
  checkTime: string;
  comments: string;
  dataEndTime: string;
  dataStartTime: string;
  dataType: number;
  dataTypeName: string;
  handleTime: string;
  handler: string;
  handlerName: string;
  historys: History[];
  id: number;
  reason: string;
  remark: string;
  result: string;
  seatName: string;
  seatUniqueId: string;
  status: number;
  statusDesc: number;
  taskCode: string;
  teamId: number;
  checkPerson: string;
  checkPersonName: string;
  detail: string;
  mainUniqueId: string;
  uniqueId: string;
  totalDuration: number;
  tenantId: number;
}

export interface DetailHistory {
  handler: string;
  handlerEndTime: string;
  handlerId: number;
  handlerStartTime: string;
  status: number;
  taskId: number;
  tenantId: number;
  remark: string;
}

export interface TaskAppealEnterType {
  appealType: number;
  approveType: number;
  id: number;
  reason: string;
  remark: string;
  seatUniqueId: string;
}

export interface TaskApproveEnterType {
  approveType: number;
  id: number;
  remark: string;
  seatUniqueId: string;
}

export interface UserInfoType {
  depId: number;
  depName: string;
  email: string;
  id: number;
  jobNo: string;
  leader: boolean;
  name: string;
  no: string;
  phone: string;
  tenantId: number;
  tenantSign: string;
  userName: string;
}

export interface AppealChatHistoryType {
  id: number;
  beginTime: string;
  beginTimeOffset: number;
  custormName: string;
  endTimeOffset: number;
  isContainRealSens: string;
  isContainSens: string;
  messageType: number;
  msgId: string;
  sensitiveStr: string[];
  sensitives: Sensitive[];
  servantName: string;
  side: number;
  silenceDuration: number;
  source: string;
  text: string;
}

interface Sensitive {
  annoyType: string;
  effectiveObject: number;
  qaId: string;
  remindContent: string;
  subType: string;
  violateType: string;
  warnContent: string[];
  warnTime: string;
  warnTip: string;
  warnType: number;
}

export interface AppealTemplateEnterType {
  tenantId: number;
  taskCode: string;
  uniqueId: string;
  status: number;
}
