export interface AddPersonEnterType {
  bizLine: string;
  bizLines: string[];
  deptGroupId: string;
  deptGroupName: string;
  groupId: string;
  groupName: string;
  id: number;
  isDeleted: string;
  phoneMasked: string;
  qualityRole: number;
  rootGroupId: string;
  rootGroupName: string;
  taskType: string;
  teamId: string;
  teamIds: string[];
  tenantId: number;
  userAccount: string;
  userId: number;
  userName: string;
  workload: number;
}

export interface QueryPersonEnterType {
  bizLines: string[];
  id: number;
  pageNum: number;
  pageSize: number;
  taskType: string;
  teamCodes: string[];
  tenantId: number;
  userAccount: string;
  userId: number;
  userName: string;
  modifier: string;
}

export interface UpdatePersonEnterType {
  bizLine: string;
  bizLines: string[];
  deptGroupId: string;
  deptGroupName: string;
  groupId: string;
  groupName: string;
  id: number;
  isDeleted: string;
  phoneMasked: string;
  qualityRole: number;
  rootGroupId: string;
  rootGroupName: string;
  taskType: string;
  teamId: string;
  teamIds: string[];
  tenantId: number;
  userAccount: string;
  userId: number;
  userName: string;
  workload: number;
}

export interface QueryPersonType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: QueryPersonTypeList[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface QueryPersonTypeList {
  key: number;
  bizLineNames: string;
  bizLines: string[];
  deptGroupId: string;
  deptGroupName: string;
  groupId: string;
  groupName: string;
  id: number;
  isDeleted: string;
  phoneMasked: string;
  qualityRole: number;
  rootGroupId: string;
  rootGroupName: string;
  taskType: string;
  teamCodeNames: string;
  teamCodes: string[];
  teamIds: string[];
  tenantId: number;
  userAccount: string;
  userId: number;
  userName: string;
  workload: number;
  modifier: string;
  gmtModified: string;
  userRoleName: string;
}

export interface QualityPersonType {
  birthday: string;
  certiNo: string;
  certiType: string;
  companyName: string;
  depId: number;
  depName: string;
  education: number;
  email: string;
  entryDate: string;
  gender: number;
  householdAddress: string;
  id: number;
  jobNo: string;
  leaveDate: string;
  leaveReason: number;
  loginName: string;
  name: string;
  onDuty: number;
  password: string;
  phone: string;
  pwdTime: string;
  roleIdList: number[];
  roleIds: string;
  staffId: number;
  tenantDTO: TenantDTO;
  tenantId: number;
  tenantSign: string;
  title: string;
  type: number;
  userName: string;
  wangwang: string;
  wechat: string;
  workDate: string;
}

interface TenantDTO {
  enable: number;
  gmtCreated: string;
  id: number;
  tenantCode: string;
  tenantEmail: string;
  tenantName: string;
  tenantTel: string;
}

export interface SeatInfoEnterType {
  modifier: string;
  pageNum: number;
  pageSize: number;
  seatCode: string;
  seatName: string;
  teamCodes: string[];
  tenantId: number;
  lastDayQuery: number;
}
export interface SeatInfoDataType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: SeatInfoListType[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface SeatInfoListType {
  creator: string;
  deptGroupId: string;
  deptGroupName: string;
  entryDays: number;
  gmtCreated: string;
  gmtModified: string;
  groupId: string;
  groupName: string;
  id: number;
  isDeleted: string;
  modifier: string;
  provider: number;
  rootGroupId: string;
  rootGroupName: string;
  seatCode: string;
  seatName: string;
  tenantId: number;
  tenantUserCode: string;
  userId: number;
}

export interface SeatAddEnterType {
  creator: string;
  deptGroupId: string;
  deptGroupName: string;
  entryDays: number;
  gmtCreated: string;
  gmtModified: string;
  groupId: string;
  groupName: string;
  id: number;
  isDeleted: string;
  modifier: string;
  provider: number;
  rootGroupId: string;
  rootGroupName: string;
  seatCode: string;
  seatName: string;
  tenantId: number;
  tenantUserCode: string;
  userId: number;
}

export interface BatchDeleteType {
  ids: number[];
  modifier: string;
  tenantId: number;
}

export interface PersonDeleteType {
  ids: number[];
}
