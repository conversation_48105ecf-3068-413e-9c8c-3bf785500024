export interface Payload<T = any> {
  message: string;
  status: number;
  data: T;
}

export interface Pageable<T = any> {
  endRow: number;
  hasNextPage: true;
  hasPreviousPage: true;
  isFirstPage: true;
  isLastPage: true;
  list: T[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface KeyValue {
  [key: string]: any;
}

export interface IGroupDics {
  bizLineTags: BizLineTag[];
  teamTags: BizLineTag[];
}

interface BizLineTag {
  childrenTags: null[];
  dicName: string;
  dicValue: string;
}

export interface TeamDics {
  chidren: TeamDics[];
  dicName: string;
  dicValue: string;
  tenantId: number;
}

export interface TeamUsers {
  bizLine: string;
  bizLines: string[];
  groupId: string;
  id: number;
  isDeleted: string;
  phoneMasked: string;
  taskType: string;
  teamId: string;
  teamIds: string[];
  tenantId: number;
  userAccount: string;
  userId: number;
  userName: string;
  workload: number;
}

export interface WorkBench {
  assigned: Assigned[];
  completed: Assigned[];
  pending: Assigned[];
  temporaryStorage: Assigned[];
  total: Assigned[];
}

export interface Assigned {
  bizType: number;
  desc: string;
  num: number;
}
