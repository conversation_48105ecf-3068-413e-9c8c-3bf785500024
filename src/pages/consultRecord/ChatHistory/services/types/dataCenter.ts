export interface FetchRuleFunnel {
  bizCagetorys: Array<any>;
  groupIds: Array<any>;
  startTime: string;
  endTime: string;
  poolDefineIds: Array<any>;
}
export interface TaskListParam {
  checkTime: any[];
  begainCheckTime: string;
  bizCagetorys: string[];
  deptGroupIds: string[];
  endCheckTime: string;
  groupIds: string[];
  pageNum: number;
  pageSize: number;
  qualityPerson: string;
  rootGroupIds: string[];
  servantName: string;
  status: number;
  tenantId: number;
  poolDefineIds: number[];
  taskCode: string;
  downloadType: number;
}
export interface TaskListType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: List[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface List {
  key?: number;
  beFollowType: number;
  bizCagetory: string;
  bizCagetoryName: string;
  checkTime: string;
  deptGroupName: string;
  extraData: string;
  followReason: string;
  groupName: string;
  id: number;
  obtainTime: string;
  policyNo: string;
  poolDefineName: string;
  preInspection: number;
  problemType: string;
  qualityPerson: string;
  qualityResult: string;
  qualityResultDetails: string;
  reQualityFlag: number;
  remark: string;
  rootGroupName: string;
  seatCode: string;
  servantName: string;
  status: number;
  summary: string;
  taskCode: string;
  taskId: number;
  tempStorage: number;
  tenantId: number;
}

export interface TaskCollectType {
  appealNum: number;
  appealRedayNum: number;
  qualityNum: number;
  reQualityNum: number;
  visitNum: number;
  specialNum: number;
}

export interface TotalTaskReportParam {
  deptGroupIdList: string[];
  endTime: string;
  groupIdList: string[];
  handlerId: number;
  handlerIdList: number[];
  pageNum: number;
  pageSize: number;
  queueList: string[];
  rootGroupIdList: string[];
  startTime: string;
  statusList: number[];
}
export interface TotalTaskReportType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: ReportList[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface ReportList {
  key: number;
  assignmentTaskNum: number;
  bizCagetoryName: string;
  deptGroupName: string;
  groupName: string;
  handlerTime: string;
  handler: string;
  handlerId: number;
  handlerTotalTaskNum: number;
  rootGroupName: string;
  status: number;
  statusDesc: string;
  submittedTaskNum: number;
  temporaryStorageTaskNum: number;
  totalTaskNum: number;
  poolDefineId: number;
  poolDefineName: string;
}
export interface TotalTaskDetailParam {
  handlerId: number;
  handlerTime: string;
  pageNum: number;
  pageSize: number;
  status: number;
  statusItem: number;
}

export interface TotalTaskDetailType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: DetailList[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}
export interface DetailList {
  key: number;
  taskId: number;
  assignmentTaskNum: number;
  bizCagetoryName: string;
  deptGroupName: string;
  groupName: string;
  handler: string;
  handlerId: number;
  handlerTime: string;
  handlerTotalTaskNum: number;
  rootGroupName: string;
  status: number;
  statusDesc: string;
  submittedTaskNum: number;
  temporaryStorageTaskNum: number;
  totalTaskNum: number;
}

export interface WhaleQueryEnterType {
  callWhaleQueryEnterTypeType: number;
  custormPhone: string;
  dataType: number;
  endTime: string;
  pageNum: number;
  pageSize: number;
  servantName: string;
  startTime: string;
  uniqueIds: string[];
  bizCagetorys: string[];
  satisfactions: number[];
}

export interface WhaleQueryType {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: WhaleQueryListType[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

export interface WhaleQueryListType {
  key: number;
  avgResponseDuration: number;
  bizCagetory: string;
  bizCagetoryName: string;
  blendKey: string;
  callType: number;
  custormId: string;
  custormName: string;
  custormPhone: string;
  dataType: number;
  deptGroupId: string;
  deptGroupName: string;
  endTime: string;
  endType: string;
  firstResponseDuration: number;
  groupId: string;
  groupName: string;
  id: number;
  mainUniqueId: string;
  ossLink: string;
  outBizNo: string;
  policyNo: string;
  provider: number;
  rootGroupId: string;
  rootGroupName: string;
  satisfaction: number;
  seatCode: string;
  servantName: string;
  startTime: string;
  tenantId: number;
  totalDuration: number;
  uniqueId: string;
}

export interface WhaleUniqueQueryType {
  avgResponseDuration: number;
  bizCagetory: string;
  bizCagetoryName: string;
  blendKey: string;
  callType: number;
  custormId: string;
  custormName: string;
  custormPhone: string;
  dataType: number;
  deptGroupId: string;
  deptGroupName: string;
  endTime: string;
  endType: string;
  firstResponseDuration: number;
  groupId: string;
  groupName: string;
  id: number;
  mainUniqueId: string;
  ossLink: string;
  outBizNo: string;
  policyNo: string;
  provider: number;
  rootGroupId: string;
  rootGroupName: string;
  satisfaction: number;
  seatCode: string;
  servantName: string;
  startTime: string;
  tenantId: number;
  totalDuration: number;
  uniqueId: string;
}

export interface TextOutType {
  beginTime: string;
  beginTimeOffset: number;
  custormName: string;
  endTimeOffset: number;
  isContainRealSens: string;
  isContainSens: string;
  messageType: number;
  msgId: string;
  sensitiveStr: string[];
  sensitives: Sensitive[];
  servantName: string;
  side: number;
  silenceDuration: number;
  source: string;
  text: string;
  id: number;
}

export interface Sensitive {
  accountId: string;
  annoyType: string;
  asrBlendKey: string;
  bizCagetory: string;
  bizCagetoryName: string;
  custormId: string;
  custormName: string;
  custormNick: string;
  custormPhone: string;
  dataTenant: number;
  dataType: number;
  effectiveObject: number;
  endTime: string;
  groupId: string;
  groupName: string;
  id: number;
  mainUniqueId: string;
  provider: number;
  qaId: string;
  realSensitiveResultId: number;
  remindContent: string;
  satisfaction: number;
  sensitiveResultId: number;
  servantId: string;
  servantName: string;
  startTime: string;
  status: number;
  subType: string;
  tenantId: number;
  uniqueId: string;
  violateType: string;
  warnContent: string[];
  warnTime: string;
  warnTip: string;
  warnType: number;
}
export interface ResultOutType {
  data: ResultOutListType[];
  violation: string;
  violationId: number;
}

interface ResultOutListType {
  keywords: string;
  msg: string;
  msgId: string;
  msgTime: string;
  name: string;
  side: number;
  subType: string;
}

export interface ResultHistoryType {
  key: number;
  checkTime: number;
  id: number;
  qualityPerson: string;
  qualityResult: string;
  qualityType: number;
  realSensitiveCount: number;
  taskCode: string;
}

export interface PersonListEnterType {
  bizLines: string[];
  id: number;
  pageNum: number;
  pageSize: number;
  taskType: string;
  teamCodes: string[];
  tenantId: number;
  userAccount: string;
  userId: number;
  userIds: number[];
  userName: string;
  qualityRole: number;
}

export interface PersonListType {
  bizLineNames: string;
  bizLines: string[];
  deptGroupId: string;
  deptGroupName: string;
  groupId: string;
  groupName: string;
  id: number;
  isDeleted: string;
  phoneMasked: string;
  qualityRole: number;
  rootGroupId: string;
  rootGroupName: string;
  taskType: string;
  teamCodeNames: string;
  teamCodes: string[];
  teamIds: string[];
  tenantId: number;
  userAccount: string;
  userId: number;
  userName: string;
  workload: number;
}

export interface TaskCreateEnterType {
  qualityLeaderName: string;
  qualityTaskLeader: number;
  tenantId: number;
  uniqueId: string;
  userId: number;
  username: string;
}
