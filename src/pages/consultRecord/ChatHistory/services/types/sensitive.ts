export interface ISensitiveWordParam {
  bizCategory?: string;
  dataType: number;
  groupId?: string;
  pageNum: number;
  pageSize: number;
  status?: number;
  tenantId?: number;
  type: number;
}

export interface ISensitiveWord {
  bizCagetory: string;
  bizCagetoryName: string;
  bizLine: number;
  custormId: string;
  custormName: string;
  custormNick: string;
  custormPhone: string;
  dataType: number;
  endTime: string;
  groupId: string;
  groupName: string;
  id: number;
  mainUniqueId: string;
  realSensitiveMsg: string;
  realSensitiveName: string;
  realSensitiveResultCount: number;
  realSensitiveResultId: number;
  sensitiveResultId: number;
  servantId: string;
  servantName: string;
  startTime: string;
  status: number;
  totalDuration: number;
  uniqueId: string;
}

export interface ISensitiveWordCount {
  sensitiveCustormRealCount: number;
  sensitiveCustormTodayCount: number;
  sensitiveRealCount: number;
  sensitiveServantRealCount: number;
  sensitiveServantTodayCount: number;
  sensitiveTodayCount: number;
}

export interface ISensitiveWordInfo {
  accountId: string;
  bizCagetory: string;
  bizCagetoryName: string;
  bizLine: number;
  custormId: string;
  custormName: string;
  custormNick: string;
  custormPhone: string;
  dataType: number;
  endTime: string;
  groupId: string;
  groupName: string;
  id: number;
  mainUniqueId: string;
  provider: number;
  realSensitiveResultId: number;
  sensitiveCustormCount: number;
  sensitiveResultId: number;
  sensitiveResults: SensitiveResult[];
  sensitiveServantCount: number;
  servantId: string;
  servantName: string;
  startTime: string;
  status: number;
  tenantId: number;
  totalDuration: number;
  uniqueId: string;
}

interface SensitiveResult {
  creator: string;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  importantLevel: number;
  isDeleted: string;
  mainUniqueId: string;
  modifier: string;
  msg: string;
  msgId: string;
  sensitiveId: number;
  sensitives: string;
  side: number;
  tenantId: number;
  uniqueId: string;
}

export interface IChatHistory {
  content: string;
  custormName: string;
  date: number;
  msgId: string;
  sensitives: string;
  servantName: string;
  side: number;
  source: string;
}
