import { request } from 'utils/request';
import { Payload } from './types';
import { CustomFieldListAdd } from './types/configure';
import {
  AppeaseType,
  AudioTextType,
  ComplaintHistoryType,
  ComplaintLogType,
  ComplaintPolicyType,
  ConversationType,
  DicTagType,
  ExtraPhoneType,
  MessageType,
  PolicyExtraType,
  PolicysList,
  ProblemType,
  QualityDetailType,
  QualityResultType,
  QualityTaskCollect,
  QualityTaskEnter,
  QualityTaskEnterType,
  QualityTaskListType,
  QualityTaskType,
  SeatListType,
  SessionCollect,
  TaskAssignment,
  TaskAssignmentEnter,
  TaskDetailType,
  TaskType,
  TemplateEnterType,
  TemplateType,
  TemplateTypeEnter,
  WoAssignList,
} from './types/manualQuality';

/**
 * 初检-新渠道-获取新增所需信息
 * @param str
 */
export function fetchQualitymessage(str: string) {
  return request.get<Payload<QualityTaskType>>(`/api/v1/lionk/dic/query/diccategory/val?dicCategoryVal=${str}`);
}

/**
 * 初检-新渠道-获取任务
 * @param status
 */
export function fetchQualityTask(params: Partial<QualityTaskEnter>) {
  return request.post<Payload<QualityTaskType>>(`/api/v1/lionk/quality/task/obtain`, params);
}

/**
 * 初检-新渠道-获取任务详情
 * @param uniqueId
 */
export function fetchSessionDetail(uniqueId: string | undefined, id: number) {
  return request.get<Payload<TaskDetailType>>(`/api/v1/lionk/audio/session/info`, {
    params: { uniqueId, id },
  });
}

/**
 * 初检-获取录音违规点
 * @param id
 */
export function fetchAudioresult({ id = '', sessionId = '' }: { id?: any; sessionId?: any }) {
  return request.get<Payload>(`/api/v1/lionk/audio/session/audio/sensitive?id=${id}&sessionId=${sessionId}`);
}

/**
 * 初检-新渠道-获取AI质检结果
 * @param id
 */
export function fetchQualityResult(id: number) {
  return request.get<Payload<QualityResultType[]>>(`/api/v1/lionk/audio/session/sensitive/result/${id}`);
}

/**
 * 初检-新渠道-获取文本
 * @param id
 */
export function fetchChatHistory(id: number) {
  return request.get<Payload<AudioTextType[]>>(`/api/v1/lionk/audio/session/text/${id}`);
}

/**
 * 初检-新渠道-提交任务
 * @param data
 */
export function submitTask(data: Partial<TaskType>) {
  return request.post<Payload>(`/api/v1/lionk/quality/task/complete`, data);
}
/**
 * 初检-新渠道-任务暂存
 * @param data
 */
export function storageTask(data: Partial<TaskType>) {
  return request.post<Payload>(`/api/v1/lionk/quality/task/temp/storage`, data);
}

/**
 * 初检-新渠道-获取质检模版
 * @param data
 */
// export function fetchTemplate(data: TemplateTypeEnter) {
//   return request.get<Payload<TemplateType>>(
//     `/api/v1/lionk/audio/session/template/${data.id}?seatCode=${data.seatCode}&queue=${data.queue}&qualityType=${data.qualityType}&templateMold=${data.templateMold} `,
//   );
// }
export function fetchTemplate(data: Partial<TemplateTypeEnter>) {
  return request.post<Payload<TemplateType>>(`/api/v1/lionk/audio/session/template/query`, data);
}

/**
 * 获取自定义字段信息
 * @param
 * @returns
 */
export function getCustomFieldData(param: Partial<TemplateTypeEnter>) {
  return request.post<Payload<Array<CustomFieldListAdd>>>(`/api/v1/lionk/config/data/customFieldResultList`, param);
}

/**
 *  初检-新渠道-提交模版
 * @param data
 * @returns
 */
export function submitTemplate(data: Partial<TemplateEnterType>) {
  return request.post<Payload<TemplateEnterType>>(`/api/v1/lionk/audio/session/template/result`, data);
}

/**
 * 初检-新渠道-获取总评语
 * @param data
 * @returns
 */
export function fetchComment(taskId?: number, status?: number) {
  return request.get<Payload>(`/api/v1/lionk/audio/session/comment/get?taskId=${taskId}&status=${status}`);
}

/**
 * 初检-新渠道-获取质检标签
 * @returns
 */
export function fetchDicTag() {
  return request.get<Payload<DicTagType[]>>(`/api/v1/lionk/dic/tag/tree`);
}

/**
 * 初检-保险-获取问题类型
 * @returns
 */
export function fetchProblemType(dicCategory: string) {
  return request.get<Payload<ProblemType[]>>(`/api/v1/lionk/dic/query`, {
    params: { dicCategory },
  });
}
/**
 * 获取任务池
 * @returns
 */
export function fetchPoolTree(dicCategory: string) {
  return request.get<Payload<[]>>(`/api/v1/lionk/dic/queryTree?dicCategory=${dicCategory}`);
}

export function fetchPoolName() {
  return request.get<Payload<ProblemType[]>>(`/api/v1/lionk/dic/task/pool/name`);
}

// 根据保单号查询产品名称
export function fetchPolicyExtra(p: string) {
  return request.get<Payload<PolicyExtraType>>(`/api/v1/lionk/task/policy/get/rivers?policyNo=${p}`);
}

/**
 * 初检-新渠道-获取质检结果明细
 * @param taskId
 * @returns
 */

export function fetchQualityDetail(taskId: number, status: number) {
  return request.get<Payload<QualityDetailType[]>>(`/api/v1/lionk/audio/session/result/list`, {
    params: { taskId, status },
  });
}
/**
 * 关联信息-保单列表
 * @param id
 * @returns
 */
export function fetchPolicys(p: { id?: number; extraId?: number }) {
  return request.get<Payload<PolicysList[]>>(
    `/api/v1/lionk/quality/task/policys/${p.id}${p.extraId ? `?extraId=${p.extraId}` : ''}`
  );
}
/**
 * 关联信息-工单列表
 * @param id
 * @returns
 */
export function fetchWoAssign(p: { id?: number; extraId?: number }) {
  return request.get<Payload<WoAssignList[]>>(
    `/api/v1/lionk/quality/task/wo/assign/${p.id}${p.extraId ? `?extraId=${p.extraId}` : ''}`
  );
}
/**
 * 关联信息-转接列表
 * @param id
 * @returns
 */
export function fetchConversation(id?: number) {
  return request.get<Payload<ConversationType[]>>(`/api/v1/lionk/audio/session/audio/detail/${id}`);
}

/**
 * 关联信息-短信列表
 * @param id
 * @returns
 */
export function fetchMessage(p: { id?: number; extraId?: number }) {
  return request.get<Payload<MessageType[]>>(
    `/api/v1/lionk/sms/globalQueryBySid?sid=${p.id}${p.extraId ? `&extraId=${p.extraId}` : ''}`
  );
}

/**
 * 质检任务查询
 * @param data
 * @returns
 */
export function fetchQualityTaskList(data: Partial<QualityTaskEnterType>) {
  return request.post<Payload<QualityTaskListType>>(`/api/v1/lionk/quality/task/page`, data);
}

/**
 * 质检任务查询-待分配
 * @param data
 * @returns
 */
export function fetchAllocatedTaskList(data: Partial<QualityTaskEnterType>) {
  return request.post<Payload<QualityTaskListType>>(`/api/v1/lionk/quality/task/claim/page`, data);
}

/**
 * 质检任务查询-批量分配
 * @param data
 * @returns
 */
export function taskAssignment(data: TaskAssignmentEnter) {
  return request.post<Payload<TaskAssignment>>(`/api/v1/lionk/quality/task/assign`, data);
}

/**
 * 质检任务查询获取卡片上总结果
 * @param params
 * @returns
 */
export function fetchQualityTaskCollect() {
  return request.get<Payload<QualityTaskCollect>>(`/api/v1/lionk/history/data/collect`);
}

export function fetchSessionCollect(id: number) {
  return request.get<Payload<SessionCollect>>(`/api/v1/lionk/audio/session/collects/${id}`);
}
/**
 * 关联信息-手机号
 * @param params
 * @returns
 */
export function fetchExtraPhone(params: { extraType: string; taskId?: number }) {
  return request.get<Payload<ExtraPhoneType[]>>(
    `/api/v1/lionk/task/extra/query?extraType=${params.extraType}&taskId=${params.taskId}`
  );
}
/**
 * 关联信息-历史投诉
 * @param id
 * @returns
 */
export function fetchComplaintHistory(id: number) {
  return request.get<Payload<ComplaintHistoryType[]>>(`/api/v1/lionk/quality/task/ocs/historyNew/${id}`);
}

/**
 * 关联信息-投诉保单
 * @param id
 * @returns
 */
export function fetchComplaintPolicy(id: number) {
  return request.get<Payload<ComplaintPolicyType[]>>(`/api/v1/lionk/quality/task/ocs/policy/${id}`);
}

/**
 * 关联信息-投诉处理日志
 * @param id
 * @returns
 */
export function fetchComplaintLog(id: number) {
  return request.get<Payload<ComplaintLogType[]>>(`/api/v1/lionk/quality/task/ocs/log/${id}`);
}

/**
 * 关联信息-安抚金
 * @param id
 * @returns
 */
export function fetchAppease(id: number) {
  return request.get<Payload<AppeaseType[]>>(`/api/v1/lionk/quality/task/ocs/appease/${id}`);
}

/**
 *  质检信息提交-新增违规
 * @param data
 * @returns
 */
export function submitAddViolation(data: any) {
  return request.post<Payload>(`/api/v1/lionk/audio/session/template/result/add-violation`, data);
}

/**
 * 初检-新渠道-获取质检结果明细
 * @param taskId
 * @returns
 */

export function getSeatList(params: any) {
  return request.get<Payload<SeatListType>>(`/api/v1/lionk/audio/session/result/list/seat-detail`, {
    params,
  });
}

/**
 * 获取任务池
 * @param
 * @returns
 */
export function getTaskPoolList() {
  return request.get(`/api/v1/lionk/dic/query/special-quality/list`);
}

/**
 * 新增任务池
 * @param
 * @returns
 */
export function addTaskPoolList(param: { dicName: string; tenantId: any }) {
  return request.post(`/api/v1/lionk/dic/add/special-quality`, param);
}

/**
 * 修改任务池
 * @param
 * @returns
 */
export function updateTaskPoolList(param: any) {
  return request.post(`/api/v1/lionk/dic/update`, param);
}

/**
 * 获取投诉详情链接跳转
 * @param
 * @returns
 */
export function getOcsDetailPath(param: { woNo: string }) {
  return request.post<Payload>(`/api/v1/lionk/ocs/ocsReport`, param);
}
