import { request } from 'utils/request';
import { Payload } from './types';
import { QueryAddViolation } from './types/addViolation';

/**
 * 获取所有坐席信息
 */
export function getAgent(name: string) {
  return request.get<Payload>(`/api/v1/lionk/seat/msg-cn-all?name=${name}`);
}

/**
 * 获取质检模板信息-新增违规
 */
export function getViolationModule(params: QueryAddViolation) {
  return request.post<Payload>(`/api/v1/lionk/audio/session/template/query/add-violation`, params);
}
