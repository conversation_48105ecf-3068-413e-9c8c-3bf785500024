import { request } from 'utils/request';
import { Payload } from './types';
import {
  FetchRuleFunnel,
  PersonListEnterType,
  PersonListType,
  ResultHistoryType,
  ResultOutType,
  TaskCollectType,
  TaskCreateEnterType,
  TaskListParam,
  TaskListType,
  TextOutType,
  TotalTaskDetailParam,
  TotalTaskDetailType,
  TotalTaskReportParam,
  TotalTaskReportType,
  WhaleQueryEnterType,
  WhaleQueryType,
  WhaleUniqueQueryType,
} from './types/dataCenter';
import { TemplateType } from './types/manualQuality';

/**
 * 数据中心-对话检索-任务管理看板对话下载（邮件发送）
 * @param params
 * @returns
 */
export function fetchSend(params: Partial<FormData>) {
  return request.post<Payload<boolean>>(`/api/v1/lionk/whale/email/send`, params, {
    headers: { 'auto-message': 'nerver' },
  });
}

/**
 * 数据中心-数据看板-任务管理看板（常规任务处理漏斗）
 * @param params
 * @returns
 */
export function fetchRuleFunnel(params: Partial<FetchRuleFunnel>) {
  return request.post<Payload>(`/api/v1/lionk/board/data/query`, params);
}

/**
 * 数据中心-数据看板-任务管理看板（申诉任务处理漏斗）
 * @param params
 * @returns
 */
export function fetchAppealFunnel(params: Partial<FetchRuleFunnel>) {
  return request.post<Payload>(`/api/v1/lionk/board/data/appeal/query`, params);
}

/**
 * 数据中心-数据看板-任务管理看板（专项任务处理分析）
 * @param params
 * @returns
 */
export function fetchSpecialAnalyse(params: Partial<FetchRuleFunnel>) {
  return request.post<Payload>(`/api/v1/lionk/board/data/special/query`, params);
}
/**
 * 数据中心-人工质检结果（列表）
 * @param params
 * @returns
 */
export function fetchTaskList(params: TaskListParam) {
  return request.post<Payload<TaskListType>>(`/api/v1/lionk/quality/task/list`, params);
}

/**
 * 下载明细
 * @param params
 * @returns
 */
export function fetchDownLoad(params: Partial<TaskListParam>) {
  return request.post(`/api/v1/lionk/quality/task/result/excel`, params, {
    responseType: 'arraybuffer',
    headers: { 'auto-message': 'nerver' },
  });
}

/**
 * 数据中心-人工质检结果获取卡片上总结果
 * @param params
 * @returns
 */
export function fetchTaskCollect(params: Partial<TaskListParam>) {
  return request.post<Payload<TaskCollectType>>(`/api/v1/lionk/quality/task/result/data/collect`, params);
}
/**
 * 数据中心-质检作业报表
 * @param params
 * @returns
 */
export function fetchTotalTaskReport(params: Partial<TotalTaskReportParam>) {
  return request.post<Payload<TotalTaskReportType>>(`/api/v1/lionk/statistics/listJobReport`, params);
}

export function fetchTotalTaskDetail(params: Partial<TotalTaskDetailParam>) {
  return request.post<Payload<TotalTaskDetailType>>(`/api/v1/lionk/statistics/listJobReportDetail`, params);
}

/**
 * 对话检索-列表查询
 * @param params
 * @returns
 */
export function fetchWhaleQuery(params: Partial<WhaleQueryEnterType>) {
  return request.post<Payload<WhaleQueryType>>(`/api/v1/lionk/whale/query`, params);
}

/**
 * 对话检索-详情
 * @param uniqueId
 * @returns
 */
export function fetchWhaleUniqueQuery(uniqueId: string) {
  return request.get<Payload<WhaleUniqueQueryType>>(`/api/v1/lionk/whale/uniqueQuery`, {
    params: { uniqueId },
  });
}

/**
 * 对话检索-获取外部的文本
 * @param params
 * @returns
 */
export function fetchTextOut(params: { uniqueId: string }) {
  return request.post<Payload<TextOutType[]>>(`/api/v1/lionk/audio/session/text/out`, params);
}

/**
 * 对话检索-获取外部实时质检结果
 * @param params
 * @returns
 */
export function fetchResultOut(params: { uniqueId: string }) {
  return request.post<Payload<ResultOutType[]>>(`/api/v1/lionk/audio/session/sensitive/result/out`, params);
}
/**
 * 对话检索详情-获取质检历史
 * @param params
 * @returns
 */
export function fetchResultHistory(params: { uniqueId: string }) {
  return request.post<Payload<ResultHistoryType[]>>(`/api/v1/lionk/audio/session/quality/result/history`, params);
}

/**
 * 对话检索详情-获取历史模版
 * @param params
 * @returns
 */
export function fetchResultTemplate(params: { id: number; uniqueId?: string }) {
  return request.post<Payload<TemplateType>>(`/api/v1/lionk/template/result/get`, params);
}

/**
 * 对话检索详情-获取质检员
 * @param params
 * @returns
 */
export function fetchPersonList(params: Partial<PersonListEnterType>) {
  return request.post<Payload<PersonListType[]>>(`/api/v1/lionk/person/query/list`, params);
}

/**
 * 对话检索详情-生成任务
 * @param params
 * @returns
 */

export function fetchTaskCreate(params: Partial<TaskCreateEnterType>) {
  return request.post<Payload>(`/api/v1/lionk/quality/task/create`, params);
}
