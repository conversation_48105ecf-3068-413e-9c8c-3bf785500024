/*
 * @Author: your name
 * @Date: 2022-01-06 17:56:22
 * @LastEditTime: 2022-04-08 16:21:32
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /za-ark-puma-static/src/services/api.ts
 */
import { request } from 'utils/request';
import { IGroupDics, Payload, TeamDics, TeamUsers, WorkBench } from './types';

/**
 * 查询当前用户信息
 */
export function fetchUser() {
  return request.get<Payload>('/api/v1/quality/real/userinfo');
}

/**
 * 查询客服组/团队字典
 */
export function fetchGroupDics() {
  return request.get<Payload<IGroupDics>>('/api/v1/quality/real/dic/bizline/team');
}
/**
 * 查询坐席团队
 * @returns
 */
export function fetchTeamTree(tenantId?: string, userId?: string) {
  return request.get<Payload<TeamDics[]>>(
    tenantId && userId
      ? `/api/v1/lionk/dic/team/tree?tenantId=${tenantId}&userId=${userId}`
      : '/api/v1/lionk/dic/team/tree'
  );
}

export function fetchSeatTeamTree() {
  return request.get<Payload<TeamDics[]>>('/api/v1/lionk/dic/tenant/person/team');
}

export function fetchBizTree() {
  return request.get<Payload<TeamDics[]>>('/api/v1/lionk/dic/biz/tree');
}

export function fetchBizTreeOut(tenantId: number) {
  return request.get<Payload<TeamDics[]>>('/api/v1/lionk/dic/biz/tree/out', {
    params: { tenantId },
  });
}

/**
 * 质检组长下面的质检员
 * @param userId
 * @param tenantId
 * @returns
 */
export function fetchTeamUser(data: { userId: number; tenantId: number; isDeleted?: string }) {
  return request.get<Payload<TeamUsers[]>>(
    `/api/v1/lionk/dic/team/user/get?userId=${data.userId}&tenantId=${data.tenantId}&isDeleted=${data.isDeleted || ''}`,
    { headers: { 'auto-message': 'nerver' } }
  );
}

export function fetchWorkBench() {
  return request.post<Payload<WorkBench>>(`/api/v1/lionk/statistics/task`);
}

/**
 * 租户配置信息
 * @returns
 */
export function fetchProperties() {
  return request.get<Payload>(`/api/v1/lionk/dic/tenant/properties`);
}

/**
 * 获取离线质检模型树状信息
 */
export function fetchPachiraTree() {
  return request.get<Payload>(`/api/v1/lionk/dic/pachira/models/tree`);
}

//获取评分表选项
export function fetchTemplateList() {
  return request.get<Payload>('/api/v1/lionk/pachira/comparse/templateListByTenant');
}

//批量上线
export function batchUp(params: Number) {
  return request.post<Payload>(`/api/v1/lionk/pachira/comparse/online/batchUp `, params);
}

//批量下线
export function batchOff(params: any) {
  return request.post<Payload>(`/api/v1/lionk/pachira/comparse/online/batchOff`, params);
}
