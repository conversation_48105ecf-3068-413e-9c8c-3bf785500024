import { request } from 'utils/request';
import { Pageable, Payload } from './types';
import {
  IChatHistory,
  ISensitiveWord,
  ISensitiveWordCount,
  ISensitiveWordInfo,
  ISensitiveWordParam,
} from './types/sensitive';

export function fetchSensitiveWordsNum(importantLevel: number = 1) {
  return request.get<Payload<ISensitiveWordCount>>('/api/v1/quality/real/num/query', {
    params: { importantLevel },
  });
}

export function fetchSensitiveWords(data: ISensitiveWordParam) {
  return request.post<Payload<Pageable<ISensitiveWord>>>('/api/v1/quality/real/list', data);
}

export function fetchSensitiveWordInfo(uniqueId: string) {
  return request.get<Payload<ISensitiveWordInfo>>('/api/v1/quality/real/info', {
    params: { uniqueId, imporantLevel: 1 },
  });
}

export function fetchChatHistory(id: number) {
  return request.get<Payload<IChatHistory[]>>(`/api/v1/quality/real/text/${id}`);
}
