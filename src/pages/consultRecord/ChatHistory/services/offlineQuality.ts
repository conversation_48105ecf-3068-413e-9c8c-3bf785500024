import { request } from 'utils/request';
import { Payload } from './types';
import {
  ComparseConfiginfo,
  ComparseCustormPhone,
  ComparseDetailEnterType,
  ComparseDetailType,
  ComparseEnterType,
  ComparseListType,
  ComparseMsgSeat,
  ComparseOnlineOff,
  ComparseOnlineUP,
} from './types/offlineQuality';

/**
 * 离线质检管理-离线&人工质检结果对比
 * @param status
 */
export function fetchComparse(params: Partial<ComparseEnterType>) {
  return request.post<Payload<ComparseListType[]>>(`/api/v1/lionk/pachira/comparse/list`, params);
}

/**
 *  get传pachirid 离线质检管理---r人工质检管理---获取普强离线模型配置评分表信息
 *
 */
export function fetchConfigInfo(pachiraId: string) {
  return request.get<Payload<ComparseConfiginfo[]>>(
    `/api/v1/lionk/pachira/comparse/configure/info?pachiraId=${pachiraId}`
  );
}
/**
 * 根据任务id，获取客户电话
 *@param taskId
 */
export function fetchCustormPhone(taskId: number | string) {
  return request.get<Payload<ComparseCustormPhone[]>>(`/api/v1/lionk/audio/session/custorm-phone/${taskId}`);
}

/**
 * 根据任务id,获取所有坐席信息
 *@param taskId
 */
export function fetchSeat(taskId: number) {
  return request.get<Payload<ComparseMsgSeat[]>>(`/api/v1/lionk/seat/msg-cn/${taskId}`);
}

/**
 * 离线质检管理---人工质检管理---普强离线模型下线
 * @param status
 */
export function fetchOnlineOff(params: Partial<ComparseOnlineOff>) {
  return request.post<Payload<ComparseOnlineOff>>(`/api/v1/lionk/pachira/comparse/online/off`, params);
}

/**
 * 离线质检管理---人工质检管理---普强离线模型上线
 * @param status
 */
export function fetchOnlineUp(params: Partial<ComparseOnlineUP>) {
  return request.post(`/api/v1/lionk/pachira/comparse/online/up`, params);
}

/**
 * 离线质检管理-人工质检结果与普强离线质检数据对比数据明细
 * @param status
 */
export function fetchComparseDetail(params: Partial<ComparseDetailEnterType>) {
  return request.post<Payload<ComparseDetailType>>(`/api/v1/lionk/pachira/comparse/details/page`, params);
}
