import usePlayerUrl from '@/hooks/usePlayerUrl';
import { message } from 'antd';
import React, { FC, memo, useEffect, useRef } from 'react';

interface IProps {
  src?: string;
  msgId: string;
  currentMsgId?: string;
  onEnd: () => void;
  onLoaded: (duration: number) => void;
}

const SimpleAudio: FC<IProps> = ({ src, msgId, currentMsgId, onEnd, onLoaded }) => {
  const { playerUrl: formatSrc, setPlayerMode, isPlayerFinish } = usePlayerUrl({ src });
  const audioRef = useRef<any>();

  useEffect(() => {
    const _onEnd = () => {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      onEnd();
    };
    if (audioRef.current) {
      audioRef.current.addEventListener('ended', _onEnd);
    }
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener('ended', _onEnd);
      }
    };
  }, [onEnd]);

  useEffect(() => {
    if (!audioRef.current) {
      return;
    }
    if (!currentMsgId || msgId !== currentMsgId) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      return;
    }
    if (audioRef.current) {
      audioRef.current.play();
    }
  }, [msgId, currentMsgId]);

  return !formatSrc ? null : (
    <audio
      onLoadedMetadata={() => {
        onLoaded(Math.round(audioRef.current.duration));
      }}
      onError={() => {
        setPlayerMode();
        isPlayerFinish && message.error('音频加载失败');
      }}
      ref={audioRef}
      src={formatSrc}
    />
  );
};

export default memo(SimpleAudio);
