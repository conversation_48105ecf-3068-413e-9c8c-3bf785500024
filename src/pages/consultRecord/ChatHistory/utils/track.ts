/*
 * @Author: wb_luyin <EMAIL>
 * @Date: 2023-03-27 18:01:09
 * @Descripttion:
 * @LastEditors: wb_luyin <EMAIL>
 * @LastEditTime: 2023-05-29 16:39:30
 * @FilePath: /za-ark-puma-static/src/utils/track.ts
 * Copyright (c) 2023 by ZA-客户服务与体验中心, All Rights Reserved.
 */
import URLSearchParams from '@ungap/url-search-params';
import store, { StoreState } from 'stores/index';
import { is } from './tool';

interface TrackData {
  tck: string;
  content?: Partial<Content>;
  operation?: string;
  tckId?: string;
}

interface Content {
  taskId: number;
  poolId: number;
  pool_name: string;
  seatId: string;
  seatName: string;
  customerNmae: string;
  customerPhone: string;
  sessionId?: string;
  policyNo?: string;
  woNo?: string;
  ocsNo?: string;
  [key: string]: any;
}

/**
 * @description: 触发Gif加载埋点
 * @param {*} url
 * @return {*}
 */
const logAction = (url: string): any => {
  try {
    document.getElementById('tracklog')?.setAttribute('src', url);
  } catch (error) {
    console.warn(error);
  }
};

/**
 * @description: tck 五维度
 * @param {*} tck 3M030000 => 3 M 03 00 00
 * @return {level1} 一维 码头：1位 2数据库 3日志加数据库（默认）
 * @return {level2} 二维 业务链类型：1位 M（关键主节点）S（子节点）
 * @return {level3} 三维 业务链模块/菜单：2位 01初检 02复检
 * @return {level4} 四维 事件类型：2位 01任务 02会话
 * @return {level5} 五维 业务链动作/按钮：2位 01拾取 02提交 03暂存 04预检
 * @return {mold} 一二三大类
 */
const getTckLevel = (tck: string): { [key: string]: any } => {
  try {
    const level1 = tck?.substring(0, 1);
    const level2 = tck?.substring(1, 2);
    const level3 = tck?.substring(2, 4);
    const level4 = tck?.substring(4, 6);
    const level5 = tck?.substring(6, 8);
    const mold = level1 + level2 + level3;
    const tcks = { mold, level1, level2, level3, level4, level5 };
    return tcks;
  } catch (e) {
    console.log(e);
    return {
      mold: null,
      level1: null,
      level2: null,
      level3: null,
      level4: null,
      level5: null,
    };
  }
};

/**
 * @description: 数据过滤 防止content导致header过大
 * @description: 相关特殊TCK码参考以下表
 * @description: https://za-cs.yuque.com/org-wiki-za-cs-ukegg2/puma/hfp7048l2fd4sb8g
 * @param {*} param
 * @return {*}
 */
const filterParams = (trackData: TrackData) => {
  const { taskData = {}, tabs } = store.getState() as StoreState;
  if (!trackData.tck) return trackData.content;
  // 通用参数
  const obj: Partial<Content> = {};
  const { level3 } = getTckLevel(trackData.tck);
  try {
    let data;
    // 初检
    if (level3 === '01') {
      data = taskData[`初检-${tabs.activeKey}`];
    }
    // 复检
    if (level3 === '02') {
      data = taskData[`复检-${tabs.activeKey}`];
    }
    if (data) {
      Object.assign(obj, {
        taskId: data.id,
        poolId: data.poolDefineId,
        pool_name: data.poolDefineName,
        seatId: data.seatCode,
        seatName: data.servantName,
        customerNmae: data.custormName,
        customerPhone: data.custormPhone,
        policyNo: is.Array(data.extraData)
          ? data.extraData.find((v: any) => v?.label === '保单号')?.value
          : undefined,
        woNo: is.Array(data.extraData)
          ? data.extraData.find((v: any) => v?.label === '工单号')?.value
          : undefined,
        ocsNo: is.Array(data.extraData)
          ? data.extraData.find((v: any) => v?.label === '投诉单号')?.value
          : undefined,
        ...trackData.content,
      });
    }
    if (JSON.stringify(obj).length > 1000) return {};
    return obj;
  } catch (e) {
    console.warn('埋点异常', e);
    return {};
  }
};

/**
 * @description: 公共track埋点event
 * @param {any} tck 后端唯一tck码
 * @param {any} params  业务参数对象 非必填
 * @param {any} operation   触点名称，中文 非必填 后端可一一对应
 * @param {any} tckId  业务ID 非必填
 * @return {*}
 */
export const commonTrackEvent = (trackData: TrackData): void => {
  const content = JSON.stringify(filterParams(trackData));
  const logs = Object.assign(trackData, { content });
  const search = new URLSearchParams(logs as any).toString();
  const url = `/api/v1/lionk/addTrack.gif?${search}`;
  logAction(url);
  console.log(
    `%c::commonTrackEvent::\n${trackData.tck}-${trackData.operation || ''}`,
    'color:#531dab;',
  );
  console.log(':::logs:::', logs);
  console.log(':::content:::', content);
};
