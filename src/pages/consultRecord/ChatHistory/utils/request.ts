/*
 * @Author: wb_luyin <EMAIL>
 * @Date: 2022-05-09 13:42:16
 * @Descripttion:
 * @LastEditors: wb_luyin <EMAIL>
 * @LastEditTime: 2022-12-02 10:46:50
 * @FilePath: /za-ark-puma-static/src/utils/request.ts
 * Copyright (c) 2022 by ZA-客户服务与体验中心, All Rights Reserved.
 */
import { SSOLogIn } from '@/utils/sso';
import { message } from 'antd';
import axios from 'axios';
import { logoutUrl } from 'constants/logoutUrl';

const url = new URLSearchParams(window.location.search);
const access_token = url.get('access_token');

if (!access_token) {
  window.location.href = logoutUrl[window.location.hostname];
} else {
  window.sessionStorage.setItem('access_token', access_token);
}

const getToken = () => {
  return access_token || sessionStorage.getItem('access_token');
};

const request = axios.create({
  headers: {
    Authorization: `Bearer ${getToken()}`,
    'Cache-Control': 'no-cache',
    Pragma: 'no-cache',
    'X-Requested-With': 'XMLHttpRequest',
  },
  withCredentials: true,
});

request.interceptors.response.use(
  (response) => {
    const { status, code, errorCode } = response.data;
    if (status !== 200) {
      if (response.config.headers['auto-message'] !== 'nerver') {
        response.data.message && message.error(response.data.message);
      }
    }
    if (code === 401 || errorCode === '401') {
      SSOLogIn();
    }
    return response;
  },
  (error) => {
    if (!error.message?.includes('401')) message.error('服务器发生错误');
    return Promise.reject(error);
  }
);

const pureRequest = axios.create({});

export { request, pureRequest };
