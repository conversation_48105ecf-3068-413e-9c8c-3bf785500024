import { Tooltip } from 'antd';
import dayjs from 'moment';
import React from 'react';

export const uiDate = (time: number) => {
  const t = dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  return <Tooltip title={t}>{t}</Tooltip>;
};

export const uiTooltip = (r: any) => (
  <Tooltip placement="topLeft" title={r}>
    {r}
  </Tooltip>
);

export const formatDate = (time: any) => {
  if (time) {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  }
  return time;
};

export const avatorColor = [
  '#2D92F3',
  '#97C954',
  '#6DD0B8',
  '#67BDF4',
  '#8698F2',
  '#E2ADF8',
  '#EFA7A8',
  '#EBC582',
  '#EF7D70',
];
