/*
 * @Author: wb_luyin <EMAIL>
 * @Date: 2022-07-18 10:54:48
 * @Descripttion:
 * @LastEditors: wb_luyin <EMAIL>
 * @LastEditTime: 2023-03-16 16:52:23
 * @FilePath: /za-ark-puma-static/src/utils/tool.ts
 * Copyright (c) 2022 by ZA-客户服务与体验中心, All Rights Reserved.
 */
/**
 * @name: 数据校验
 */
class Verify {
  //  数组类型以及非空校验
  Array(data: any): boolean {
    try {
      return Array.isArray(data) && data.length ? true : false;
    } catch (error) {
      console.warn(error, 'Array');
      return false;
    }
  }
  //  对象类型以及非空校验
  Object(data: any): boolean {
    try {
      return Object.getPrototypeOf(data) === Object.prototype && Object.keys(data).length ? true : false;
    } catch (error) {
      console.warn(error, 'Object');
      return false;
    }
  }
  //  数字类型转换，转换失败返回-1
  toNumber(data: any): number {
    try {
      if (typeof data === 'number' && !isNaN(data)) return data;
      if (typeof data === 'string' && !isNaN(Number(data))) return Number(data);
      return -1;
    } catch (error) {
      console.warn(error, 'toNumber');
      return -1;
    }
  }
  //  字符串类型转换,转换失败返回''
  toString(data: any): string {
    try {
      if (typeof data === 'string') return data;
      return String(data);
    } catch (error) {
      console.warn(error, 'toString');
      return '';
    }
  }
}
const is = new Verify();

// 深度遍历
const treeIterator = (data: any[], fn: any) => {
  data?.forEach((item) => {
    fn(item);
    is.Array(item.children) && treeIterator(item.children, fn);
  });
};

export { is, treeIterator };
