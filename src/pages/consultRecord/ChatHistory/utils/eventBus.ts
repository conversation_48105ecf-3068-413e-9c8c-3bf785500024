/*
 * @Author: wb_luyin <EMAIL>
 * @Date: 2022-07-05 15:15:06
 * @Descripttion:
 * @LastEditors: wb_luyin <EMAIL>
 * @LastEditTime: 2022-07-08 16:47:01
 * @FilePath: /za-ark-puma-static/src/utils/eventBus.ts
 * Copyright (c) 2022 by ZA-客户服务与体验中心, All Rights Reserved.
 */
class Handler {
  fns: any[] = [];

  add(fn: any) {
    this.fns.push(fn);
  }
  remove(fn: any) {
    const i = this.fns.indexOf(fn);
    if (i >= 0) {
      this.fns.splice(i, 1);
    }
  }
  invoke(data: any) {
    this.fns.forEach((fn) => {
      try {
        fn(data);
      } catch (error) {
        console.error(error);
      }
    });
  }
}

class EventBus {
  handlers = {};
  isFunc(fn: any) {
    return typeof fn === 'function';
  }
  on(eventName: string, fn: any) {
    if (!eventName) {
      throw new Error('事件名无效');
    }
    if (!this.isFunc(fn)) {
      throw new Error('必须提供事件函数');
    }
    if (!(eventName in this.handlers)) {
      this.handlers[eventName] = new Handler();
    }
    this.handlers[eventName].add(fn);
  }
  off(eventName: string, fn: any) {
    if (!eventName) {
      return;
    }
    if (this.handlers[eventName]) {
      if (!fn) {
        delete this.handlers[eventName];
      } else {
        this.handlers[eventName].remove(fn);
      }
    }
  }
  emit(eventName: string, data: any) {
    if (!eventName) {
      return;
    }
    if (eventName in this.handlers) {
      this.handlers[eventName].invoke(data);
    } else {
      return 'noExistEvent';
    }
  }
}

export default new EventBus();
