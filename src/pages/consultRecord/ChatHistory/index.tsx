import addViolationIcon from '@/assets/add-violation.svg';
import ClientAvatar from '@/assets/clientAvatar.svg';
import { ReactComponent as DeskAvatar } from '@/assets/deskAvatar.svg';
import pauseIcon from '@/assets/pause-new.svg';
import playIcon from '@/assets/play.svg';
import MarkdownRender from '@/components/MarkdownRender';
import { MSG_ID_PREFIX } from '@/utils/constants';
import { Image, Spin, Tooltip } from 'antd';
import classnames from 'classnames';
import { debounce, isEmpty } from 'lodash';
import React, { FC, Fragment, HTMLAttributes, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import playing from './img/playing.gif';
import playleft from './img/playleft.png';
import styles from './index.module.scss';
import { AudioTextType } from './services/types/manualQuality';
import SimpleAudio from './SimpleAudio';
import { avatorColor, formatDate } from './utils/ui';

const renderMatchMsg = (msg: string, words: string[]) => {
  return <MarkdownRender markdown={msg} highlightWords={words} />;
};

const formatTime = (milliseconds: number) => {
  // 将毫秒转换为秒
  const totalSeconds = Math.floor(milliseconds / 1000);
  // 计算分钟数和剩余的秒数
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  // 格式化分钟数和秒数，确保它们的长度为2位
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(seconds).padStart(2, '0');
  // 拼接成 mm:ss 格式的字符串
  return `${formattedMinutes}:${formattedSeconds}`;
};

const getNameList = (item?: any, seatName?: string): any[] =>
  (item?.transName || item?.servantName || seatName)?.split(',')?.filter(Boolean) || [];

export interface ChatHistoryProps extends HTMLAttributes<HTMLDivElement> {
  list?: AudioTextType[];
  heightLightMsgId?: string;
  isSimple?: boolean;
  currentMsgId?: string;
  hasAudio?: boolean;
  seatName?: string;
  onPlayAudio?: (beginTimeOffset: number) => void;
  onPause?: () => void;
  openViolationModal?: (...args: any[]) => void;
}

const ChatHistory: FC<ChatHistoryProps> = ({
  list,
  heightLightMsgId,
  isSimple,
  currentMsgId,
  hasAudio,
  seatName,
  onPlayAudio,
  onPause,
  openViolationModal,
}) => {
  const [currentAudio, setCurrentAudio] = useState<AudioTextType>();
  const [isPlay, setIsPlay] = useState(false);
  const bubbleRefs = useRef<Record<string, HTMLDivElement>>({});
  const [loadedMap, setLoadedMap] = useState<any>({});
  const [containerWidth, setContainerWidth] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const getWidthNumRef = useRef(0);

  const nameData = useMemo(() => {
    const names = new Set();
    list?.forEach((item) => {
      const nameList = getNameList(item, seatName);
      nameList?.forEach((name) => {
        name && names.add(name);
      });
    });
    return [...names].map((name, index) => ({
      name,
      color: avatorColor[index],
    }));
  }, [list, seatName]);

  const getAvatarColor = useCallback(
    (item: any) => {
      const name = getNameList(item, seatName)[0];
      return nameData?.find((v) => v.name === name)?.color || avatorColor[0];
    },
    [nameData, seatName]
  );

  const renderName = useCallback(
    (item: any) => {
      const names = getNameList(item, seatName);
      return (
        !!(item.side !== 1 && names?.length) && (
          <span>
            {names.map((name, index) => (
              <Fragment key={name}>
                <i
                  style={
                    nameData?.length > 1
                      ? {
                          color: nameData?.find((v) => v.name === name)?.color || avatorColor[0],
                          fontStyle: 'normal',
                        }
                      : { fontStyle: 'normal' }
                  }
                >
                  {name}
                </i>
                {index !== names.length - 1 ? '、' : ''}
              </Fragment>
            ))}
          </span>
        )
      );
    },
    [nameData, seatName]
  );

  const renderAvatar = useCallback(
    (item: AudioTextType) => {
      return (
        <div className={styles.avatar}>
          {item.side === 1 ? <img src={ClientAvatar} /> : <DeskAvatar style={{ color: getAvatarColor(item) }} />}
        </div>
      );
    },
    [getAvatarColor]
  );

  const getContainerWidth = useCallback(
    debounce(() => {
      if (!containerRef.current?.offsetWidth && getWidthNumRef.current < 5) {
        getWidthNumRef.current += 1;
        getContainerWidth();
      } else {
        setContainerWidth(containerRef.current?.offsetWidth || 0);
      }
    }, 300),
    []
  );

  useEffect(() => {
    getContainerWidth();
    window.addEventListener('resize', getContainerWidth);
    return () => {
      window.removeEventListener('resize', getContainerWidth);
    };
  }, [getContainerWidth]);

  const renderBubble = useCallback(
    (item: AudioTextType, index: number) => (
      <div className={classnames(styles.w90, (item.side === 2 || item.side === 3) && styles.alignR)}>
        <div className={styles.time}>
          {renderName(item)}
          {formatDate(item.beginTime)}
        </div>
        <div>
          {item.messageType === 2 ? (
            <div className={styles.imgStyle}>
              <Image
                style={{ borderRadius: '5px', maxWidth: '150px', maxHeight: '150px' }}
                src={item.text}
                fallback="data:image/png;base64,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"
              />
            </div>
          ) : item.messageType === 3 ? (
            <Spin spinning={!loadedMap[item.msgId]}>
              <div
                className={styles.bubble}
                style={{
                  minWidth: 70,
                  ...(containerWidth && containerWidth > 150 ? { maxWidth: containerWidth - 80 } : {}),
                  ...(item.side === 2 || item.side === 3 ? {} : { marginRight: 0 }),
                  width: loadedMap[item.msgId] ? `${loadedMap[item.msgId] * 10}px` : '70px',
                  textAlign: item.side === 2 || item.side === 3 ? 'right' : 'left',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  if (isEmpty(currentAudio)) {
                    setCurrentAudio(item);
                    setIsPlay(true);
                  } else {
                    if (currentAudio?.msgId === item.msgId) {
                      setCurrentAudio(undefined);
                      setIsPlay(false);
                    } else {
                      setCurrentAudio(item);
                      setIsPlay(true);
                    }
                  }
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <img
                    width={isPlay && item.msgId === currentAudio?.msgId ? '15px' : '12px'}
                    height="16px"
                    src={isPlay && item.msgId === currentAudio?.msgId ? playing : playleft}
                    alt=""
                    className={item.side === 2 || item.side === 3 ? styles.playingright : ''}
                  />
                  <div style={{ marginLeft: 8 }}>{loadedMap[item.msgId]}”</div>
                </div>
                <SimpleAudio
                  src={item.url || item.text}
                  onEnd={() => {
                    setCurrentAudio(undefined);
                    setIsPlay(false);
                  }}
                  msgId={item.msgId}
                  currentMsgId={currentAudio?.msgId}
                  onLoaded={(duration) => {
                    const map = { ...loadedMap };
                    setLoadedMap({ ...map, [item.msgId]: duration });
                  }}
                />
              </div>
              <div className={styles.asrText}>{item.text}</div>
            </Spin>
          ) : (
            <div
              className={classnames(
                styles.bubble,
                (item.side === 2 || item.side === 3) && item.isContainRealSens === 'Y'
                  ? styles.dotLeft
                  : item.side === 1 && item.isContainRealSens === 'Y'
                    ? styles.dotRight
                    : '',
                item.msgId === heightLightMsgId ? styles.highLight : '',
                item.msgId === currentMsgId ? styles.currentMsg : '',
                !!item.isHit ? styles.hit : ''
              )}
            >
              {renderMatchMsg(item.text, item.sensitiveStr)}
            </div>
          )}
          <div
            className={styles.action}
            style={item.side === 2 || item.side === 3 ? { flexDirection: 'row-reverse' } : {}}
          >
            <div
              className={styles.audioBox}
              style={!(item.side === 2 || item.side === 3) ? { justifyContent: 'flex-end' } : {}}
            >
              {hasAudio &&
                (currentMsgId === item.msgId ? (
                  <Tooltip
                    title="暂停播放"
                    getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
                  >
                    <img src={pauseIcon} className={styles.playIcon} onClick={() => onPause?.()} />
                  </Tooltip>
                ) : (
                  <Tooltip
                    title="语音播放"
                    getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
                  >
                    <img
                      src={playIcon}
                      className={styles.playIcon}
                      onClick={() => onPlayAudio?.(item?.beginTimeOffset)}
                    />
                  </Tooltip>
                ))}
              {(item.side === 2 || item.side === 3) && (
                <Tooltip
                  title="添加违规项"
                  getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
                >
                  <img
                    className={styles.playIcon}
                    src={addViolationIcon}
                    onClick={() => openViolationModal?.(index, item?.transName || item?.servantName || seatName)}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      </div>
    ),
    [
      currentAudio,
      isPlay,
      heightLightMsgId,
      currentMsgId,
      hasAudio,
      loadedMap,
      containerWidth,
      seatName,
      onPlayAudio,
      onPause,
      openViolationModal,
      renderName,
    ]
  );

  const renderText = useCallback(
    (item: AudioTextType) => {
      if (item.source === 'SYSTEM') {
        return (
          <div key={item.msgId} className={styles.tips}>
            <div style={{ color: '#4f53d9', fontSize: 12, fontWeight: 500 }}>
              <span style={{ color: 'rgba(79, 83, 217, 0.9)' }}>——————</span> {item.text}{' '}
              <span style={{ color: 'rgba(79, 83, 217, 0.9)' }}>——————</span>
            </div>
          </div>
        );
      }
      return item.messageType === 2 ? (
        <div className={styles.imgStyle}>
          <Image
            style={{ borderRadius: '5px', maxWidth: '150px', maxHeight: '150px' }}
            src={item.text}
            fallback="data:image/png;base64,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"
          />
        </div>
      ) : item.messageType === 3 ? (
        <div
          className={styles.bubble}
          style={{
            width: '70px',
            cursor: 'pointer',
          }}
          // onClick={() => handlePlay(item)}
        >
          <img
            width={isPlay && item.msgId === currentAudio?.msgId ? '15px' : '12px'}
            height="16px"
            src={isPlay && item.msgId === currentAudio?.msgId ? playing : playleft}
            alt=""
            className={item.side === 2 || item.side === 3 ? styles.playingright : ''}
          />
        </div>
      ) : (
        <div
          key={item.msgId}
          className={classnames(
            styles.bubble,
            item.msgId === heightLightMsgId ? styles.highLight : '',
            item.msgId === currentMsgId ? styles.currentMsg : '',
            !!item.isHit ? styles.hit : ''
          )}
        >
          {renderMatchMsg(item.text, item.sensitiveStr)}
        </div>
      );
    },
    [isPlay, currentAudio, heightLightMsgId, currentMsgId]
  );

  const renderItem = useCallback(
    (item: AudioTextType, index: number) => {
      if (item.source === 'SYSTEM') {
        return (
          <div key={item.msgId} className={styles.tips}>
            <div style={{ color: '#4f53d9', fontSize: 12, fontWeight: 500 }}>
              <span style={{ color: 'rgba(79, 83, 217, 0.9)' }}>——————</span> {item.text}{' '}
              <span style={{ color: 'rgba(79, 83, 217, 0.9)' }}>——————</span>
            </div>
            <div style={{ color: '#666666', fontSize: 12 }}>{formatDate(item.beginTime)}</div>
          </div>
        );
      }
      if (item.side === 1) {
        return (
          <div
            id={`${MSG_ID_PREFIX}${item.msgId}`}
            key={item.msgId}
            ref={(ref) => ref && (bubbleRefs.current[index] = ref)}
          >
            <div className={styles.item}>
              {renderAvatar(item)}
              {renderBubble(item, index)}
            </div>
          </div>
        );
      } else if (item.side === 2 || item.side === 3) {
        return (
          <div
            id={`${MSG_ID_PREFIX}${item.msgId}`}
            key={item.msgId}
            ref={(ref) => ref && (bubbleRefs.current[index] = ref)}
          >
            <div className={classnames(styles.item, styles.reverseItem)}>
              {renderBubble(item, index)}
              {renderAvatar(item)}
            </div>
          </div>
        );
      }
      return null;
    },
    [renderBubble, renderAvatar]
  );

  return (
    <div className={isSimple ? styles.simpleContainer : styles.container} ref={containerRef}>
      {list?.map((item, index) => {
        return isSimple ? (
          <div key={`${item.msgId}__${index}`} id={`msgRow-${item.msgId}`} data-rowid={item.msgId} className="msgRow">
            <div id={`${MSG_ID_PREFIX}${item.msgId}`} className={styles.simpleRow}>
              <div className={styles.simpleRowAvatar}>
                {item.side === 1 ? <img src={ClientAvatar} /> : <DeskAvatar style={{ color: getAvatarColor(item) }} />}
              </div>
              <div className={styles.simpleRowContent}>
                <div className={styles.beginTime}>
                  {formatTime(item.beginTimeOffset)}
                  {renderName(item)}
                </div>
                {renderText(item)}
                <div className={styles.action}>
                  <div className={styles.audioBox} style={{ justifyContent: 'flex-end' }}>
                    {(item.side === 2 || item.side === 3) && (
                      <Tooltip
                        title="添加违规项"
                        getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
                      >
                        <img
                          className={styles.playIcon}
                          src={addViolationIcon}
                          onClick={() => openViolationModal?.(index, item?.transName || item?.servantName || seatName)}
                        />
                      </Tooltip>
                    )}
                    {hasAudio &&
                      (currentMsgId === item.msgId ? (
                        <Tooltip
                          title="暂停播放"
                          getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
                        >
                          <img src={pauseIcon} className={styles.playIcon} onClick={() => onPause?.()} />
                        </Tooltip>
                      ) : (
                        <Tooltip
                          title="语音播放"
                          getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
                        >
                          <img
                            src={playIcon}
                            className={styles.playIcon}
                            onClick={() => onPlayAudio?.(item?.beginTimeOffset)}
                          />
                        </Tooltip>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div id={`msgRow-${item.msgId}`} data-rowid={item.msgId} className="msgRow" key={`${item.msgId}__${index}`}>
            {renderItem(item, index)}
          </div>
        );
      })}
    </div>
  );
};

export default memo(ChatHistory);
