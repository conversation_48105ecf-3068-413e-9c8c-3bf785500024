/*
 * @Author: your name
 * @Date: 2021-12-29 15:31:29
 * @LastEditTime: 2022-01-18 14:02:19
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /za-ark-puma-static/src/components/Iconfont/index.tsx
 */
import classnames from 'classnames';
import React, { CSSProperties, HTMLProps } from 'react';
import KehuSvg from '../img/app.svg';
import KefuSvg from '../img/kefu.svg';
import robot from '../img/robot.svg';
import styles from './index.module.scss';

export interface IconFontProps extends HTMLProps<HTMLSpanElement> {
  iconStyle?: CSSProperties;
  iconClassName?: string;
  spin?: boolean;
  type: string;
}

type Options = { url: string };

const Iconfont = (props: IconFontProps) => {
  const { type, iconStyle, iconClassName, className, spin, ...rest } = props;

  return (
    <span role="img" className={classnames(styles.action, className)} {...rest}>
      {type === 'icon-robot' ? (
        // 只能客服头像
        <img src={robot} width={35} height={35} alt="" />
      ) : // <svg
      //   style={iconStyle}
      //   className={classnames(styles.icon, spin ? styles.spin : '', iconClassName)}
      //   aria-hidden="true"
      //   focusable="false"
      // >
      //   <use xlinkHref={`#${type}`}></use>
      // </svg>
      // <img src={robot} width={35} height={35} alt="" />
      type === 'icon-kehu1' ? (
        <img src={KehuSvg} width={40} height={40} />
      ) : (
        <img src={KefuSvg} width={40} height={40} />
      )}
    </span>
  );
};

Iconfont.customCache = new Set<string>();

Iconfont.config = (options: Options) => {
  const { url } = options;
  if (
    typeof document !== 'undefined' &&
    typeof window !== 'undefined' &&
    typeof document.createElement === 'function' &&
    !Iconfont.customCache.has(url)
  ) {
    const script = document.createElement('script');
    script.setAttribute('src', url);
    script.setAttribute('data-namespace', url);
    Iconfont.customCache.add(url);
    document.body.appendChild(script);
  }
};

export default Iconfont;
