@-webkit-keyframes antRotate {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes antRotate {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.spin {
  animation: antRotate 1s infinite linear;
}

.action {
  display: inline-block;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  svg {
    display: inline-block;
  }

  > * {
    line-height: 1;
  }
}

.icon {
  width: 1em;
  height: 1em;
  overflow: hidden;
  fill: currentColor;
}
