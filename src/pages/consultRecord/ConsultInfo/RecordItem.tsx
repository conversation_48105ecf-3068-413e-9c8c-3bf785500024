import { ReactComponent as IconArrow } from '@/assets/arrow.svg';
import { ReactComponent as DeleteIcon } from '@/assets/delete.svg';
import Collapse from '@/components/Collapse';
import { delQualityViolationItems, delRealTimeQualityViolationItems } from '@/services/ce';
import { customIsEmpty } from '@/utils';
import { RULE_TAGGING } from '@/utils/constants';
import { useSelector } from '@magi/magi';
import { Button, Col, Divider, Form, Input, InputNumber, Popconfirm, Radio, Row, Select, Tag } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { FC, Fragment, memo, useCallback, useMemo, useState } from 'react';
import styles from './index.scss';

const RecordItem: FC<any> = ({
  record,
  editBoolData,
  setEditBoolData,
  form,
  onChildValue,
  redstraIds,
  refreshData,
  curLabelList,
  curRiskList,
  isRedLineRobot,
  isRealTime,
}) => {
  const {
    labelList: noRedLabelList,
    redLabelList,
    riskList: noRedRiskList,
    redRiskList,
    userInfo,
  } = useSelector((state: { global: any }) => state.global);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);

  const labelList = useMemo(() => {
    return record?.redRuleType ? redLabelList : isRedLineRobot ? curLabelList : noRedLabelList;
  }, [record?.redRuleType, redLabelList, noRedLabelList, curLabelList, isRedLineRobot]);

  const riskList = useMemo(() => {
    return record?.redRuleType ? redRiskList : isRedLineRobot ? curRiskList : noRedRiskList;
  }, [record?.redRuleType, redRiskList, noRedRiskList, curRiskList, isRedLineRobot]);

  const handleClickWord = useCallback(
    (sensitiveWord: any, data: { msgId?: number; line?: string | number }) => {
      onChildValue({ sensitiveWord: sensitiveWord, ...(data || {}) });
    },
    [onChildValue]
  );

  const handleDelete = useCallback(async () => {
    try {
      setIsDeleteLoading(true);
      const res = await (isRealTime ? delRealTimeQualityViolationItems : delQualityViolationItems)({
        id: record.id,
        tenantNo: record.tenantNo,
        strategyId: record.strategyId,
        ruleId: record.ruleId,
        sessionId: record.sessionId,
        operator: userInfo?.name || userInfo?.adAccount,
      });
      if (res?.data?.value) {
        await refreshData();
      }
    } finally {
      setIsDeleteLoading(false);
    }
  }, [record, refreshData, userInfo, isRealTime]);

  const onValuesChange = useCallback(
    (changedValues: any) => {
      const key = Object.keys(changedValues)?.[0];
      key && setEditBoolData((preState: any) => (preState[key] ? preState : { ...preState, [key]: true }));
    },
    [setEditBoolData]
  );

  const isManualFeedback = useMemo(() => record?.recordAnalyze === '人工反馈', [record?.recordAnalyze]);

  const seatNames = useMemo(() => {
    return record?.seatName?.split(',')?.filter(Boolean) || [];
  }, [record?.seatName]);

  const labelIds = useMemo(() => {
    const ids = record.label
      ?.split(',')
      ?.filter(Boolean)
      ?.map((v: string) => Number(v));
    return ids?.length ? ids : undefined;
  }, [record.label]);

  const isEdit = useMemo(() => {
    return !(
      (Array.isArray(redstraIds)
        ? (redstraIds?.includes(record.strategyId) && record.ruleTagging) || !redstraIds?.includes(record.strategyId)
        : record.ruleTagging) && !editBoolData[record.id]
    );
  }, [redstraIds, editBoolData, record]);

  const isShowEditBtn = useMemo(
    () => !isEdit && (!Array.isArray(redstraIds) || redstraIds?.includes(record.strategyId)),
    [isEdit, redstraIds, record.strategyId]
  );

  const viewList = useMemo(() => {
    const riskName = riskList?.find((v: any) => v.value === record?.level)?.label;
    const babelNames = labelList
      ?.filter((v: any) => labelIds?.includes(v.value))
      ?.map((v: any) => v.label)
      ?.join('、');
    return [
      { label: '风险等级', value: riskName, span: 12 },
      { label: '评分', value: record?.score, span: 12 },
      { label: '标签', value: babelNames, span: 24 },
    ].filter((v) => !customIsEmpty(v.value));
  }, [riskList, labelList, labelIds, record]);

  return (
    <div className={styles.container}>
      <Form form={form} onValuesChange={onValuesChange} layout="vertical" className={styles.formBox}>
        {isShowEditBtn && isManualFeedback && (
          <Popconfirm title="是否删除此条结果？" onConfirm={handleDelete} okButtonProps={{ loading: isDeleteLoading }}>
            <Button type="link" className={styles.deleteBtn}>
              <DeleteIcon />
            </Button>
          </Popconfirm>
        )}
        {record.lines?.length ? (
          <Button
            type="primary"
            className={styles.title}
            onClick={() => handleClickWord(null, { line: record.lines[0] })}
            size="small"
          >
            {record.hitRule}
          </Button>
        ) : (
          <h1 className={styles.title}>{record.hitRule}</h1>
        )}
        <div className={styles.tagList}>
          {isManualFeedback ? <Tag color="red">人工反馈</Tag> : <Tag color="purple">AI命中</Tag>}
          {!!seatNames?.length && seatNames.map((seatName: string) => <Tag key={seatName}>{seatName}</Tag>)}
          {!!record?.gmtModified && <Tag>{dayjs(record.gmtModified).format('YYYY-MM-DD HH:mm:ss')}</Tag>}
        </div>
        {!!record?.qualityTaskKeyWordDTOList?.length && (
          <div className={styles.listItem}>
            <div className={styles.label}>关键词</div>
            <div className={styles.value}>
              {record.qualityTaskKeyWordDTOList.map((item: any, index: number) => (
                <Fragment key={`${item.msgId}__${index}`}>
                  <span onClick={() => handleClickWord(item?.keyWordName, { msgId: item?.msgId })}>
                    {item.keyWordName}
                  </span>
                  {index !== record.qualityTaskKeyWordDTOList.length - 1 ? '、' : ''}
                </Fragment>
              ))}
            </div>
          </div>
        )}
        {!isManualFeedback && !customIsEmpty(record?.recordAnalyze) && (
          <div className={styles.listItem}>
            <div className={styles.label}>原因分析</div>
            <div className={styles.value}>{record.recordAnalyze}</div>
          </div>
        )}
        <Form.Item
          label={
            <>
              结果反馈
              {isShowEditBtn && (
                <Button
                  type="link"
                  style={{ padding: '0 15px', border: 'none', height: 'inherit' }}
                  onClick={() =>
                    setEditBoolData((preState: any) =>
                      preState[record.id] ? preState : { ...preState, [record.id]: true }
                    )
                  }
                >
                  修改
                </Button>
              )}
            </>
          }
          className={classNames({
            [styles.formItemRequired]: !isManualFeedback,
            [styles.formItemViewEmpty]: !isEdit && isManualFeedback,
          })}
          style={{ marginBottom: isEdit ? 8 : 0 }}
        >
          {isEdit ? (
            <>
              <Form.Item
                name={[record.id, 'ruleTagging']}
                style={{ marginBottom: 0 }}
                initialValue={record.ruleTagging}
                rules={[{ required: !isManualFeedback && !!editBoolData[record.id], message: '请选择' }]}
                hidden={isManualFeedback}
              >
                <Radio.Group className={styles.radioGroup} options={RULE_TAGGING} />
              </Form.Item>
              <Form.Item noStyle name={[record.id, 'failMessage']} initialValue={record.failMessage}>
                <Input.TextArea
                  style={{ marginBottom: 8 }}
                  placeholder="结果反馈说明"
                  showCount
                  maxLength={500}
                  rows={3}
                />
              </Form.Item>
            </>
          ) : (
            <>
              {!isManualFeedback && !!RULE_TAGGING?.find(({ value }) => value === record.ruleTagging)?.label && (
                <Tag
                  className={styles.tag}
                  color={record.ruleTagging === '1' ? 'success' : record.ruleTagging === '2' ? 'warning' : 'error'}
                >
                  {RULE_TAGGING?.find(({ value }) => value === record.ruleTagging)?.label}
                </Tag>
              )}
              {!customIsEmpty(record?.failMessage) && <p className={styles.valueMsg}>{record.failMessage}</p>}
            </>
          )}
        </Form.Item>
        {isEdit ? (
          <Collapse
            className={styles.collapse}
            children={
              <Row gutter={24} style={{ marginTop: 8 }}>
                <Col xl={12} lg={24}>
                  <Form.Item label="风险等级" name={[record.id, 'level']} initialValue={record.level || undefined}>
                    <Select
                      allowClear
                      options={riskList}
                      placeholder="选择风险等级"
                      optionRender={(option) => (
                        <span className={classNames({ red: option?.data?.isRedLine === '1' && !isRedLineRobot })}>
                          {option?.data?.label}
                        </span>
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col xl={12} lg={24}>
                  <Form.Item label="评分" name={[record.id, 'score']} initialValue={record.score}>
                    <InputNumber placeholder="请输入评分" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xl={24} lg={24}>
                  <Form.Item
                    label="标签"
                    name={[record.id, 'label']}
                    style={{ marginBottom: 0 }}
                    initialValue={labelIds}
                  >
                    <Select
                      allowClear
                      options={labelList}
                      mode="multiple"
                      placeholder="请选择标签"
                      optionRender={(option) => (
                        <span className={classNames({ red: option?.data?.isRedLine === '1' && !isRedLineRobot })}>
                          {option?.data?.label}
                        </span>
                      )}
                    />
                  </Form.Item>
                </Col>
              </Row>
            }
            activeLabel={
              <Divider style={{ borderColor: '#C9CDD4' }}>
                更多
                <IconArrow />
              </Divider>
            }
            unActiveLabel={
              <Divider style={{ borderColor: '#C9CDD4' }}>
                更多
                <IconArrow style={{ transform: 'rotate(180deg)' }} />
              </Divider>
            }
          />
        ) : (
          <Row gutter={24}>
            {viewList.map(({ label, value, span }) => (
              <Col xl={span} lg={24} key={label}>
                <Form.Item label={label} className={styles.viewFormItem}>
                  <p className={styles.valueMsg} style={{ margin: 0 }}>
                    {value}
                  </p>
                </Form.Item>
              </Col>
            ))}
          </Row>
        )}
      </Form>
    </div>
  );
};

export default memo(RecordItem);
