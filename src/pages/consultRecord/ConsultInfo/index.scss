$message-base-font-size: 12px;
$message-base-line-height: 1.5em;

$message-body-bg-color: #f4fafe;
$message-body-color: #4d6a7e;
$message-body-alt-bg-color: #eee;
$message-body-alt-color: #7a7a7a;

$message-block-full-bg-color: #d4eaf7;
$message-block-full-color: #72a9c9;
$message-block-warning-color: #ff4747;
$message-block-normal-color: #000000;
$message-block-note-bg-color: #f0f0f0;
$message-block-note-color: #c6c6c6;

.ConsultRecordContainer {
  .consultWrap {
    border: 1px solid #fbfbff;
    background: #f4f4fc;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 10px;

    .consultItem {
      line-height: 28px;
      display: flex;

      flex-flow: row wrap;

      .consult-item-part {
        max-width: 200px;
        display: inline-block;
      }

      .consultTitle {
        display: inline-block;
        text-align: right;
        color: #1d212f;
      }

      .consultContent {
        padding-left: 8px;
        word-break: break-all;
        color: #34384b;
      }
    }
  }

  .consult-date {
    float: right;
  }

  .consult-icon {
    display: inline-block;
    width: 20px;
    height: 22px;
    // background-image: url('../../images/chathistory.png');
    background-size: contain;
    vertical-align: middle;
    margin-left: 4px;
    cursor: pointer;
  }

  .more-tip {
    text-align: center;
    cursor: pointer;
  }
}

.ruleRemarkFormItem {
  :global {
    .ant-form-item-label {
      width: 81px;
    }
  }
}

.load-more-msg {
  background-color: #afafaf;
  height: 20px;
  line-height: 20px;
  color: #e5e5e5;
  text-align: center;
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
  }
}

.bianque-plugins-search-input {
  display: flex;

  .tcm {
    margin-left: 20px;
    width: 200px;
    height: 30px;
  }

  .btn-search {
    margin-left: 20px;
  }
}

.bianque-plugins-guideBasic-info {
  display: flex;

  .bianque-plugins-guideBasic-select {
    display: flex;
    font:
      12px/1.14 arial,
      \5b8b\4f53;
    color: #7a7a7a;
    background: rgb(248, 248, 248);
    height: 30px;
    width: 200px;
    line-height: 28px;
    border: 1px solid rgb(166, 166, 166);
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    outline: none;
    margin-top: 30px;
  }

  .bianque-plugins-guideBasic-label {
    display: flex;
    justify-content: space-between;
    font:
      12px/1.14 arial,
      \5b8b\4f53;
    color: #7a7a7a;
    line-height: 28px;
    outline: none;
    margin-top: 30px;

    .zhengjian-info {
      display: flex;
      justify-content: space-between;
      margin-left: 30px;
    }

    .patient-info {
      display: flex;
      justify-content: space-between;
      margin-left: 30px;
    }
  }
}

.bianque-plugins-guideBasic-input-item {
  display: flex;
  justify-content: space-between;
  font:
    12px/1.14 arial,
    \5b8b\4f53;
  color: #7a7a7a;
  background: rgb(248, 248, 248);
  line-height: 28px;
  border: 1px solid rgb(166, 166, 166);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
  outline: none;
  padding: 20px 40px 20px 40px;
  margin: 20px;

  .btn-log-health {
    color: #0000ff;
    border: none;
  }
}

//信息素、消息块
.es-message-block-box {
  display: block;
  position: relative;
  text-align: center;
  padding: 0 5px;

  .es-message-block {
    position: relative;
    display: block;
    font-size: $message-base-font-size;
    line-height: $message-base-line-height;
    padding: 5px;

    &.full {
      background: $message-block-full-bg-color;
      color: $message-block-full-color;
      font-size: $message-base-font-size;
      line-height: $message-base-line-height;
      white-space: normal;
      word-break: break-all;
      margin: 10px 0;
      border-radius: 5px;
      width: 100%;
    }

    &.warning {
      color: $message-block-warning-color;
    }

    &.normal {
      color: $message-block-normal-color;
    }

    &.note {
      display: inline-block;
      background-color: $message-block-note-bg-color;
      color: $message-block-note-color;
      max-width: 100%;
      min-width: 200px;
      margin: 0 auto;
    }
  }
}

//通用卡片消息
.es-common-card-message {
  width: 200px;
  border-radius: 3px;
  background: #f4fafe;
  padding: 10px 2px;

  .title {
    padding: 6px 0;
    border-bottom: #ccc dashed 1px;
    margin: 0 10px;
    color: #333;
  }

  .box {
    display: flex;
    padding: 6px 10px;

    .left {
      margin-right: 6px;

      .image {
        width: 32px;
        height: 32px;
      }
    }

    .right {
      flex: 1;

      .content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 130px;
      }
    }
  }
}

.es-message-main-suit-table {
  display: inline-block;
  border-radius: 5px;
  border: 1px solid #c5d1d9;
  width: 60%;
  max-width: 60%;
  color: #4d6a7e;
  position: relative;

  .es-subject {
    color: #46677f;
    background: #f1f9ff;
    font-weight: bold;
  }

  .es-main-suit-row-line {
    border-bottom: 1px solid #c5d1d9;
  }

  .es-main-suit-row-line:last-child {
    border-bottom: none;
  }

  .es-main-suit-col {
    padding: 10px;
    word-break: break-all;
  }

  .es-error-log {
    position: absolute;
    right: -50px;
    top: 0;
    border: #c5d1d9 solid 1px;
    padding: 1px 6px;
    border-radius: 2px;
    background: #f1f9ff;

    &:hover {
      cursor: pointer;
    }
  }

  .es-error-disable {
    background: #a29f9f;
    color: #bdbdbd;
  }

  .es-error-reasons {
    text-align: left;
    padding: 10px 0;

    li {
      border-bottom: 1px solid #ccc;
      line-height: 30px;

      label {
        display: block;
        padding: 5px 0;

        input {
          margin-right: 4px;
        }
      }

      span {
        padding-left: 10px;
      }
    }
  }

  .bg-yellow {
    background-color: #fcde59;
  }
}

.msg-text {
  img {
    max-height: 70px;
    width: auto;
    cursor: pointer;
  }
}

.chat-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.collapseSet {
  :global {
    .ant-collapse-header-text {
      &>div {
        margin-top: 0;
      }
    }

    .ant-form-item-label {
      width: 81px;
      text-align: right;
    }

    .ant-form-item-control-input-content {
      &>div {
        margin-left: 0 !important;
        width: 100% !important;
      }
    }
  }
}

.container {
  background-color: #f8f8f8;
  padding: 16px;
  border-radius: 8px;
  position: relative;

  .tag {
    border-radius: 4px;
    padding: 0 18px;
    height: 22px;
    line-height: 22px;
    font-size: 12px;

    &:global(.ant-tag-success) {
      color: #16643b;
      background: #d0fbe9;
      border-color: #84ebb4;
    }

    &:global(.ant-tag-error) {
      color: #ad1f2b;
      background: #ffd5d8;
      border-color: #ff97a0;
    }
  }

  .radioGroup {
    :global {
      .ant-radio-wrapper {
        border: 1px solid #d0d5dd;
        border-radius: 8px;
        padding: 6px 15px;
        background: #fff;
        margin-inline-end: 16px;
        margin-bottom: 8px;

        .ant-radio {
          .ant-radio-inner {
            border-color: #cacfd8;
          }
        }

        span:not(.ant-radio) {
          color: #0e121b;
          font-size: 14px;
          line-height: 14px;
          padding: 0 0 0 8px;
          font-weight: normal;
        }

        &:first-child {
          &.ant-radio-wrapper-checked {
            background: #dcfae6;

            .ant-radio-checked {
              .ant-radio-inner {
                background: #17b26a;
              }

              &::after {
                border-color: #17b26a;
              }
            }
          }

          &.ant-radio-wrapper-checked,
          &:hover {
            border-color: #17b26a;

            .ant-radio {
              .ant-radio-inner {
                border-color: #17b26a;
              }
            }
          }
        }

        &:nth-child(2) {
          &.ant-radio-wrapper-checked {
            background: #fee4e2;

            .ant-radio-checked {
              .ant-radio-inner {
                background: #f04438;
              }

              &::after {
                border-color: #f04438;
              }
            }
          }

          &.ant-radio-wrapper-checked,
          &:hover {
            border-color: #f04438;

            .ant-radio {
              .ant-radio-inner {
                border-color: #f04438;
              }
            }
          }
        }

        &:last-child {
          &.ant-radio-wrapper-checked {
            background: #fff0e3;

            .ant-radio-checked {
              .ant-radio-inner {
                background: #f2994a;
              }

              &::after {
                border-color: #f2994a;
              }
            }
          }

          &.ant-radio-wrapper-checked,
          &:hover {
            border-color: #f2994a;

            .ant-radio {
              .ant-radio-inner {
                border-color: #f2994a;
              }
            }
          }
        }
      }
    }
  }

  .listItem {
    font-size: 14px;

    &:not(:first-child) {
      margin-top: 16px;
    }

    .label {
      color: #4e5969;
      white-space: nowrap;
    }

    .value {
      color: #1d2129;
      font-weight: 500;
      margin-top: 8px;
      word-break: break-all;

      span {
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }

  .valueMsg {
    margin: 8px 0 0;
    color: #1d2129;
    font-weight: 500;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .title {
    color: #0e121b;
    font-size: 16px;
    line-height: 16px;
    margin: 0;
    font-weight: 600;

    &:global(.ant-btn) {
      background: #faad14;
      color: #fff;
      box-shadow: none;
      font-weight: 500;

      &:hover,
      &:active {
        background: #faad14 !important;
        color: #fff !important;
      }
    }
  }

  .tagList {
    :global {
      .ant-tag {
        margin-top: 15px;
        color: #2b303b;
        border-color: #e1e4ea;
        background: #f5f7fa;
        border-radius: 100px;
        white-space: nowrap;

        &:not(:last-child) {
          margin-right: 8px;
        }

        &.ant-tag-purple {
          color: #5b2cc9;
          border-color: #cac0ff;
          background: #efebff;
        }

        &.ant-tag-red {
          color: #d0257a;
          border-color: #ffc0df;
          background: #ffebf4;
        }
      }
    }
  }

  .deleteBtn {
    padding: 0;
    position: absolute;
    right: 16px;
    top: 16px;
    color: #0e121b;
    height: initial;
  }

  .formBox {
    &:global(> .ant-form-item) {
      margin-top: 16px;
    }

    :global {
      .ant-form-item {
        margin-bottom: 16px;
      }

      .ant-form-item-control-input {
        min-height: initial;
      }

      .ant-form-item-label {
        &>label {
          color: #0e121b;
        }
      }
    }

    .formItemRequired {
      :global {
        .ant-form-item-label {
          &::before {
            display: inline-block;
            margin-inline-end: 4px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: '*';
          }
        }
      }
    }

    .formItemViewEmpty {
      :global {
        .ant-form-item-label {
          padding-bottom: 0 !important;
        }
      }
    }

    .collapse {
      :global {
        .ant-divider {
          margin: 2px 0;

          .ant-divider-inner-text {
            font-size: 12px;
            color: #86909c;
            display: flex;
            align-items: center;
            font-weight: normal;

            svg {
              margin-left: 8px;
            }
          }
        }

        .ant-collapse-content {
          background: transparent;
        }
      }
    }

    .viewFormItem {
      margin-top: 16px;
      margin-bottom: 0;
    }
  }
}