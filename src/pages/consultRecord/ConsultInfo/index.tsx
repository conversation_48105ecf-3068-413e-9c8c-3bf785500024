import useRobotData from '@/hooks/useRobotData';
import useStrategyRuleSearch from '@/hooks/useStrategyRuleSearch';
import CollapseRender from '@/pages/strategyDetail/FieldRuleConfig/AiRule/CollapseRender';
import {
  addQualityViolationItems,
  addRealTimeQualityViolationItems,
  getGroupTree,
  getLabelList,
  getRiskList,
} from '@/services/ce';
import { customIsEmpty, disableSelectedOptions, formatGroupTree, selectSearch } from '@/utils';
import { useLocation, useSelector } from '@magi/magi';
import { Button, Cascader, Divider, Form, Input, Modal, Select } from 'antd';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import React, {
  forwardRef,
  Fragment,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { TIMEOUT_NUM } from '../index';
import styles from './index.scss';
import RecordItem from './RecordItem';

const changeRealTreeData = (tree?: any[], isRealTime?: boolean): any => {
  if (!isRealTime) return tree;
  return tree
    ?.filter((item: any) => item.qualityType === 3)
    ?.map((item: any) => ({ ...item, children: changeRealTreeData(item.children, isRealTime) }));
};

const ConsultRecord = forwardRef<any, any>((props, ref) => {
  const {
    onChildValue,
    recordLists,
    form,
    setEditBoolData,
    editBoolData,
    sessionId,
    handleGetQualityTaskResult,
    currentTenantNo,
    seatName: seatNameProp,
    isRealTime,
  } = props;
  const {
    labelList: noRedLabelList,
    redLabelList,
    riskList: noRedRiskList,
    redRiskList,
    userInfo,
  } = useSelector((state: { global: any }) => state.global);
  const location: any = useLocation();
  const [editForm] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newGroupList, setNewGroupList] = useState<any[]>([]);
  const [curLabelList, setCurLabelList] = useState<any[]>([]);
  const [curRiskList, setCurRiskList] = useState<any[]>([]);
  const timerRef = useRef<any>();
  const ruleIdList = Form.useWatch(['ruleIdList'], editForm);
  const { isRedLineRobot } = useRobotData();

  const tenantNo = useMemo(
    () => currentTenantNo || location.query.tenantNo,
    [currentTenantNo, location.query.tenantNo]
  );

  const isSomeTenantNo = useMemo(
    () => location.query.tenantNo === currentTenantNo,
    [location.query.tenantNo, currentTenantNo]
  );

  const handleLabelList = useCallback(() => {
    if (!tenantNo || tenantNo === location.query.tenantNo) return;
    getLabelList(tenantNo).then((res: any) => {
      const list = (res.data.value || []).map((item: any) => {
        return {
          ...item,
          value: item.id,
          label: item.labelName,
        };
      });
      setCurLabelList(list || []);
    });
  }, [tenantNo, location.query.tenantNo]);

  const handleRiskList = useCallback(() => {
    if (!tenantNo || tenantNo === location.query.tenantNo) return;
    getRiskList(tenantNo).then((res: any) => {
      const list = (res.data.value || []).map((item: any) => {
        return {
          label: item,
          value: item,
        };
      });
      setCurRiskList(list || []);
    });
  }, [tenantNo, location.query.tenantNo]);

  useEffect(() => {
    handleLabelList();
    handleRiskList();
  }, [handleLabelList, handleRiskList]);

  const openViolationModal = useCallback(
    (line?: string, seatName?: string) => {
      if (line || seatName) {
        const obj: any = {};
        line && (obj.line = line);
        seatName && (obj.seatName = seatName);
        editForm.setFieldsValue(obj);
      }
      setVisible(true);
    },
    [editForm]
  );

  useImperativeHandle(ref, () => ({
    openViolationModal,
  }));

  const handeGroupTree = useCallback(async () => {
    if (isSomeTenantNo) return;
    const res = await getGroupTree(tenantNo);
    if (res.data.success && res.data.value) {
      const { tree } = formatGroupTree(res.data.value.strategyGroupDTOList);
      setNewGroupList(
        (tree || []).filter((item: any) => item.isRedLine === '1').map(({ isRedLine, ...item }: any) => ({ ...item }))
      );
    }
  }, [isSomeTenantNo, tenantNo]);

  const onRuleChange = useCallback(
    (_ruleList?: any[], value?: string) => {
      const item = _ruleList?.find((v: any) => v.value === value);
      const label = item?.labelData
        ?.split(',')
        ?.filter(Boolean)
        ?.map((v: string) => Number(v));
      editForm.setFieldsValue({
        level: item?.riskLevel || undefined,
        label: label?.length ? label : undefined,
        score: item?.score,
      });
    },
    [editForm]
  );

  const { columns, checkGroups, checkStrategys, checkRules, straConfigList, ruleList } = useStrategyRuleSearch({
    tenantNo,
    groupList: isSomeTenantNo ? undefined : newGroupList,
    showColumns: ['rule'],
    form: editForm,
    groupProps: { type: 'cascader' },
    ruleProps: {
      onChange: (value: string) => onRuleChange(ruleList, value),
    },
  });

  const labelList = useMemo(() => {
    if (customIsEmpty(ruleIdList)) return [];
    const isRed = ruleList?.find((item: any) => item.value === ruleIdList)?.isRedLine === '1';
    return isRed ? redLabelList : noRedLabelList;
  }, [noRedLabelList, redLabelList, ruleIdList, ruleList]);

  const riskList = useMemo(() => {
    if (customIsEmpty(ruleIdList)) return [];
    const isRed = ruleList?.find((item: any) => item.value === ruleIdList)?.isRedLine === '1';
    return isRed ? redRiskList : noRedRiskList;
  }, [noRedRiskList, redRiskList, ruleIdList, ruleList]);

  const redstraIds = useMemo(() => {
    if (isSomeTenantNo) return null;
    return (
      straConfigList
        ?.filter((item: any) => newGroupList?.find((group: any) => group.value === item.groupId))
        ?.map((item: any) => item.value) || []
    );
  }, [isSomeTenantNo, straConfigList, newGroupList]);

  useEffect(() => {
    handeGroupTree();
  }, [handeGroupTree]);

  const onCancel = useCallback(() => {
    editForm.resetFields();
    setVisible(false);
  }, [editForm]);

  const refreshData = useCallback(() => {
    return new Promise((resolve) => {
      clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        onCancel();
        setEditBoolData({});
        handleGetQualityTaskResult(true).finally(() => {
          resolve(true);
        });
      }, TIMEOUT_NUM);
    });
  }, [onCancel, handleGetQualityTaskResult, setEditBoolData]);

  useEffect(() => {
    return () => {
      clearTimeout(timerRef.current);
    };
  }, [sessionId]);

  const onSubmit = useCallback(async () => {
    const values = await editForm.validateFields();
    try {
      setLoading(true);
      const params = {
        tenantNo,
        sessionId,
        lines: values.line ? [values.line] : undefined,
        batchNo: recordLists?.[0]?.batchNo,
        strategyId: values.strategyIdList,
        failMessage: values.failMessage,
        ruleId: values.ruleIdList,
        groupId: values.groupIdList?.[values.groupIdList?.length - 1],
        ruleName: checkRules?.[0]?.label,
        strategyName: checkStrategys?.[0]?.label,
        groupName: checkGroups?.find((item) => item.value === values.groupIdList?.[values.groupIdList?.length - 1])
          ?.label,
        seatName: values.seatName,
        level: values.level,
        score: values.score,
        label: values.label?.join(','),
        operator: userInfo?.name || userInfo?.adAccount,
      };
      const res = await (isRealTime ? addRealTimeQualityViolationItems : addQualityViolationItems)(params);
      if (res?.data?.value) {
        try {
          await refreshData();
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  }, [
    editForm,
    tenantNo,
    sessionId,
    recordLists,
    checkRules,
    checkStrategys,
    checkGroups,
    userInfo,
    refreshData,
    isRealTime,
  ]);

  return (
    <>
      <div className={styles.ConsultRecordContainer}>
        {recordLists && recordLists.length
          ? recordLists.map((item: any, index: number) => {
              return (
                <Fragment key={item.id}>
                  {isEmpty(item) ? null : (
                    <RecordItem
                      record={item}
                      isRedLineRobot={isRedLineRobot}
                      curLabelList={curLabelList}
                      curRiskList={curRiskList}
                      editBoolData={editBoolData}
                      setEditBoolData={setEditBoolData}
                      form={form}
                      onChildValue={onChildValue}
                      redstraIds={redstraIds}
                      refreshData={refreshData}
                      isRealTime={isRealTime}
                    />
                  )}
                  {index !== recordLists.length - 1 && <Divider />}
                </Fragment>
              );
            })
          : null}
        <Button
          type="primary"
          style={{ width: '100%', marginTop: 24 }}
          onClick={() => openViolationModal(undefined, seatNameProp)}
        >
          +添加违规项
        </Button>
      </div>
      <Modal
        title={'添加违规项'}
        open={visible}
        onCancel={onCancel}
        width={600}
        onOk={onSubmit}
        confirmLoading={loading}
        okText="提交"
      >
        <Form style={{ marginTop: 24 }} form={editForm}>
          {columns
            .filter((v) => ['groupIdList', 'strategyIdList', 'ruleIdList'].includes(v.name))
            .map(({ name, label, type, ...item }) => (
              <Form.Item key={name} name={name} label={label} rules={[{ required: true, message: `请选择${label}` }]}>
                {type === 'cascader' ? (
                  <Cascader
                    placeholder={`请选择${label}`}
                    {...item}
                    options={name === 'groupIdList' ? changeRealTreeData(item.options, isRealTime) : item.options}
                    fieldNames={{ label: 'title', value: 'value', children: 'children' }}
                    treeCheckable={false}
                    optionRender={(node: any) => (
                      <span className={classNames({ red: node.isRedLine === '1' && !isRedLineRobot })}>
                        {node?.title}
                      </span>
                    )}
                  />
                ) : (
                  <Select
                    placeholder={`请选择${label}`}
                    {...item}
                    {...selectSearch}
                    optionRender={(option) => (
                      <span className={classNames({ red: option?.data?.isRedLine === '1' && !isRedLineRobot })}>
                        {option?.data?.label}
                      </span>
                    )}
                    options={
                      name === 'ruleIdList'
                        ? disableSelectedOptions(
                            item.options,
                            recordLists?.map((item: any) => item.ruleId),
                            true
                          )
                        : item.options?.filter(
                            (v: any) =>
                              (typeof v.status === 'undefined' || v.status === 1) &&
                              (isRealTime && name === 'strategyIdList' ? v.qualityType === 3 : true) // 过滤出实时质检的策略名称
                          )
                    }
                    mode={undefined}
                  />
                )}
              </Form.Item>
            ))}
          <Form.Item name="line" label="行数" hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="seatName" label="违规坐席" hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="failMessage" label="备注" className={styles.ruleRemarkFormItem}>
            <Input.TextArea maxLength={500} showCount placeholder="请输入备注" rows={3} />
          </Form.Item>
          <CollapseRender
            isRedLineRobot={isRedLineRobot}
            labelList={labelList}
            riskList={riskList}
            riskFormName="level"
            className={styles.collapseSet}
          />
        </Form>
      </Modal>
    </>
  );
});

export default memo(ConsultRecord);
