import { antConfig } from '@/utils/constants';
import { Checkbox, Col, ConfigProvider, Popover, Radio, Row, Slider, Switch, Tooltip } from 'antd';
import React, { FC, memo, useEffect, useRef, useState } from 'react';
import NoVolume from '../../../assets/noVolume.svg';
import Pause from '../../../assets/pause.svg';
import QuickBack from '../../../assets/quickBack.svg';
import QuickGo from '../../../assets/quickGo.svg';
import Start from '../../../assets/start.svg';
import Volume from '../../../assets/volume.svg';
import styles from './index.module.scss';

const colorPrimary = antConfig.theme.token.colorPrimary;

const theme = {
  token: {
    colorBgSpotlight: '#fff',
    colorTextLightSolid: '#000B15',
  },
  components: {
    Slider: {
      handleColor: colorPrimary,
      handleActiveColor: colorPrimary,
      handleLineWidth: 1,
      handleLineWidthHover: 1,
      railBg: '#E7E7EB',
      railHoverBg: '#E7E7EB',
      railSize: 3,
      trackBg: colorPrimary,
      trackHoverBg: colorPrimary,
      dotBorderColor: colorPrimary,
      dotActiveBorderColor: colorPrimary,
      dotSize: 8,
      controlSize: 8,
      handleSize: 8,
      handleSizeHover: 8,
    },
  },
};

function formatTime(seconds: number) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(remainingSeconds).padStart(2, '0');
  return `${formattedMinutes}:${formattedSeconds}`;
}

const speeds = [
  {
    label: '0.5X',
    value: 0.5,
  },
  {
    label: '1X',
    value: 1,
  },
  {
    label: '1.25X',
    value: 1.25,
  },
  {
    label: '1.5X',
    value: 1.5,
  },
  {
    label: '2.0X',
    value: 2,
  },
  {
    label: '3.0X',
    value: 3,
  },
];

interface IProps {
  currentTime: number;
  duration: number;
  playing: boolean;
  autoPlay: boolean;
  canAutoPlay: boolean;
  url: string;
  setAutoPlay: (autoPlay: boolean) => void;
  onTimeUpdate?: ({ status, time }: { status: boolean; time: number; follow: boolean }) => void;
  start: () => void;
  pause: () => void;
  getAudio: () => HTMLAudioElement;
  setAudioStats: (key: string, value: any) => void;
}

const PlayActions: FC<IProps> = ({
  currentTime,
  duration,
  playing,
  autoPlay,
  canAutoPlay,
  url,
  setAutoPlay,
  onTimeUpdate,
  start,
  pause,
  getAudio,
  setAudioStats,
}) => {
  const [volume, setVolume] = useState(100); // 音量
  const [follow, setFollow] = useState(true); // 文本自动跟随
  const [speed, setSpeed] = useState(1); // 播放速度
  const [isCustomSpeed, setIsCustomSpeed] = useState(false);
  const volumeRef = useRef(volume || 100);

  useEffect(() => {
    onTimeUpdate?.({ status: playing, time: currentTime, follow });
  }, [playing, currentTime, onTimeUpdate, follow]);

  useEffect(() => {
    if (!getAudio() || !url) return;
    setAudioStats('playbackRate', speed);
  }, [speed, url, getAudio, setAudioStats]);

  return (
    <Row className={styles.actions} gutter={24}>
      <Col className={styles.actionLeft} xl={12} lg={24}>
        <div
          onClick={() => {
            start();
            setAudioStats('currentTime', getAudio()?.currentTime - 15);
          }}
          className={styles.actionIconContainer}
        >
          <img src={QuickBack} className={styles.actionIcon} />
        </div>
        <Tooltip overlayStyle={{ zIndex: 1074 }} title={playing ? '暂停' : '播放'}>
          <div
            onClick={() => {
              !playing ? start() : pause();
            }}
            className={styles.actionIconContainer}
          >
            <img src={playing ? Pause : Start} className={styles.actionIcon} style={{ width: 24, height: 24 }} />
          </div>
        </Tooltip>
        <div
          onClick={() => {
            start();
            setAudioStats('currentTime', getAudio()?.currentTime + 15);
          }}
          className={styles.actionIconContainer}
        >
          <img src={QuickGo} className={styles.actionIcon} />
        </div>
        <div className={styles.audioTime}>
          <span>{formatTime(currentTime)}</span>
          <span> / </span>
          <span>{formatTime(duration)}</span>
        </div>
      </Col>
      <Col className={styles.actionRight} xl={12} lg={24}>
        <Popover
          placement="top"
          overlayClassName={styles.speedPop}
          arrow={false}
          content={
            <>
              <Radio.Group
                disabled={isCustomSpeed}
                block
                optionType="button"
                options={speeds}
                onChange={(e) => setSpeed(e.target.value)}
                value={speed}
              />
              <ConfigProvider theme={theme}>
                <Checkbox
                  onChange={(e) => {
                    if (!e.target.checked && !speeds?.find(({ value }) => speed === value)) {
                      const ls = speeds.filter(({ value }) => value <= speed);
                      setSpeed(ls[ls.length - 1].value ?? 1);
                    }
                    setIsCustomSpeed(e.target.checked);
                  }}
                  checked={isCustomSpeed}
                >
                  自定义
                </Checkbox>
                <Slider
                  tooltip={{
                    formatter(value) {
                      return `${value}X`;
                    },
                  }}
                  min={0.5}
                  max={3}
                  step={0.1}
                  value={speed}
                  disabled={!isCustomSpeed}
                  onChange={(e) => setSpeed(e)}
                />
              </ConfigProvider>
            </>
          }
        >
          <span className={styles.speed}>倍数</span>
        </Popover>
        <Popover
          placement="top"
          overlayClassName={styles.volumePop}
          arrow={false}
          content={
            <div className={styles.volume}>
              <span>{volume}</span>
              <ConfigProvider theme={theme}>
                <Slider
                  vertical
                  value={volume}
                  onChange={(e) => {
                    setVolume(e);
                    volumeRef.current = e;
                    setAudioStats('volume', e / 100);
                  }}
                  tooltip={{
                    formatter(value) {
                      return `${value}%`;
                    },
                  }}
                  min={0}
                  max={100}
                  step={1}
                />
              </ConfigProvider>
            </div>
          }
        >
          <img
            className={styles.volumeIcon}
            src={volume === 0 ? NoVolume : Volume}
            onClick={() => {
              setVolume((preState) => {
                setAudioStats('volume', (preState === 0 ? volumeRef.current : 0) / 100);
                return preState === 0 ? volumeRef.current : 0;
              });
            }}
          />
        </Popover>
        {canAutoPlay && (
          <>
            <span style={{ marginRight: 8, fontSize: 12, color: '#4E5969' }}>自动播放</span>
            <Switch
              style={{ marginRight: 18 }}
              checked={autoPlay}
              size="small"
              onChange={(e) => {
                e && start();
                localStorage.setItem('playerAutoPlay', String(e));
                setAutoPlay(e);
              }}
            />
          </>
        )}
        <span style={{ marginRight: 8, fontSize: 12, color: '#4E5969' }}>文本跟随</span>
        <Switch checked={follow} onChange={(e) => setFollow(e)} size="small" />
      </Col>
    </Row>
  );
};

export default memo(PlayActions);
