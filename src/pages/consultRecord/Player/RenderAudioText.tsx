import ClientAvatar from '@/assets/clientAvatar.svg';
import { ReactComponent as DeskAvatar } from '@/assets/deskAvatar.svg';
import { avatorColor } from '@/pages/consultRecord/ChatHistory/utils/ui';
import { ConfigProvider, Tooltip } from 'antd';
import React, { FC, memo, useCallback, useMemo } from 'react';
import { AudioTextType } from '../ChatHistory/services/types/manualQuality';
import styles from './index.module.scss';

interface IProps {
  duration: number;
  list: AudioTextType[];
  onVolumeChange: (time: number) => void;
}

const RenderAudioText: FC<IProps> = ({ list, duration, onVolumeChange }) => {
  const client = useMemo(() => list?.filter((item) => item.side === 1) || [], [list]);
  const desk = useMemo(() => list?.filter((item) => item.side === 2) || [], [list]);

  const getPercent = useCallback(
    (arr: AudioTextType[]) => {
      const total = arr.reduce((acc, cur) => {
        return acc + cur.endTimeOffset - cur.beginTimeOffset;
      }, 0);
      return `${Math.round((total / 1000 / duration) * 100)}%`;
    },
    [duration]
  );

  const renderTextLine = useCallback(
    (textList: AudioTextType[], side: number) => {
      return textList?.map((item) => {
        let backgroundColor = '';
        if (side === 1) {
          backgroundColor = '#27AE60';
        }
        if (side === 2) {
          backgroundColor = '#2D92F3';
        }
        if (!!item.isHit) {
          backgroundColor = 'rgba(255, 138, 2, 1)';
        }
        return (
          <ConfigProvider
            key={item.msgId}
            theme={{
              token: {
                colorBgSpotlight: 'rgba(255, 138, 2, 1)',
                colorTextLightSolid: '#fff',
              },
            }}
          >
            <Tooltip title={item.isHit}>
              <div
                className={styles.audioTextLine}
                onClick={() => {
                  if (!item.isHit) return;
                  const time = (item.beginTimeOffset || 0) / 1000;
                  onVolumeChange(time);
                }}
                style={{
                  width: `${((item.endTimeOffset - item.beginTimeOffset) / 1000 / duration) * 100}%`,
                  left: `${(item.beginTimeOffset / 1000 / duration) * 100}%`,
                  backgroundColor,
                  cursor: !!item.isHit ? 'pointer' : 'default',
                }}
              />
            </Tooltip>
          </ConfigProvider>
        );
      });
    },
    [duration, onVolumeChange]
  );

  return (
    <>
      <div className={styles.audioText}>
        <img src={ClientAvatar} />
        <div className={styles.audioTextContent}>
          <div style={{ width: '75px' }}>
            客户：<span style={{ color: '#999' }}>{getPercent(client)}</span>
          </div>
          <div className={styles.background}>{renderTextLine(client, 1)}</div>
        </div>
      </div>
      <div className={styles.audioText}>
        <DeskAvatar style={{ color: avatorColor[0] }} />
        <div className={styles.audioTextContent}>
          <div style={{ width: '75px' }}>
            坐席：<span style={{ color: '#999' }}>{getPercent(desk)}</span>
          </div>
          <div className={styles.background}>{renderTextLine(desk, 2)}</div>
        </div>
      </div>
    </>
  );
};

export default memo(RenderAudioText);
