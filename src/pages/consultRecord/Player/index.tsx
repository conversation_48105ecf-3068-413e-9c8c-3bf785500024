import usePlayerUrl from '@/hooks/usePlayerUrl';
import { useHistory } from '@magi/magi';
import { message } from 'antd';
import React, { forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import Back from '../../../assets/back.svg';
import { AudioTextType } from '../ChatHistory/services/types/manualQuality';
import styles from './index.module.scss';
import PlayActions from './PlayActions';
import PlayAudioBox from './PlayAudioBox';
import RenderAudioText from './RenderAudioText';

interface IProps {
  url: string;
  onTimeUpdate?: ({ status, time }: { status: boolean; time: number; follow: boolean }) => void;
  currentSessionID: string;
  list?: AudioTextType[];
  goBack?: () => void;
  isIframe?: boolean;
  onPlayerChange?: () => void;
  isContainer?: boolean;
  isService?: boolean;
  ControlsRender?: React.ReactNode;
}

const Player = forwardRef((props: IProps, ref) => {
  const {
    url: playerUrl,
    onTimeUpdate,
    currentSessionID,
    list = [],
    goBack,
    isIframe,
    onPlayerChange,
    isContainer,
    isService,
    ControlsRender,
  } = props;
  const history = useHistory();
  const { playerUrl: url, setPlayerMode, isPlayerFinish } = usePlayerUrl({ src: playerUrl });
  const [playing, setPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0); // 当前播放的时间
  const audioRef = useRef<any>();
  const [duration, setDuration] = useState(0);
  const [autoPlay, setAutoPlay] = useState(false);
  const [canAutoPlay, setCanAutoPlay] = useState(true);

  const handleTimeUpdate = useCallback((e: any) => {
    setCurrentTime(e.target.currentTime);
  }, []);

  const onStart = useCallback((init?: boolean) => {
    const duration = audioRef.current.duration;
    setCurrentTime((preState) => {
      if (preState >= duration) {
        audioRef.current.currentTime = 0;
        return 0;
      }
      return preState;
    });
    setPlaying(true);
    return audioRef.current
      .play()
      .then(() => {
        init && setCanAutoPlay(true);
      })
      .catch((error: string) => {
        console.log('player autoplay error:', error);
        if (init) {
          setCanAutoPlay(false);
          setPlaying(false);
        }
      });
  }, []);

  const start = useCallback(
    async (init?: boolean) => {
      await onStart(init);
      onPlayerChange?.();
    },
    [onStart, onPlayerChange]
  );

  const onPause = useCallback(() => {
    setPlaying(false);
    audioRef.current.pause();
  }, []);

  const pause = useCallback(() => {
    onPause();
    onPlayerChange?.();
  }, [onPause, onPlayerChange]);

  useEffect(() => {
    // 独立打开页面时，判断是否支持自动播放
    const bool = localStorage.getItem('playerAutoPlay') === 'true';
    setAutoPlay(bool);
    let timer: any;
    if (url && !goBack) {
      timer = setTimeout(() => {
        start(true).then(() => {
          !bool && pause();
        });
      }, 300);
    }
    return () => {
      clearTimeout(timer);
    };
  }, [url]);

  const onLoadedMetadata = useCallback((e: any) => {
    const duration = e.target.duration;
    setDuration(duration);
  }, []);

  const onEnded = useCallback(() => {
    audioRef.current.currentTime = 0;
  }, []);

  const onError = useCallback(() => {
    setPlayerMode();
    isPlayerFinish && message.error('音频加载失败');
  }, [setPlayerMode, isPlayerFinish]);

  const onPlaySilderChange = useCallback(
    (e) => {
      setCurrentTime(e);
      audioRef.current.currentTime = e;
      start();
    },
    [start]
  );

  const onVolumeChange = useCallback(
    (time: number) => {
      setCurrentTime(time);
      audioRef.current.currentTime = time;
      start();
    },
    [start]
  );

  const getAudio = useCallback(() => audioRef.current, []);

  const setAudioStats = useCallback((key, value) => {
    if (!audioRef.current) return;
    audioRef.current[key] = value;
  }, []);

  const initPlayer = useCallback(() => {
    pause();
    audioRef.current.currentTime = 0;
    setCurrentTime(0);
  }, [pause]);

  useImperativeHandle(ref, () => ({
    onCurrentTimePlay: (beginTimeOffset: number) => {
      const time = (beginTimeOffset || 0) / 1000;
      setCurrentTime(time);
      audioRef.current.currentTime = time;
      return onStart();
    },
    onPause,
    initPlayer,
  }));

  return (
    <div
      className={styles.container}
      style={isIframe ? { marginLeft: 0, marginRight: 0, width: 'calc(100% - 32px)' } : {}}
    >
      {!isService && (
        <div className={styles.title}>
          {(!isIframe || isContainer) && (
            <img
              onClick={() => (goBack ? goBack() : history.goBack())}
              style={{ marginRight: 10, cursor: 'pointer' }}
              src={Back}
            />
          )}
          语音ID：{currentSessionID}
          {(!isIframe || isContainer) && ControlsRender}
        </div>
      )}
      <PlayAudioBox
        ref={audioRef}
        currentTime={currentTime}
        duration={duration}
        url={url}
        autoPlay={autoPlay}
        onLoadedMetadata={onLoadedMetadata}
        setPlaying={setPlaying}
        handleTimeUpdate={handleTimeUpdate}
        onEnded={onEnded}
        onError={onError}
        onChange={onPlaySilderChange}
      />
      <PlayActions
        currentTime={currentTime}
        duration={duration}
        playing={playing}
        autoPlay={autoPlay}
        canAutoPlay={canAutoPlay}
        url={url}
        setAutoPlay={setAutoPlay}
        onTimeUpdate={onTimeUpdate}
        start={start}
        pause={pause}
        getAudio={getAudio}
        setAudioStats={setAudioStats}
      />
      {!!duration && <RenderAudioText duration={duration} list={list} onVolumeChange={onVolumeChange} />}
    </div>
  );
});

export default memo(Player);
