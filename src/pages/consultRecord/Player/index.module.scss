.container {
  border-radius: 8px;
  background-color: #fff;
  margin-left: 20px;
  margin-right: 20px;
  width: calc(100% - 40px - 32px);
  padding: 10px 16px;
  margin-bottom: 8px;
  color: #1d2129;
  :global {
    .ant-switch.ant-switch-checked {
      background: #a28ff5;
    }
    .ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
      background: #a28ff5;
    }
  }
  .title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  .actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eff1f4;
    box-sizing: border-box;
    .actionLeft,
    .actionRight {
      display: flex;
      align-items: center;
    }
    @media (min-width: 1200px) {
      .actionRight {
        justify-content: flex-end;
      }
    }
    .actionIconContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px;
      border-radius: 4px;
      cursor: pointer;
    }
    .actionIcon {
      width: 16px;
      height: 16px;
    }
    .audioTime {
      font-size: 12px;
      color: #4e5969;
      font-weight: 500;
      margin-left: 16px;
      margin-right: 16px;
    }
  }
  .speed {
    margin-right: 16px;
    padding: 4px 8px;
    font-size: 12px;
    color: #4e5969;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background: #f2f2fe;
    }
  }

  .progress {
    width: 100%;
    :global {
      .ant-slider-rail {
        border-radius: 8px;
      }
      .ant-slider-track {
        border-radius: 8px;
      }
      .ant-slider-horizontal {
        margin-bottom: 4px;
      }
      .ant-slider {
        margin: 0 0 0 8px;
        &:hover {
          .ant-slider-handle {
            &::after {
              box-shadow: 0 0 0 1px #7052f0;
            }
          }
        }
      }
    }
  }
  .line {
    background-color: var(--primary-color);
    height: 2px;
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  .currentProgress {
    background-color: #000;
    width: 2px;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    cursor: ew-resize;
  }
  .audioText {
    margin-top: 5px;
    display: flex;
    align-items: center;
    img,
    svg {
      width: 18px;
      height: 18px;
    }
    .background {
      width: 100%;
      border-radius: 6px;
      height: 4px;
      background-color: #eff1f4;
      position: relative;
      flex: 1;
    }
    .audioTextLine {
      height: 100%;
      position: absolute;
      border-radius: 8px;
    }
  }
  .audioTextContent {
    margin-left: 8px;
    flex: 1;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

.speedPop {
  :global {
    .ant-popover-inner {
      padding: 8px !important;
      .ant-radio-group {
        .ant-radio-button-wrapper {
          &::before {
            display: none;
          }
          border: none;
          height: 18px;
          width: 42px;
          padding: 0;
          line-height: 18px;
          font-size: 12px;
          font-weight: 400;
          color: #1d2129;
          &.ant-radio-button-wrapper-disabled {
            background: none;
            color: #c9cdd4 !important;
          }
          &.ant-radio-button-wrapper-checked {
            color: var(--primary-color);
          }
        }
      }
      .ant-checkbox-wrapper {
        margin-top: 10px;
        padding-left: 6px;
        font-size: 12px;
        color: #181b25;
      }
      .ant-slider {
        margin: 4px 10px 0;
        .ant-slider-handle {
          &::after {
            background: var(--primary-color);
          }
        }
        &.ant-slider-disabled {
          .ant-slider-handle {
            &::after {
              background: #babec4;
              box-shadow: 0 0 0 1px #babec4;
            }
          }
          .ant-slider-rail {
            background: #eff1f4 !important;
          }
          .ant-slider-track {
            background: #e7e7eb !important;
          }
        }
      }
    }
  }
}
.volumePop {
  :global {
    .ant-popover-inner {
      min-width: 21px;
      padding: 4px 5px 10px !important;
    }
  }
}

.volume {
  min-width: 16px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  & > span {
    color: #4e5969;
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 6px;
    text-align: center;
  }
  :global {
    .ant-slider {
      flex: 1;
      margin: 0 !important;
      .ant-slider-handle {
        &::after {
          background: var(--primary-color);
        }
      }
    }
  }
}
.volumeIcon {
  cursor: pointer;
  margin-right: 18px;
  padding: 4px;
  border-radius: 4px;
  &:hover {
    background: #f2f2fe;
  }
}
