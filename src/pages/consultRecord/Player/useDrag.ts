import { useEffect, useRef } from 'react';

const useDrag = (props: any) => {
  const { ref, handleClickProgress } = props;
  const down = useRef(false);

  useEffect(() => {
    const dom = ref.current;

    if (!dom) {
      return;
    }
    const mousedown = (e: any) => {
      if (e.target === dom) {
        e.stopPropagation();
        down.current = true;
      }
    };
    const mousemove = (e: any) => {
      if (!down.current) {
        return;
      }
      e.stopPropagation();
      dom.style.left = `${e.pageX - 90}px`; // 减去侧边栏的宽度
    };
    const mouseup = (e: any) => {
      if (down.current) {
        e.stopPropagation();
        down.current = false;
        handleClickProgress(e);
      }
    };
    document.addEventListener('mousedown', mousedown);
    document.addEventListener('mousemove', mousemove);
    document.addEventListener('mouseup', mouseup);
    return () => {
      document.removeEventListener('mousedown', mousedown);
      document.removeEventListener('mousemove', mousemove);
      document.removeEventListener('mouseup', mouseup);
    };
  }, [ref]);

  return {
    down,
  };
};

export default useDrag;
