import { Line, Mix } from '@/components/Charts';
import { isDateFormat } from '@/components/Charts/Line';
import Form, { useForm } from '@/components/Form';
import PageLayout from '@/components/PageLayout';
import useGetRuleNames from '@/hooks/useGetRuleNames';
import { initialValues } from '@/hooks/useStrategyRuleSearch';
import { queryMetricCycles, queryReportTrendMonitoring } from '@/services/ce';
import { queryRedLineReportTrendMonitoring } from '@/services/redLine';
import { useLocation } from '@magi/magi';
import { Divider, Radio, Segmented, Select, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.scss';

const monitoringTypeOptions = [
  { label: '商机量', value: 'serviceId' },
  { label: '会话量', value: 'sessionId' },
  { label: '规则', value: 'ruleId' },
  { label: '违规率', value: 'violationRate' },
  { label: '商机违规量', value: 'serviceIdViolation' },
  { label: '商机违规率', value: 'serviceIdViolationRate' },
  { label: '策略', value: 'strategyId' },
  { label: '策略分组', value: 'groupId' },
  { label: '规则精确率', value: 'ruleAccuracy' },
  { label: '标注精确率', value: 'ruleTagAccuracy' },
];

const redLineMonitoringTypeOptions = [
  { label: '商机量', value: 'serviceId' },
  { label: '会话量', value: 'sessionId' },
  { label: '策略', value: 'strategyId' },
  { label: '策略分组', value: 'groupId' },
  { label: '违规情况', value: 'serviceIdViolationRate', isMix: true },
  { label: '回访后违规情况', value: 'returnVisitServiceIdViolationRate', isMix: true },
  { label: '标注情况', value: 'ruleAccuracy', isMix: true },
];

const percentageOptions = [
  'violationRate',
  'ruleAccuracy',
  'ruleTagAccuracy',
  'serviceIdViolationRate',
  'returnVisitServiceIdViolationRate',
];

export const offsetOptions = [
  { label: '周一 ~ 周日', value: 0 },
  { label: '周二 ~ 周一', value: 1 },
  { label: '周三 ~ 周二', value: 2 },
  { label: '周四 ~ 周三', value: 3 },
  { label: '周五 ~ 周四', value: 4 },
  { label: '周六 ~ 周五', value: 5 },
  { label: '周日 ~ 周六', value: 6 },
];

interface IProps {
  isRedLine?: string;
}

const Tendency: FC<IProps> = ({ isRedLine }) => {
  const location: any = useLocation();
  const { tenantNo: currentTenantNo } = location.query;
  const [form] = useForm();
  const { strategyRuleSearchList, ruleNames, setSearchData } = useGetRuleNames({
    form,
    currentTenantNo,
    showColumns: ['time', 'rule', 'session'],
  });
  const [cyclesOptions, setCyclesOptions] = useState([]);
  const [metricCycle, setMetricCycle] = useState('');
  const [monitoringType, setMonitoringType] = useState<string>('');
  const [cyclesLoading, setCyclesLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [lineData, setLineData] = useState<any[]>([]);
  const [offset, setOffset] = useState(0);
  const sessionTypesRef = useRef<string[]>();

  const monitoringOptions = useMemo(() => {
    return isRedLine === '1'
      ? redLineMonitoringTypeOptions.filter((v) =>
          ruleNames?.split('、')?.length === 1 ? true : v.value !== 'ruleAccuracy'
        )
      : monitoringTypeOptions;
  }, [isRedLine, ruleNames]);

  useEffect(() => {
    setMonitoringType((preState) => {
      if (monitoringOptions?.find(({ value }: { value: string }) => value === preState)) {
        return preState;
      }
      return monitoringOptions[0].value;
    });
  }, [JSON.stringify(monitoringOptions)]);

  const getTimeData = useCallback(() => {
    let [startTime, endTime] = form.getFieldValue('time') || initialValues.time;
    const timeType = form.getFieldValue('timeType') || initialValues.timeType;
    startTime && (startTime = `${dayjs(startTime)?.format('YYYY-MM-DD')} 00:00:00`);
    endTime && (endTime = `${dayjs(endTime)?.format('YYYY-MM-DD')} 23:59:59`);
    return timeType === '2'
      ? {
          qualityStartTime: startTime && Date.parse(dayjs(startTime) as any),
          qualityEndTime: endTime && Date.parse(dayjs(endTime) as any),
        }
      : { startTime, endTime };
  }, [form]);

  const handleQueryMetricCycles = useCallback(
    async (cb?: (bool: boolean) => void) => {
      const timeData = getTimeData();
      if (!(timeData.startTime && timeData.endTime) && !(timeData.qualityStartTime && timeData.qualityEndTime)) return;
      try {
        setCyclesLoading(true);
        const res = await queryMetricCycles({ tenantNo: currentTenantNo, ...timeData });
        const data = res?.data?.value || [];
        setCyclesOptions(data);
        setMetricCycle((preState) => {
          const val = data.filter(({ disabled }: { disabled: boolean }) => !disabled);
          const firstValue = val?.[0]?.value;
          cb?.(preState === firstValue);
          if (val?.find(({ value }: { value: string }) => value === preState)) return preState;
          firstValue === 'week' && setOffset(0);
          return firstValue;
        });
      } finally {
        setCyclesLoading(false);
      }
    },
    [getTimeData, currentTenantNo]
  );

  useEffect(() => {
    handleQueryMetricCycles();
  }, [handleQueryMetricCycles]);

  const hanldeQueryReportTrendMonitoring = useCallback(async () => {
    if (!metricCycle || !monitoringType) return;
    const isRed = isRedLine === '1';
    const values = form.getFieldsValue();
    setSearchData(values);
    const { time, timeType, ...formData } = values;
    const timeData = getTimeData();
    try {
      setLoading(true);
      const params = {
        tenantNo: isRed ? undefined : currentTenantNo,
        metricCycle,
        monitoringType,
        ...timeData,
        ...formData,
      };
      metricCycle === 'week' && (params.offset = offset);
      const res = await (isRed ? queryRedLineReportTrendMonitoring(params) : queryReportTrendMonitoring(params));
      if (res?.data?.success) {
        const { list, businessList, sessionCount, businessCount: businessCountList } = res?.data?.value || {};
        const data: any[] = [];
        if (['sessionId', 'serviceId'].includes(monitoringType)) {
          sessionTypesRef.current = formData.sessionTypes;
          const list = monitoringType === 'serviceId' ? businessCountList : sessionCount;
          list?.forEach(({ keyAsString, docCount, sessionType, tenantNoName, tenantNo }: any) => {
            data.push({
              date: keyAsString,
              key: (sessionTypesRef.current?.length ? sessionType : monitoringType) + (tenantNo || ''),
              type: isRed
                ? sessionTypesRef.current?.length
                  ? `${sessionType}-${tenantNoName}`
                  : tenantNoName
                : sessionTypesRef.current?.length
                  ? sessionType
                  : keyAsString,
              value: docCount,
            });
          });
        } else {
          const dataList = [
            'serviceIdViolation',
            'serviceIdViolationRate',
            'returnVisitServiceIdViolationRate',
          ].includes(monitoringType)
            ? businessList
            : list;
          dataList?.slice(0, 15)?.forEach(({ buckets, name, key, sessionType, tenantNoName, tenantNo }: any) => {
            buckets?.forEach(
              ({
                keyAsString,
                docCount,
                accuracy,
                accuracyTag,
                molecule,
                denominator,
                moleculeTag,
                denominatorTag,
                violationRate,
                sessionCount,
                businessViolationRate,
                businessCount,
              }: any) => {
                let val = docCount,
                  _count,
                  _scale,
                  _countScale,
                  _molecule,
                  _denominator;
                let typeName = {};
                ['serviceIdViolationRate', 'returnVisitServiceIdViolationRate'].includes(monitoringType) &&
                  (typeName = { name: '商机违规量', rateName: '商机违规率' });
                monitoringType === 'ruleAccuracy' && (typeName = { name: '标注精确率', rateName: '规则精确率' });
                if (monitoringType === percentageOptions[0]) {
                  // 违规率
                  val = Number(violationRate?.replace('%', '') || 0);
                  _count = docCount;
                  _molecule = docCount;
                  _denominator = sessionCount;
                } else if (monitoringType === percentageOptions[1]) {
                  // 规则精确率
                  val = Number(accuracy?.replace('%', '') || 0);
                  _scale = `${molecule ?? 0}/${denominator ?? 0}`;
                  _count = Number(accuracyTag?.replace('%', '') || 0);
                  _countScale = `${moleculeTag ?? 0}/${denominatorTag ?? 0}`;
                  _molecule = molecule;
                  _denominator = denominator;
                } else if (monitoringType === percentageOptions[2]) {
                  // 标注精确率
                  val = Number(accuracyTag?.replace('%', '') || 0);
                  _count = moleculeTag;
                  _molecule = moleculeTag;
                  _denominator = denominatorTag;
                } else if (monitoringType === percentageOptions[3] || monitoringType === percentageOptions[4]) {
                  // 商机违规率
                  val = Number(businessViolationRate?.replace('%', '') || 0);
                  _scale = `${docCount ?? 0}/${businessCount ?? 0}`;
                  _count = docCount;
                  _molecule = docCount;
                  _denominator = businessCount;
                }
                data.push({
                  typeName: isRed ? typeName : {},
                  date: keyAsString,
                  key: (key || '') + (sessionType || '') + (tenantNo || ''),
                  type: sessionType ? `${sessionType}-${tenantNoName || name}` : tenantNoName || name,
                  value: val,
                  _count,
                  _scale,
                  _countScale,
                  molecule: _molecule,
                  denominator: _denominator,
                });
              }
            );
          });
        }
        setLineData(!isDateFormat(data?.[0]?.date) ? data : data?.sort((a, b) => (a.date > b.date ? 1 : -1)));
      }
    } finally {
      setLoading(false);
    }
  }, [metricCycle, currentTenantNo, getTimeData, monitoringType, offset, form, isRedLine, setSearchData]);

  useEffect(() => {
    hanldeQueryReportTrendMonitoring();
  }, [hanldeQueryReportTrendMonitoring]);

  const queryList = useCallback(() => {
    handleQueryMetricCycles((isCycleChange) => {
      isCycleChange && hanldeQueryReportTrendMonitoring();
    });
  }, [handleQueryMetricCycles, hanldeQueryReportTrendMonitoring]);

  const config = useMemo(() => {
    const getValue = (val?: number) => (percentageOptions.includes(monitoringType) ? (val || 0) + '%' : val || 0);
    const getScale = (item?: any) => {
      if (!percentageOptions.includes(monitoringType)) return '';
      const { molecule, denominator } =
        lineData?.find(({ key, date }) => {
          const newDate = isDateFormat(date) ? dayjs(date).valueOf()?.toString() : date;
          return key === item?.key && newDate === item?.date;
        }) || {};
      return `(${molecule ?? 0}/${denominator ?? 0})`;
    };
    const hasSessionTypes = sessionTypesRef.current && sessionTypesRef.current.length > 0;
    const isSpecial = isRedLine !== '1' && ['sessionId', 'serviceId'].includes(monitoringType);
    return {
      data: lineData,
      xField: 'date',
      yField: 'value',
      seriesField: 'key',
      legend: isSpecial && !hasSessionTypes ? false : {},
      tooltip: {
        showTitle: !(isSpecial && !hasSessionTypes),
        formatter: (datum: any) => {
          return {
            name:
              isSpecial && !hasSessionTypes
                ? isNaN(Number(datum.date))
                  ? datum.date
                  : dayjs(Number(datum.date)).format('YYYY-MM-DD')
                : lineData?.find(({ key }) => key === datum.key)?.type,
            value: getValue(datum.value) + getScale(datum),
          };
        },
      },
      meta: {
        value: {
          formatter: (v: number) => getValue(v),
        },
      },
    };
  }, [lineData, monitoringType, isRedLine]);

  const mixConfig = useMemo(() => {
    const { data, tooltip, legend, meta, ...extraConfig } = config;
    return {
      data,
      tooltip: {
        ...(tooltip || {}),
        customContent: (title: string, items: any[]) => {
          return `
            <div class="g2-tooltip-title">${title}</div>
            <ul class="g2-tooltip-list">
              ${items
                ?.map(
                  (item: any) => `
                <li class="g2-tooltip-list-item">
                  <span class="g2-tooltip-marker" style="background-color: ${item.color};"></span>
                  <span class="g2-tooltip-name">${item.name}:</span>
                  <span class="g2-tooltip-value">${item.value}${item.value?.includes('%') ? `(${item.name?.indexOf(item?.data?.typeName?.rateName) !== -1 ? item?.data?._scale || '' : item?.data?._countScale || ''})` : ''}</span>
                </li>
              `
                )
                .join('')}
            </ul>
          `;
        },
      },
      columnConfig: {
        data,
        xField: 'date',
        yField: '_count',
        seriesField: 'key',
        isGroup: true,
        dodgePadding: 2,
        meta: {
          key: {
            formatter: (v: string) => {
              const data = lineData?.find(({ key }) => key === v);
              return `${data?.type}-${data?.typeName?.name}`;
            },
          },
          _count: {
            formatter: (v: number) => v + (monitoringType === 'ruleAccuracy' ? '%' : ''),
          },
          ...meta,
        },
      },
      lineConfig: {
        data,
        meta: {
          key: {
            formatter: (v: string) => {
              const data = lineData?.find(({ key }) => key === v);
              return `${data?.type}-${data?.typeName?.rateName}`;
            },
          },
          ...meta,
        },
        ...extraConfig,
      },
      legend: true,
    };
  }, [config, lineData, monitoringType]);

  return (
    <PageLayout>
      <div className={styles.container}>
        <Form
          initialValues={initialValues}
          form={form}
          columns={strategyRuleSearchList}
          onQuery={queryList}
          onReset={queryList}
        />
        <Divider style={{ margin: '0 0 16px' }} />
        {isRedLine === '1' && (
          <Typography.Paragraph className={styles.desc} ellipsis={{ rows: 2, tooltip: ruleNames }}>
            当前选中规则：{ruleNames || '-'}
          </Typography.Paragraph>
        )}
        <div className={styles.operateBox}>
          <Radio.Group
            className={styles.radioGroup}
            optionType="button"
            options={monitoringOptions}
            value={monitoringType}
            onChange={(e) => setMonitoringType(e.target.value)}
          />
          <Spin spinning={cyclesLoading}>
            {metricCycle === 'week' && (
              <Select options={offsetOptions} value={offset} onChange={setOffset} style={{ marginRight: 10 }} />
            )}
            <Segmented
              className={styles.segmented}
              options={cyclesOptions}
              value={metricCycle}
              onChange={setMetricCycle}
            />
          </Spin>
        </div>
        <Spin spinning={loading}>
          {(monitoringOptions.find(({ value }) => value === monitoringType) as any)?.isMix ? (
            <Mix {...mixConfig} descName="type" />
          ) : (
            <Line autoFit {...config} descName="type" />
          )}
        </Spin>
      </div>
    </PageLayout>
  );
};

export default Tendency;
