.container {
  padding: 20px;

  .operateBox {
    margin: 16px 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .desc {
    font-size: 14px;
    color: #4e5969;
    margin: 16px 0 0 !important;
    word-break: break-all;
  }

  .radioGroup {
    :global {
      .ant-radio-button-wrapper {
        border: none;
        font-size: 14px;
        min-width: 88px;
        padding: 0 10px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 100px;
        color: #4e5969;
        font-weight: normal;

        &::before {
          display: none;
        }

        &.ant-radio-button-wrapper-checked,
        &:hover {
          background: #f2f2fe;
          color: var(--primary-color);
          font-weight: 500;
        }
      }
    }
  }

  .segmented {
    padding: 4px;
    background: #eff1f4;

    :global {
      .ant-segmented-item {
        color: #86909c;

        .ant-segmented-item-label {
          height: 30px;
          line-height: 30px;
          padding: 0 8px;
        }

        &.ant-segmented-item-selected {
          font-weight: 500;
          color: #1d2129;
          box-shadow: 0px 1px 2px 0px #d0d0d0;
        }
      }
    }
  }
}
