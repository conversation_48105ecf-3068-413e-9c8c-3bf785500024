import { Pagination } from '@/components/EasyTable';
import PageLayout from '@/components/PageLayout';
import TableRender from '@/components/TableRender';
import { deleteStrategyTemplate, getStrategyTemplateList, getStrategyTemplateNameList } from '@/services/ce';
import { <PERSON><PERSON>, Popconfirm, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import StrategyDetailDrawer from '../strategyDetail/StrategyDetailDrawer';
import getSchema from './schema';

const StrategyTemplate = () => {
  const [strategyList, setStrategyList] = React.useState([]);
  const tableRef = useRef<any>(null);
  const [currentDetail, setCurrentDetail] = useState<any>({});
  const [pagination, setPagination] = useState<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const handleSearch = async ({ current, pageSize, ...restParams }: any) => {
    const res = await getStrategyTemplateList({
      ...restParams,
      pageSize,
      pageNum: current,
    });
    return {
      data: res?.data?.value?.list,
      total: res?.data?.value?.total,
    };
  };

  useEffect(() => {
    getStrategyTemplateNameList().then((res: any) => {
      setStrategyList(res.data.value || []);
    });
  }, []);

  const handleDelete = async (id: number) => {
    await deleteStrategyTemplate(id);
    await tableRef.current.refresh();
  };

  const create = () => {
    const obj = { isTemplate: true };
    setCurrentDetail(obj);
  };

  const columns = useMemo(() => {
    return [
      {
        title: '策略名称',
        dataIndex: 'strategyName',
        key: 'strategyName',
      },
      {
        title: '关联规则显示名称',
        dataIndex: 'strategyRuleName',
        key: 'strategyRuleName',
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreated',
        key: 'gmtCreated',
        render: (text: any) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
      },
      {
        title: '更新人',
        dataIndex: 'modifier',
        key: 'modifier',
      },
      {
        title: '更新时间',
        dataIndex: 'gmtModified',
        key: 'gmtModified',
        render: (text: any) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        render: (_: any, record: any) => {
          return (
            <Row>
              <Button
                onClick={() => {
                  setCurrentDetail({ id: record.id });
                }}
                style={{ marginRight: 20, padding: 0 }}
                type="link"
              >
                编辑
              </Button>
              <Popconfirm title="确认删除吗？" onConfirm={async () => await handleDelete(record.id)}>
                <Button type="link" danger style={{ padding: 0 }}>
                  删除
                </Button>
              </Popconfirm>
            </Row>
          );
        },
      },
    ];
  }, []);

  return (
    <PageLayout>
      <TableRender
        ref={tableRef}
        pageData={pagination}
        setPageData={setPagination}
        search={{ schema: getSchema(strategyList) }}
        request={async (params) => {
          return await handleSearch({
            ...pagination,
            ...params,
          });
        }}
        columns={columns as any}
        toolbarAction={false}
        style={{ marginTop: -32 }}
        title={
          <Button onClick={create} type="primary" ghost>
            新建策略模版
          </Button>
        }
      />
      <StrategyDetailDrawer
        currentDetail={currentDetail}
        setCurrentDetail={setCurrentDetail}
        refresh={() => tableRef.current.refresh()}
        isTemplate
        title={currentDetail.id ? '编辑策略模版' : '新建策略模版'}
      />
    </PageLayout>
  );
};

export default StrategyTemplate;
