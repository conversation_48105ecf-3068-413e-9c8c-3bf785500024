import { getAllTreeList, getFirstLevelKeys } from '@/utils';
import { TreeSelect } from 'antd';
import classNames from 'classnames';
import React from 'react';

const { SHOW_CHILD } = TreeSelect;

const getSchema = (groupIdList = []) => ({
  type: 'object',
  labelWidth: 90,
  properties: {
    idList: {
      title: '策略名称',
      type: 'string',
      widget: 'treeSelect',
      span: 8,
      placeholder: '请选择策略名称',
      props: {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        maxTagCount: 'responsive',
        treeData: getAllTreeList(groupIdList, { titleKey: 'strategyName', valueKey: 'id' }) || [],
        treeDefaultExpandedKeys: getFirstLevelKeys(groupIdList, { titleKey: 'strategyName', valueKey: 'id' }),
        fieldNames: { value: 'id', label: 'strategyName', children: 'children' },
        treeTitleRender: (node: any) => (
          <span className={classNames({ red: node?.isRedLine === '1' })}>{node?.strategyName}</span>
        ),
        filterTreeNode: (input: any, option: any) => {
          return (option?.strategyName ?? '').toLowerCase().includes(input.toLowerCase());
        },
      },
    },
    strategyCode: {
      title: '编号',
      type: 'string',
      placeholder: '请输入编号',
      span: 8,
    },
  },
});
export default getSchema;
