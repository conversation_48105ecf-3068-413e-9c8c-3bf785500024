import { ReactComponent as IconExport } from '@/assets/export.svg';
import { Pagination } from '@/components/EasyTable';
import Form, { useForm, useWatch } from '@/components/Form';
import PageLayout from '@/components/PageLayout';
import TableRender from '@/components/TableRender';
import useStrategyRuleSearch, { initialValues } from '@/hooks/useStrategyRuleSearch';
import ConsultRecordDrawer from '@/pages/consultRecord/Drawer';
import { PREFIX } from '@/services';
import {
  downloadQualityResultList,
  queryDownloadStatus,
  queryListConfigNewColumns,
  queryQualityResultList,
  updateFeedbackColumns,
} from '@/services/ce';
import { download, fetchQueryList, getDateTimes } from '@/utils';
import { latestXDay, RULE_TAGGING_OPTIONS, TEXT_SET_QUERY_PAGE_SIZE } from '@/utils/constants';
import { useLocation, useSelector } from '@magi/magi';
import { Button, Divider, message, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep, initial, isEmpty } from 'lodash';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import DerivedModal from './DerivedModal';
import DerivedRecordModal from './DerivedRecordModal';
import styles from './index.scss';

export const IS_UPLOAD = 'isTestSetUpload';
const MAX_TABLE_TOTAL = 10000;

const customFormFields = [
  {
    label: '申诉状态',
    name: 'appealStatusList',
    fieldCode: 'appealStatus',
    type: 'select',
    allowClear: true,
    mode: 'multiple',
    options: [
      { label: '无申诉', value: 'none' },
      { label: '发起申诉', value: 'start' },
      { label: '申诉成功', value: 'success' },
      { label: '驳回申诉', value: 'reject' },
    ],
  },
];

const TestSetManage = () => {
  const { whitelistPermission } = useSelector((state: { global: any }) => state.global);
  const [form] = useForm();
  const location: any = useLocation();
  const [pageLoading, setPageLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [downloadLoadingData, setDownloadLoadingData] = useState<any>({});
  const [consultDrawerData, setConsultDrawerData] = useState<any>({ open: false, id: null });
  const [pagination, setPagination] = useState<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState(0);
  const [currentParams, setCurrentParams] = useState<any>({});
  const [queryColumns, setQueryColumns] = useState<any>([]);
  const [columnsSettingValue, setColumnsSettingValue] = useState<any>([]); // 列设置
  const [columns, setColumns] = useState([]); // 列
  const [listData, setListData] = useState<any[]>([]);
  const [derivedRecordVisible, setDerivedRecordVisible] = useState(false);
  const [derivedVisible, setDerivedVisible] = useState(false);
  const [uploadStatus, setUploadStatus] = useState();
  const currentTenantNo = useMemo(() => location.query.tenantNo, [location.query.tenantNo]);
  const { columns: strategyRuleSearchList } = useStrategyRuleSearch({
    form,
    tenantNo: currentTenantNo,
    showColumns: ['time', 'rule', 'session'],
    isTimeType: true,
  });
  const tableRef = useRef<any>();
  const qualityType = useWatch(['qualityType'], form);

  const isRealTimeQuality = useMemo(() => {
    const { realTimeQuality } = whitelistPermission || {};
    return realTimeQuality?.includes(currentTenantNo);
  }, [whitelistPermission, currentTenantNo]);

  const tableData = useMemo(() => {
    const queryTableColumns: any[] = [];
    const queryTableColumnsData: any[] = [];
    currentParams?.displayFieldListQuery?.forEach((item: any) => {
      if (!isEmpty(item.value) && !columns?.find((v: any) => v.dataIndex === item.fieldCode)) {
        queryTableColumns.push({
          title: item.fieldName,
          dataIndex: item.fieldCode,
          key: item.fieldCode,
          width: 200,
          ...(item.render ? { render: item.render } : {}),
        });
        queryTableColumnsData.push(item);
      }
    });
    return {
      columns: [...queryTableColumns, ...columns],
      queryTableColumnsData,
    };
  }, [currentParams, columns]);

  const chooseList = useMemo(
    () =>
      [{ fieldName: '会话原文', fieldCode: 'textContent' }, ...tableData.queryTableColumnsData, ...columnsSettingValue]
        .filter((v) => !!v.fieldCode)
        .map((v) => ({
          fieldName: v.fieldName,
          fieldCode: v.fieldCode,
          id: v.fieldCode,
        })),
    [tableData.queryTableColumnsData, columnsSettingValue]
  );

  const actionRow = useMemo(
    () => ({
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 80,
      render: (_: any, record: any, index: number) => {
        return (
          <Button
            onClick={() => {
              setConsultDrawerData({
                open: true,
                id: record?.sessionId,
                serviceId: record?.serviceId,
                curIndex: index,
              });
            }}
            style={{ padding: 0 }}
            type="link"
          >
            详情
          </Button>
        );
      },
    }),
    []
  );

  const formatColumns = useCallback(
    (columnsList: any) => {
      const _columns = cloneDeep(columnsList);
      _columns.push(actionRow);
      setColumnsSettingValue(_columns);
      // 列具体配置
      _columns.forEach((item: any) => {
        if (item.dataIndex !== 'action') {
          item.width = 200;
          if (['sessionId', 'ruleName', 'strategyName', 'failMessage'].includes(item.dataIndex)) {
            item.width = 250;
            item.render = (text: string) => (
              <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text }}>
                {text}
              </Typography.Paragraph>
            );
          } else if (['startTime', 'gmtModified'].includes(item.dataIndex)) {
            item.width = 180;
            item.render = (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss');
          }
        }
      });
      return _columns;
    },
    [actionRow]
  );

  const getSelectColumns = useCallback(
    async (displayType: string) => {
      setPageLoading(true);
      queryListConfigNewColumns({
        tenantNo: currentTenantNo,
        displayType,
      })
        .then((res) => {
          if (res.data.success) {
            if (displayType === 'resultTagQuery') {
              setQueryColumns(res.data.value);
            } else if (displayType === 'resultTagList') {
              setColumns(
                formatColumns(
                  res.data.value.map((item: any) => {
                    return {
                      ...item,
                      key: item.fieldCode,
                      title: item.fieldName,
                      dataIndex: item.fieldCode,
                      sort: item.sort,
                      hidden: item.hidden === 'N' ? false : true,
                    };
                  })
                )
              );
            }
          }
        })
        .finally(() => {
          setPageLoading(false);
        });
    },
    [currentTenantNo, formatColumns]
  );

  useEffect(() => {
    getSelectColumns('resultTagQuery');
    getSelectColumns('resultTagList');
  }, [getSelectColumns]);

  const getDownloadStatus = useCallback(async () => {
    const res = await queryDownloadStatus({ tenantNo: currentTenantNo });
    const isUpload = window.localStorage.getItem(IS_UPLOAD) !== '1';
    if (res.data.success) {
      isUpload && message.success('导出成功');
      setUploadStatus(res.data.value?.taskStatus);
    } else if (!res.data.errorMsg) {
      isUpload && message.error('导出失败，请重新导出');
    }
    window.localStorage.setItem(IS_UPLOAD, '1');
  }, [currentTenantNo]);

  useEffect(() => {
    getDownloadStatus();
  }, [getDownloadStatus]);

  const handleQuery = useCallback(
    async ({ current: pageNum, pageSize }: { current: number; pageSize: number }) => {
      const fields = cloneDeep(form.getFieldsValue());
      const { time, timeType, ...otherFields } = fields || {};
      let newTime = time;
      if (newTime?.length === 2) {
        newTime = getDateTimes(newTime);
      }
      const displayFieldListQuery: any[] = [];
      Object.entries(otherFields).forEach(([key, value]) => {
        const customField = customFormFields.find((item: any) => item.name === key);
        const resData = !isEmpty(value) && (queryColumns?.find((item: any) => item.fieldCode === key) || !!customField);
        if (resData) {
          displayFieldListQuery.push({
            fieldName: resData.fieldName || customField?.label,
            fieldCode: resData.fieldCode || customField?.fieldCode || key,
            render: customField
              ? (text?: string) => customField?.options?.find((item: any) => item.value === text)?.label || text
              : undefined,
            ...resData,
            isCustom: !!customField,
            value,
          });
          !customField && delete otherFields[key];
        }
      });
      const fromValues: any = {
        ...otherFields,
        displayFieldListQuery,
        tenantNo: currentTenantNo,
      };
      if (timeType === '2') {
        fromValues.qualityStartTime = newTime[0] && Date.parse(dayjs(newTime[0]) as any);
        fromValues.qualityEndTime = newTime[1] && Date.parse(dayjs(newTime[1]) as any);
      } else {
        fromValues.startTime = newTime[0];
        fromValues.endTime = newTime[1];
      }
      const params = {
        pageNum,
        pageSize,
        ...fromValues,
        displayFieldListQuery: fromValues.displayFieldListQuery.filter((v: any) => !v.isCustom),
      };
      setCurrentParams({ ...fromValues });
      setLoading(true);
      try {
        fetchQueryList(params, queryQualityResultList, TEXT_SET_QUERY_PAGE_SIZE).then((data) => {
          data && setListData(data || []);
        });
        const res = await queryQualityResultList(params);
        if (res.data.success) {
          const data = res.data.value;
          const { list = [], total } = data || {};
          setTotal(total);
          return {
            data: list,
            total: total > MAX_TABLE_TOTAL ? MAX_TABLE_TOTAL : total,
          };
        }
      } finally {
        setLoading(false);
      }
    },
    [form, currentTenantNo, queryColumns]
  );

  const exportResult = useCallback(
    (isNew?: boolean, displayFieldList?: any[]) => {
      if (!currentParams?.ruleIdList?.length && isNew) {
        message.error('请选择规则');
        return;
      }
      const maxTotal = isNew ? 1000 : 100000;
      if (total > maxTotal) {
        message.error(
          `导出内容为当前查询列表的结果，最大支持${maxTotal >= 10000 ? maxTotal / 10000 + '万' : maxTotal}条！`
        );
        return;
      }
      setDownloadLoadingData((preState: any) => ({ ...preState, [isNew ? 'new' : 'old']: true }));
      const params = {
        ...currentParams,
        displayFieldListQuery: currentParams.displayFieldListQuery?.filter((v: any) => !v.isCustom),
        displayFieldList:
          displayFieldList ||
          [...tableData.queryTableColumnsData, ...columnsSettingValue]
            ?.filter((item: any) => item.fieldCode && item.hidden !== true)
            ?.map((item: any) => ({
              fieldCode: item.fieldCode,
              fieldName: item.fieldName,
            })),
      };
      if (isNew) {
        download(`${PREFIX}/api/v1/qualityTask/downloadQualityResultListNew`, params, `测试集结果.xlsx`).finally(() => {
          setDownloadLoadingData((preState: any) => ({ ...preState, [isNew ? 'new' : 'old']: false }));
        });
      } else {
        downloadQualityResultList(params)
          .then((res) => {
            if (res.data.success) {
              window.localStorage.removeItem(IS_UPLOAD);
              setDerivedVisible(false);
              getDownloadStatus();
            }
          })
          .finally(() => {
            setDownloadLoadingData((preState: any) => ({ ...preState, [isNew ? 'new' : 'old']: false }));
          });
      }
    },
    [currentParams, total, columnsSettingValue, tableData, getDownloadStatus]
  );

  const onColumnsSettingChange = useCallback(
    (setting: any) => {
      const removeActionList = initial(setting);
      setColumnsSettingValue([...removeActionList, actionRow]);
      const displayFieldList: any[] = [];
      removeActionList.forEach((item: any) => {
        columns.forEach((column: any) => {
          if (item.key === column.key) {
            displayFieldList.push({
              fieldCode: column.dataIndex,
              fieldName: column?.title,
              fieldNo: column.key,
              sort: column?.sort,
              hidden: item?.hidden ? 'Y' : 'N',
            });
          }
        });
      });
      updateFeedbackColumns({
        displayFieldList: displayFieldList.map((item, index) => ({ ...item, sort: index + 1 })),
        displayType: 'resultTagList',
        tenantNo: currentTenantNo,
      });
    },
    [currentTenantNo, actionRow, columns]
  );

  const formColumns = useMemo(() => {
    const newStrategyRuleSearchList = [...strategyRuleSearchList];
    newStrategyRuleSearchList.splice(1, 0, {
      label: '质检类型',
      name: 'qualityType',
      type: 'select',
      options: [
        { label: '离线质检', value: null },
        { label: '实时质检', value: 3 },
      ],
      hidden: !isRealTimeQuality ? 'Y' : 'N',
    });
    const _columns = [
      ...newStrategyRuleSearchList,
      {
        label: '反馈结果',
        name: 'ruleTaggingList',
        type: 'select',
        mode: 'multiple',
        maxTagCount: 'responsive',
        allowClear: true,
        options: RULE_TAGGING_OPTIONS,
      },
      {
        label: '是否违规',
        name: 'isHit',
        type: 'select',
        allowClear: true,
        options: [
          { label: '是', value: 'true' },
          { label: '否', value: 'false' },
        ],
      },
      ...customFormFields,
      {
        label: '违规来源',
        name: 'illegalSource',
        type: 'select',
        allowClear: true,
        options: [
          { label: 'AI判断', value: 'AI判断' },
          { label: '人工反馈', value: '人工反馈' },
        ],
      },
      {
        label: '是否成交',
        name: 'dealFlag',
        type: 'select',
        allowClear: true,
        options: [
          { label: '成交', value: '成交' },
          { label: '未成交', value: '未成交' },
        ],
      },
    ];
    const newQueryColumns = queryColumns
      ?.filter((item: any) => !_columns.find((v) => v.name === item.fieldCode) && item.hidden !== 'Y')
      ?.map((item: any) => {
        return {
          label: item.fieldName,
          name: item.fieldCode,
        };
      });
    return [..._columns.filter((v: any) => v.hidden !== 'Y'), ...(newQueryColumns || [])];
  }, [strategyRuleSearchList, queryColumns, isRealTimeQuality]);

  useEffect(() => {
    isRealTimeQuality && form.setFieldsValue({ qualityType: null });
  }, [isRealTimeQuality, form]);

  return (
    <PageLayout>
      <Spin spinning={pageLoading}>
        <div className={styles.container}>
          <Form
            form={form}
            initialValues={{
              ...initialValues,
              qualityType: isRealTimeQuality ? null : undefined,
              time: latestXDay(7),
            }}
            columns={formColumns}
            loading={loading}
            onReset={() => {
              form.resetFields();
              setPagination((preState) => ({ ...preState, current: 1 }));
              tableRef.current?.refresh();
            }}
            onQuery={() => {
              setPagination((preState) => ({ ...preState, current: 1 }));
              tableRef.current?.refresh();
            }}
          />
          <Divider style={{ margin: '0 0 16px' }} />
          <TableRender
            ref={tableRef}
            rowKey={'id'}
            setPageData={setPagination}
            pageData={{
              ...pagination,
              showTotal: () => `共${total}条`,
            }}
            request={handleQuery as any}
            style={{ marginTop: -16, padding: 0 }}
            columns={tableData.columns}
            toolbarAction={{
              columnsSettingValue,
              onColumnsSettingChange,
            }}
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  type="primary"
                  ghost
                  onClick={() => exportResult(true)}
                  icon={<IconExport style={{ marginTop: 1 }} />}
                  loading={downloadLoadingData.new}
                >
                  导出为测试集
                </Button>
                <Button
                  onClick={() => {
                    if (total <= 0) {
                      message.error('无数据可导出');
                      return;
                    }
                    setDerivedVisible(true);
                  }}
                  icon={<IconExport style={{ marginTop: 1 }} />}
                  loading={uploadStatus === 1}
                  style={{ marginLeft: 16 }}
                >
                  {uploadStatus === 1 ? '导出中' : '导出'}
                </Button>
                <Button onClick={() => setDerivedRecordVisible(true)} style={{ marginLeft: 16 }}>
                  导出记录
                </Button>
              </div>
            }
          />
        </div>
      </Spin>
      <ConsultRecordDrawer
        listData={listData}
        drawerData={consultDrawerData}
        setDrawerData={setConsultDrawerData}
        isRealTime={qualityType === 3}
      />
      <DerivedRecordModal
        open={derivedRecordVisible}
        onCancel={() => setDerivedRecordVisible(false)}
        tenantNo={currentTenantNo}
      />
      <DerivedModal
        open={derivedVisible}
        onCancel={() => setDerivedVisible(false)}
        availableFields={chooseList}
        loading={downloadLoadingData.old}
        exportResult={exportResult}
      />
    </PageLayout>
  );
};

export default TestSetManage;
