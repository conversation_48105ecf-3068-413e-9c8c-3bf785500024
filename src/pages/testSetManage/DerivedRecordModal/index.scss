.list {
  margin: 15px 0 !important;
  max-height: 350px;
  overflow-y: auto;
  .item {
    border: 1px solid #d0d5dd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    &:not(:first-child) {
      margin-top: 8px;
    }
    .icon {
      width: 32px;
    }
    .info {
      margin-left: 12px;
      flex: 1;
      h1 {
        font-size: 14px;
        line-height: 20px;
        color: #344054;
        font-weight: 500;
        margin: 0;
      }
      span {
        font-size: 14px;
        color: #475467;
        line-height: 20px;
      }
    }
    .btn {
      font-size: 20px;
      color: #4e5969;
      padding: 0;
      width: initial !important;
      height: initial !important;
      border: none;
      &:hover {
        color: var(--primary-color);
      }
    }
  }
}
