import { ReactComponent as DownloadIcon } from '@/assets/download.svg';
import { ReactComponent as DeleteIcon } from '@/assets/trash.svg';
import xlsImg from '@/assets/xls.png';
import { delDownloadDetail, queryDownloadCsvList } from '@/services/ce';
import { downloadFile } from '@/utils';
import { LoadingOutlined } from '@ant-design/icons';
import { Button, List, message, Modal, Popconfirm, Spin } from 'antd';
import React, { FC, useCallback, useEffect, useState } from 'react';
import styles from './index.scss';

interface IProps {
  open: boolean;
  onCancel: () => void;
  tenantNo: string;
}

const DerivedRecordModal: FC<IProps> = ({ open, onCancel, tenantNo }) => {
  const [loading, setLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState<any>({});
  const [isDeletedLoading, setIsDeletedLoading] = useState<any>({});
  const [downloadList, setDownloadList] = useState<any[]>([]);

  const fetchDownloadCsvList = useCallback(async () => {
    try {
      setLoading(true);
      const res = await queryDownloadCsvList({ tenantNo });
      setDownloadList((res?.data?.value || []).slice(0, 10));
    } finally {
      setLoading(false);
    }
  }, [tenantNo]);

  useEffect(() => {
    open && fetchDownloadCsvList();
  }, [fetchDownloadCsvList, open]);

  const exportResult = useCallback((item: any) => {
    try {
      setIsLoadingData((preState: any) => ({ ...preState, [item.id]: true }));
      downloadFile(item.ossUrl, item.fileName);
    } catch (error) {
      message.error('下载失败');
    } finally {
      setIsLoadingData((preState: any) => ({ ...preState, [item.id]: false }));
    }
  }, []);

  const deleteDownloadDetail = useCallback(
    async (id: string) => {
      try {
        setIsDeletedLoading((preState: any) => ({ ...preState, [id]: true }));
        const res = await delDownloadDetail({ taskId: id });
        res.data.success && fetchDownloadCsvList();
      } finally {
        setIsDeletedLoading((preState: any) => ({ ...preState, [id]: false }));
      }
    },
    [fetchDownloadCsvList]
  );

  return (
    <Modal
      width={500}
      open={open}
      destroyOnClose
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
      ]}
      title="导出记录"
      closable={false}
    >
      <List
        className={styles.list}
        dataSource={downloadList}
        loading={loading}
        key={'id'}
        renderItem={(item) => (
          <div className={styles.item} key={item.id}>
            <img className={styles.icon} src={xlsImg} alt="" />
            <div className={styles.info}>
              <h1>{item.fileName}</h1>
              <span>{item.fileSizeKB ? `${item.fileSizeKB}KB` : ''}</span>
            </div>
            <Button
              className={styles.btn}
              type="link"
              onClick={() => exportResult(item)}
              disabled={isLoadingData[item.id] || isDeletedLoading[item.id]}
            >
              {isLoadingData[item.id] ? (
                <Spin indicator={<LoadingOutlined spin style={{ fontSize: 17 }} />} />
              ) : (
                <DownloadIcon />
              )}
            </Button>
            {!isLoadingData[item.id] && (
              <Popconfirm
                title="确认删除吗"
                onConfirm={() => deleteDownloadDetail(item.id)}
                okButtonProps={{ loading: isDeletedLoading[item.id] }}
              >
                <Button className={styles.btn} icon={<DeleteIcon />} type="link" style={{ marginLeft: 12 }} />
              </Popconfirm>
            )}
          </div>
        )}
      />
    </Modal>
  );
};

export default DerivedRecordModal;
