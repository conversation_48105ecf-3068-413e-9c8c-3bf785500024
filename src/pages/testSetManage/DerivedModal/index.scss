.derivedContent {
  display: flex;
  .title {
    color: #1d2129;
    font-size: 14px;
    font-weight: 500;
    margin: 15px 0 8px;
    span {
      font-size: 12px;
      color: #86909c;
      font-weight: normal;
    }
  }
  .chooseBox {
    flex: 1;
    margin-right: 20px;
    .chooseCon {
      padding: 20px;
      border: 1px solid #e5e6eb;
      border-radius: 8px;
      height: 248px;
      overflow-y: auto;
      box-sizing: border-box;
      .checkAll {
        width: 100%;
        margin-bottom: 16px;
      }
      :global {
        .ant-checkbox-group {
          width: 100%;
          gap: 20px 16px;
          .ant-checkbox-wrapper {
            width: calc((100% - 40px) / 3);
          }
        }
      }
    }
  }
  .list {
    :global {
      .ant-list {
        width: 234px;
        height: 248px;
        overflow-y: auto;
        border: 1px solid #e5e6eb;
        border-radius: 8px;
        padding: 20px 0 12px;
      }
      .ant-btn {
        padding: 4px;
        color: #86909c;
        border: none;
        .anticon {
          font-size: 10px;
        }
      }
    }
    .tuodong {
      color: #86909c;
      font-size: 16px;
      margin-right: 8px;
    }

    .sortableItemStyles {
      padding: 0 10px 0 20px;
      height: 36px;
      display: flex;
      align-items: center;
      background-color: white;
      cursor: grab;
      color: #1d2129;
      font-size: 14px;

      .label {
        flex: 1;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &.sortableItem {
        &:hover {
          background-color: #f8f8f8;
        }
      }
      &.dragHandleStyles {
        max-width: 234px;
        box-shadow: 0px 4px 12px 0px #181b2514;
        cursor: grabbing;
      }
    }
  }
}

.tips {
  background: #f8f8f8;
  padding: 8px 20px;
  border-radius: 8px;
  color: #4e5969;
  font-size: 12px;
  font-weight: 500;
  margin-top: 20px;
}
