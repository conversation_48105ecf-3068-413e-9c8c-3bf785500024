import { ReactComponent as TuoDong<PERSON>con } from '@/assets/tuodong.svg';
import { CloseOutlined } from '@ant-design/icons';
import {
  closestCenter,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Checkbox, List, Modal } from 'antd';
import classNames from 'classnames';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.scss';

const SortableItem: FC<{ children: React.ReactNode; id: string }> = ({ id, children }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: id });

  return (
    <div
      ref={setNodeRef}
      className={classNames(styles.sortableItemStyles, styles.sortableItem)}
      style={{
        transform: CSS.Transform.toString(transform),
        transition,
      }}
      {...attributes}
      {...listeners}
    >
      {children}
    </div>
  );
};

interface AvailableFieldsItem {
  fieldCode: string;
  fieldName: string;
  id: string;
}

interface IProps {
  open: boolean;
  onCancel: () => void;
  availableFields: AvailableFieldsItem[];
  loading?: boolean;
  exportResult: (...args: any[]) => void;
}

const DerivedModal: FC<IProps> = ({ open, onCancel, availableFields, loading, exportResult }) => {
  const [selectedFields, setSelectedFields] = useState<AvailableFieldsItem[]>([]);
  const [activeId, setActiveId] = useState(null);

  useEffect(() => {
    !open && setSelectedFields([]);
  }, [open]);

  const checkAll = useMemo(() => availableFields.length === selectedFields.length, [availableFields, selectedFields]);
  const indeterminate = useMemo(
    () => selectedFields.length > 0 && selectedFields.length < availableFields.length,
    [availableFields, selectedFields]
  );

  const onCheckAllChange = useCallback(
    (e: any) => {
      setSelectedFields(e.target.checked ? availableFields : []);
    },
    [availableFields]
  );

  const handleFieldSelect = useCallback(
    (checkedValues) => {
      setSelectedFields(availableFields.filter((field) => checkedValues.includes(field.id)));
    },
    [availableFields]
  );

  const handleRemoveField = useCallback((id, event: any) => {
    event.stopPropagation();
    setSelectedFields((preState) => preState.filter((field) => field.id !== id));
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const customTransform = useCallback(({ transform }) => {
    return {
      ...transform,
      x: 0,
    };
  }, []);

  const handleDragStart = useCallback((event: any) => {
    const { active } = event;
    setActiveId(active.id);
  }, []);

  const handleDragEnd = useCallback((event: any) => {
    const { active, over } = event;
    if (!over) {
      setActiveId(null);
      return;
    }
    if (active.id !== over.id) {
      setSelectedFields((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
    setActiveId(null);
  }, []);

  return (
    <Modal
      width={800}
      open={open}
      destroyOnClose
      onCancel={onCancel}
      okText="确认导出"
      title="导出"
      okButtonProps={{ disabled: !selectedFields?.length, loading }}
      onOk={() =>
        exportResult(
          false,
          selectedFields.map((field) => ({ fieldCode: field.fieldCode, fieldName: field.fieldName }))
        )
      }
    >
      <div className={styles.derivedContent}>
        <div className={styles.chooseBox}>
          <h1 className={styles.title}>
            可选导出字段<span>（共{availableFields?.length ?? 0}个）</span>
          </h1>
          <div className={styles.chooseCon}>
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
              className={styles.checkAll}
            >
              全选
            </Checkbox>
            <Checkbox.Group
              value={selectedFields.map((v) => v.id)}
              options={availableFields.map((field) => ({ label: field.fieldName, value: field.id }))}
              onChange={handleFieldSelect}
            />
          </div>
        </div>
        <div className={styles.list}>
          <h1 className={styles.title}>
            已选字段<span>（共{selectedFields?.length ?? 0}个）</span>
          </h1>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            modifiers={[customTransform]}
          >
            <SortableContext items={selectedFields} strategy={verticalListSortingStrategy}>
              <List
                dataSource={selectedFields}
                renderItem={(item) => (
                  <SortableItem key={item.id} id={item.id}>
                    <TuoDongIcon className={styles.tuodong} />
                    <span className={styles.label}>{item.fieldName}</span>
                    <Button type="link" icon={<CloseOutlined />} onClick={(e) => handleRemoveField(item.id, e)} />
                  </SortableItem>
                )}
              />
            </SortableContext>
            <DragOverlay>
              {activeId ? (
                <div className={classNames(styles.sortableItemStyles, styles.dragHandleStyles)}>
                  <TuoDongIcon className={styles.tuodong} />
                  <span className={styles.label}>{selectedFields.find((item) => item.id === activeId)?.fieldName}</span>
                </div>
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>
      </div>
      <div className={styles.tips}>
        Tips：1、单次导出数据上限为10万条；2、选中“对话原文”进行导出，所需时间将会延长；3、导出字段选择越多，导出时间越长。
      </div>
    </Modal>
  );
};

export default DerivedModal;
