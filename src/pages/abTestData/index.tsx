import { Line } from '@/components/Charts';
import { isDateFormat } from '@/components/Charts/Line';
import Empty from '@/components/Empty';
import Form, { useForm } from '@/components/Form';
import PageLayout from '@/components/PageLayout';
import useStrategyRuleSearch, { initialValues } from '@/hooks/useStrategyRuleSearch';
import { offsetOptions } from '@/pages/tendency';
import { queryAbTestMonitoring, queryMetricCycles } from '@/services/ce';
import { MODEL_TYPE_DARA } from '@/utils/constants';
import { useLocation } from '@magi/magi';
import { Divider, Segmented, Select, Spin } from 'antd';
import dayjs from 'dayjs';
import React, { Fragment, useCallback, useEffect, useState } from 'react';
import styles from './index.scss';

const AbTestData = () => {
  const [form] = useForm();
  const location: any = useLocation();
  const { tenantNo: currentTenantNo } = location.query;
  const { columns: strategyRuleSearchList } = useStrategyRuleSearch({
    form,
    tenantNo: currentTenantNo,
    showColumns: ['time', 'rule'],
    isTimeType: true,
    isFilterSmallModelConfig: true,
  });
  const [loading, setLoading] = useState(false);
  const [lineData, setLineData] = useState<any>({});
  const [metricCycle, setMetricCycle] = useState('');
  const [offset, setOffset] = useState(0);
  const [cyclesLoading, setCyclesLoading] = useState(false);
  const [cyclesOptions, setCyclesOptions] = useState([]);

  const getTimeData = useCallback(() => {
    let [startTime, endTime] = form.getFieldValue('time') || initialValues.time;
    const timeType = form.getFieldValue('timeType') || initialValues.timeType;
    startTime && (startTime = `${dayjs(startTime)?.format('YYYY-MM-DD')} 00:00:00`);
    endTime && (endTime = `${dayjs(endTime)?.format('YYYY-MM-DD')} 23:59:59`);
    return timeType === '2'
      ? {
          qualityStartTime: startTime && Date.parse(dayjs(startTime) as any),
          qualityEndTime: endTime && Date.parse(dayjs(endTime) as any),
        }
      : { startTime, endTime };
  }, [form]);

  const handleQueryMetricCycles = useCallback(
    async (cb?: (bool: boolean) => void) => {
      const timeData = getTimeData();
      if (!(timeData.startTime && timeData.endTime) && !(timeData.qualityStartTime && timeData.qualityEndTime)) return;
      try {
        setCyclesLoading(true);
        const res = await queryMetricCycles({ tenantNo: currentTenantNo, ...timeData });
        const data = res?.data?.value || [];
        setCyclesOptions(data);
        setMetricCycle((preState) => {
          const val = data.filter(({ disabled }: { disabled: boolean }) => !disabled);
          const firstValue = val?.[0]?.value;
          cb?.(preState === firstValue);
          if (val?.find(({ value }: { value: string }) => value === preState)) return preState;
          firstValue === 'week' && setOffset(0);
          return firstValue;
        });
      } finally {
        setCyclesLoading(false);
      }
    },
    [getTimeData, currentTenantNo]
  );

  useEffect(() => {
    handleQueryMetricCycles();
  }, [handleQueryMetricCycles]);

  const handleQuery = useCallback(async () => {
    if (!metricCycle) return;
    try {
      const values = await form.validateFields();
      const { time, timeType, ...formData } = values;
      const timeData = getTimeData();
      setLoading(true);
      const params = {
        tenantNo: currentTenantNo,
        metricCycle,
        monitoringType: 'ruleTagAccuracy',
        ...timeData,
        ...formData,
      };
      metricCycle === 'week' && (params.offset = offset);
      const res = await queryAbTestMonitoring(params);
      if (res?.data?.success) {
        const { list } = res?.data?.value || {};
        const dataMap: any = {};
        list?.forEach(({ buckets, name, key, modelType }: any) => {
          !dataMap[name] && (dataMap[name] = []);
          buckets?.forEach(({ keyAsString, accuracyTag, moleculeTag, denominatorTag }: any) => {
            dataMap[name].push({
              date: keyAsString,
              key: (key || '') + (modelType || ''),
              type: MODEL_TYPE_DARA[modelType as keyof typeof MODEL_TYPE_DARA],
              value: Number(accuracyTag?.replace('%', '') || 0),
              molecule: moleculeTag,
              denominator: denominatorTag,
            });
          });
        });
        setLineData(dataMap);
      }
    } finally {
      setLoading(false);
    }
  }, [metricCycle, currentTenantNo, getTimeData, offset, form]);

  useEffect(() => {
    handleQuery();
  }, [handleQuery]);

  const queryList = useCallback(() => {
    handleQueryMetricCycles((isCycleChange) => {
      isCycleChange && handleQuery();
    });
  }, [handleQueryMetricCycles, handleQuery]);

  const getConfig = useCallback(
    (name: string) => {
      const _lineData: any[] =
        (!isDateFormat(lineData[name]?.[0]?.date)
          ? lineData[name]
          : lineData[name]?.sort((a: any, b: any) => (a.date > b.date ? 1 : -1))) || [];
      const getValue = (val?: number) => (val || 0) + '%';
      const getScale = (item?: any) => {
        const { molecule, denominator } =
          _lineData?.find(({ key, date }) => {
            const newDate = isDateFormat(date) ? dayjs(date).valueOf()?.toString() : date;
            return key === item?.key && newDate === item?.date;
          }) || {};
        return `(${molecule ?? 0}/${denominator ?? 0})`;
      };
      return {
        data: _lineData,
        xField: 'date',
        yField: 'value',
        seriesField: 'key',
        tooltip: {
          formatter: (datum: any) => {
            return {
              name: _lineData?.find(({ key }) => key === datum.key)?.type,
              value: getValue(datum.value) + getScale(datum),
            };
          },
        },
        meta: {
          value: {
            formatter: (v: number) => getValue(v),
          },
        },
      };
    },
    [lineData]
  );

  return (
    <PageLayout>
      <div className={styles.container}>
        <Form
          form={form}
          initialValues={initialValues}
          columns={strategyRuleSearchList}
          onQuery={queryList}
          onReset={queryList}
          loading={loading}
        />
        <Divider style={{ margin: '0 0 16px' }} />
        <div className={styles.operateBox}>
          <Spin spinning={cyclesLoading}>
            {metricCycle === 'week' && (
              <Select options={offsetOptions} value={offset} onChange={setOffset} style={{ marginRight: 10 }} />
            )}
            <Segmented
              className={styles.segmented}
              options={cyclesOptions}
              value={metricCycle}
              onChange={setMetricCycle}
            />
          </Spin>
        </div>
        <Spin spinning={loading}>
          {Object.keys(lineData || {})?.length ? (
            Object.keys(lineData || {}).map((name, index) => (
              <Fragment key={`${name}__${index}`}>
                <h1 className={styles.title}>{name || ''}</h1>
                <Line autoFit {...getConfig(name)} descName="type" />
              </Fragment>
            ))
          ) : (
            <Empty style={{ marginTop: 100 }} />
          )}
        </Spin>
      </div>
    </PageLayout>
  );
};

export default AbTestData;
