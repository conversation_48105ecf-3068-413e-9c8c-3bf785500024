import ColumnList from '@/pages/dailyReports/ColumnList';
import { Typography } from 'antd';
import Big from 'big.js';
import React, { FC, useCallback, useMemo } from 'react';
import styles from './index.scss';

const getPercentValue = (val?: number) =>
  Big(val || 0)
    .times(100)
    .toString() + '%';

interface IProps {
  columnsData?: { [key: string]: any };
  setDrawerData: React.Dispatch<
    React.SetStateAction<{
      open: boolean;
      data: null;
    }>
  >;
}

const Columns: FC<IProps> = ({ columnsData, setDrawerData }) => {
  const getColumnProps = useCallback((data: any[]) => {
    const getName = (name?: string) => data?.find((item) => item.id === name)?.name || name;

    const commonData = {
      tooltip: {
        showTitle: false,
        title: (title: string) => getName(title),
        formatter: (datum: any) => {
          return { name: getName(datum.id), value: datum.value || 0 };
        },
      },
      color: () => {
        return '#7094F3';
      },
      xAxis: {
        label: {
          formatter: (text: string) => {
            const str = getName(text);
            return str?.length > 8 ? str?.slice(0, 8) + '...' : str;
          },
        },
      },
    };
    const scaleData = {
      ...commonData,
      tooltip: {
        ...commonData.tooltip,
        formatter: (datum: any) => {
          return {
            name: getName(datum.id),
            value: getPercentValue(datum.value),
          };
        },
      },
      meta: {
        value: {
          formatter: (v: number) => getPercentValue(v),
        },
      },
    };
    return { commonData, scaleData };
  }, []);

  const handleColumnProps = useCallback(
    (list: any[], type: 'commonData' | 'scaleData') => {
      const newList = list?.map(({ id, ...item }) => ({ ...item, id: id?.toString() }));
      return {
        data: newList,
        ...(getColumnProps(newList)?.[type] || {}),
      };
    },
    [getColumnProps]
  );

  const handleMixColumnProps = useCallback(
    (list: any[], config?: any) => {
      const newList = list?.map(({ id, ...item }) => ({ ...item, id: id?.toString() }));
      const { xAxis, color, tooltip } = getColumnProps(newList)?.['commonData'] || {};
      const data = newList || [];
      return {
        data,
        tooltip: { title: tooltip?.title },
        columnConfig: {
          data,
          color: color?.(),
          xAxis,
          xField: 'id',
          yField: 'value',
          meta: {
            value: {
              alias: config?.columnAlias,
              formatter: (v: any) => (config.columnType === 'scaleData' ? getPercentValue(v) : v),
            },
          },
        },
        lineConfig: {
          data,
          xField: 'id',
          yField: 'lineValue',
          meta: {
            lineValue: {
              alias: config?.lineAlias,
              formatter: (v: any) => (config.lineType === 'scaleData' ? getPercentValue(v) : v),
            },
          },
        },
      };
    },
    [getColumnProps]
  );

  const columnList = useMemo(() => {
    const {
      businessViolationRateList,
      businessViolationReturnVisitRateList,
      sessionViolationRateList,
      sessionsCountList,
      businessCountList,
    } = columnsData || {};
    const baseDataMap = new Map();
    sessionsCountList?.forEach((item: any) => {
      baseDataMap.set(item.tenantNo, {
        ...item,
        lineValue: item.amount,
        id: item.tenantNo,
        name: item.name,
      });
    });
    businessCountList?.forEach((item: any) => {
      if (baseDataMap.has(item.tenantNo)) {
        baseDataMap.set(item.tenantNo, { ...baseDataMap.get(item.tenantNo), businessData: item, value: item.amount });
      } else {
        baseDataMap.set(item.tenantNo, { businessData: item, value: item.amount, id: item.tenantNo, name: item.name });
      }
    });

    return [
      {
        title: '基础信息',
        tip: (
          <>
            会话量：会话数量
            <br />
            商机量：商机数量
          </>
        ),
        isMix: true,
        ...handleMixColumnProps(Array.from(baseDataMap.values()), {
          columnAlias: '商机量',
          lineAlias: '会话量',
        }),
      },
      {
        title: '商机违规情况',
        tip: (
          <>
            商机违规量：在商机维度下，违反选中规则的商机数量
            <br />
            商机违规率：商机违规量/商机量
          </>
        ),
        isMix: true,
        ...handleMixColumnProps(
          businessViolationRateList?.map((item: any) => ({
            ...item,
            lineValue: item.businessAccuracy,
            value: item.molecule,
            id: item.tenantNo,
          })),
          { columnAlias: '商机违规量', lineAlias: '商机违规率', lineType: 'scaleData' }
        ),
      },
      {
        title: '回访后商机违规情况',
        tip: (
          <>
            商机违规量：在商机维度下，违反选中规则的商机数量
            <br />
            商机违规率：商机违规量/商机量
          </>
        ),
        isMix: true,
        queryType: 'returnVisit', // 回访查质检结果列表的详情接口入参
        ...handleMixColumnProps(
          businessViolationReturnVisitRateList?.map((item: any) => ({
            ...item,
            lineValue: item.businessAccuracy,
            value: item.molecule,
            id: item.tenantNo,
          })),
          { columnAlias: '商机违规量', lineAlias: '商机违规率', lineType: 'scaleData' }
        ),
      },
      {
        title: '会话违规率',
        tip: '违规数量/会话数量',
        ...handleColumnProps(
          sessionViolationRateList?.map((item: any) => ({
            ...item,
            value: item.sessionAccuracy,
            id: item.tenantNo,
          })),
          'scaleData'
        ),
      },
    ];
  }, [columnsData, handleColumnProps, handleMixColumnProps]);

  const getColumns = useCallback((index: number): any[] => {
    const list = [
      { name: '事业部', value: '商机量', num: '会话量' },
      { name: '事业部', value: '商机违规量', num: '商机违规率' },
      { name: '事业部', value: '商机违规量', num: '商机违规率' },
      { name: '事业部', value: '会话违规率' },
    ];
    const render = (key: string) => (text: number, record: any) => {
      const textStr =
        (list[index] as any)[key].indexOf('率') !== -1
          ? Big(text || 0)
              .times(100)
              .toString() +
            '%' +
            `(${record.molecule ?? 0}/${record.denominator ?? 0})`
          : text;
      return (
        <span style={{ textAlign: 'center' }}>
          <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 1, tooltip: textStr }}>
            {textStr}
          </Typography.Paragraph>
        </span>
      );
    };
    const resList: any[] = [
      {
        title: list[index].name,
        dataIndex: 'name',
        width: 80,
      },
      {
        title: list[index].value,
        dataIndex: 'value',
        className: styles.tableItemValue,
        render: render('value'),
      },
    ];
    list[index].num &&
      resList.push({
        title: list[index].num,
        dataIndex: 'lineValue',
        className: styles.tableItemValue,
        render: render('num'),
      });
    return resList;
  }, []);

  return (
    <ColumnList
      columnList={columnList}
      popoverClassName={styles.popover}
      setDrawerData={setDrawerData}
      getColumns={getColumns}
    />
  );
};

export default Columns;
