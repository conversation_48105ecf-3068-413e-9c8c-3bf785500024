import Form, { useForm } from '@/components/Form';
import PageLayout from '@/components/PageLayout';
import useGetRuleNames from '@/hooks/useGetRuleNames';
import { initialValues } from '@/hooks/useStrategyRuleSearch';
import { getTime } from '@/pages/dailyReports';
import HitResultDrawer from '@/pages/hitResult/HitResultDrawer';
import { getDataScreening } from '@/services/redLine';
import { useLocation } from '@magi/magi';
import { Divider, Spin, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import Columns from './Columns';
import styles from './index.scss';

const RedLineDataOverview = () => {
  const location: any = useLocation();
  const [form] = useForm();
  const { tenantNo: currentTenantNo } = location.query;
  const { strategyRuleSearchList, ruleNames, searchDataObj, setSearchData } = useGetRuleNames({
    form,
    currentTenantNo,
    showColumns: ['time', 'rule'],
  });
  const [columnsData, setColumnsData] = useState<{ [key: string]: any }>({});
  const [loading, setLoading] = useState(false);
  const [drawerData, setDrawerData] = useState({ open: false, data: null });
  const [startDate, setStartDate] = useState<Dayjs | null>(initialValues.time[0]);

  const searchData = useMemo(
    () => ({ ...searchDataObj, queryType: (drawerData?.data as any)?.queryType }),
    [searchDataObj, drawerData]
  );

  const formColumns = useMemo(() => {
    return strategyRuleSearchList?.map((item: any) => {
      if (item.name === 'time') {
        return {
          ...item,
          disabledDate: (current: Dayjs) => {
            if (!startDate) return false;
            const maxDate = dayjs(startDate).add(1, 'months');
            const minDate = dayjs(startDate).subtract(1, 'months');
            return current && (current > maxDate || current < minDate);
          },
          onCalendarChange: (dates: Dayjs[]) => setStartDate(dates?.[0]),
          onChange: (dates: Dayjs[]) => setStartDate((dates as Dayjs[])?.[0]),
        };
      }
      return item;
    });
  }, [strategyRuleSearchList, startDate]);

  const handleDataScreening = async (params: any) => {
    const { time, sessionTypes, timeType, ...otherParams } = params || {};
    if (!currentTenantNo || !time?.length) return;
    const res = await getDataScreening({
      ...getTime(time, timeType),
      ...otherParams,
    });
    setColumnsData(res?.data?.value || {});
  };

  const handleQuery = async () => {
    try {
      const values = await form.validateFields();
      setSearchData(values);
      setLoading(true);
      await handleDataScreening(values);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleQuery();
  }, []);

  return (
    <PageLayout>
      <div className={styles.container}>
        <Form
          form={form}
          initialValues={initialValues}
          columns={formColumns}
          onQuery={handleQuery}
          onReset={handleQuery}
          loading={loading}
        />
        <Divider style={{ margin: '0 0 20px' }} />
        <Spin spinning={loading}>
          <h1 className={styles.title}>报表详情</h1>
          <Typography.Paragraph className={styles.desc} ellipsis={{ rows: 2, tooltip: ruleNames }}>
            当前选中规则：{ruleNames || '-'}
          </Typography.Paragraph>
          <Columns columnsData={columnsData} setDrawerData={setDrawerData} />
        </Spin>
      </div>
      <HitResultDrawer
        drawerData={drawerData}
        setDrawerData={setDrawerData}
        currentTenantNo={(drawerData?.data as any)?.tenantNo}
        searchData={searchData}
      />
    </PageLayout>
  );
};

export default RedLineDataOverview;
