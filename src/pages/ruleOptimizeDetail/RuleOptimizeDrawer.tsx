import SubTitle from '@/components/SubTitle';
import { drawerWidth } from '@/utils/constants';
import { useLocation } from '@magi/magi';
import { Drawer } from 'antd';
import classNames from 'classnames';
import React, { FC, useMemo } from 'react';
import RuleOptimizeDetail from './index';
import styles from './index.scss';

interface IProps {
  visible: boolean;
  id?: number;
  onCancel: () => void;
}

const RuleOptimizeDrawer: FC<IProps> = ({ visible, onCancel, id }) => {
  const location: any = useLocation();
  const isIframe = useMemo(() => location?.query?.isIframe === 'true' || false, [location?.query?.isIframe]);

  return (
    <Drawer
      maskClosable={false}
      title={'规则优化查看'}
      width={drawerWidth}
      open={visible}
      push={false}
      mask={false}
      rootClassName={classNames(styles.ruleOptimizeDrawer, { [styles.ruleOptimizeIframeDrawer]: isIframe })}
      onClose={onCancel}
    >
      <SubTitle style={{ margin: '10px 0' }} title={'规则优化查看'} goBack={onCancel} />
      {visible && <RuleOptimizeDetail id={id} />}
    </Drawer>
  );
};

export default RuleOptimizeDrawer;
