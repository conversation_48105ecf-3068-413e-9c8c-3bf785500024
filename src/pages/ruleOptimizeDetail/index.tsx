import { ReactComponent as OptimizationProcess } from '@/assets/optimizationProcess.svg';
import { ReactComponent as ResultContrast } from '@/assets/resultContrast.svg';
import { ReactComponent as RuleContrast } from '@/assets/ruleContrast.svg';
import { Column } from '@/components/Charts';
import CodeMirror from '@/components/CodeMirror';
import Empty from '@/components/Empty';
import PageLayout from '@/components/PageLayout';
import { queryStepExec, queryTaskDetail } from '@/services/ce';
import { antConfig } from '@/utils/constants';
import { useLocation } from '@magi/magi';
import { Button, Collapse, Progress, Spin, Steps, Tag } from 'antd';
import { debounce } from 'lodash';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.scss';

const tabData = [
  {
    key: 'dataLoading',
    label: '数据加载',
    process: 5,
  },
  {
    key: 'rulePerformance',
    label: '规则性能评估',
    process: 10,
  },
  {
    key: 'caseAnalysis',
    label: '案例分析',
    process: 40,
  },
  {
    key: 'differenceClassification',
    label: '差异分类',
    process: 50,
  },
  {
    key: 'ruleGeneration',
    label: '规则生成',
    process: 60,
  },
  {
    key: 'ruleValidation',
    label: '规则验证',
    process: 90,
  },
  {
    key: 'resultEvaluation',
    label: '结果评估',
    process: 100,
  },
];

const columnList = [
  {
    type: '新规则',
    name: '召回率',
    key: 'newRecall',
  },
  {
    type: '原规则',
    name: '召回率',
    key: 'originRecall',
  },
  {
    type: '新规则',
    name: '准确率',
    key: 'newAccuracy',
  },
  {
    type: '原规则',
    name: '准确率',
    key: 'originAccuracy',
  },
  {
    type: '新规则',
    name: '精确率',
    key: 'newPrecision',
  },
  {
    type: '原规则',
    name: '精确率',
    key: 'originPrecision',
  },
  {
    type: '新规则',
    name: 'F1分数',
    key: 'newF1Score',
  },
  {
    type: '原规则',
    name: 'F1分数',
    key: 'originF1Score',
  },
  {
    type: '新规则',
    name: '特异性',
    key: 'newSpecify',
  },
  {
    type: '原规则',
    name: '特异性',
    key: 'originSpecify',
  },
];

interface IProps {
  id?: number;
}

const RuleOptimizeDetail: FC<IProps> = ({ id }) => {
  const [metricsData, setMetricsData] = useState<any>({});
  const [stepExecData, setStepExecData] = useState<any>({});
  const [taskData, setTaskData] = useState<any>({});
  const [activeKeyData, setActiveKeyData] = useState<any>(
    tabData.reduce((acc: any, item) => {
      acc[item.key] = item.key;
      return acc;
    }, {})
  );
  const [columnHeight, setColumnHeight] = useState(168);
  const [loading, setLoading] = useState(false);
  const timerRef = useRef<any>();
  const columnBoxRef = useRef<any>(null);
  const location: any = useLocation();
  const { tenantNo: currentTenantNo, id: taskId } = location.query;

  const progress = useMemo(() => (taskData?.status === 2 ? 100 : taskData?.progress || 0), [taskData]);

  const hanldeQueryTaskDetail = async () => {
    try {
      const { label: step, process } = tabData[0];
      setLoading(true);
      const res = await queryTaskDetail({ tenantNo: currentTenantNo, taskId: id || taskId, step });
      const { metricsDTO, list, task } = res?.data?.value || {};
      setStepExecData((preState: any) => ({ ...(preState || {}), [step]: list }));
      setMetricsData(metricsDTO);
      if (list?.length && !task?.progress) {
        task.progress = process;
      }
      setTaskData(task);
      if (task?.progress === 100 || task?.status === 2) {
        clearTimeout(timerRef.current);
        return;
      }
      timerRef.current = setTimeout(() => {
        clearTimeout(timerRef.current);
        hanldeQueryTaskDetail();
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    hanldeQueryTaskDetail();
    return () => {
      clearTimeout(timerRef.current);
    };
  }, [id, taskId, currentTenantNo]);

  const batchHanldeQueryStepExec = async () => {
    const steps = tabData
      .filter((item) => item.label !== '数据加载' && item.process <= progress && !stepExecData[item.label])
      .map((item) => item.label);
    if (!steps?.length) return;
    const promises = steps.map((step) => queryStepExec({ tenantNo: currentTenantNo, taskId: id || taskId, step }));
    const resList = await Promise.all(promises);
    const data = resList
      .map((item, index) => ({ list: item?.data?.value?.list, step: steps[index] }))
      .reduce((acc: any, item: any) => {
        acc[item.step] = item.list?.length ? item.list : null;
        return acc;
      }, {});
    setStepExecData((preState: any) => ({ ...(preState || {}), ...data }));
  };

  useEffect(() => {
    batchHanldeQueryStepExec();
  }, [progress]);

  const columnData = useMemo(() => {
    return columnList.map(({ key, type, ...other }) => ({
      ...other,
      type,
      value:
        (progress === 100 || type !== '新规则') && metricsData?.[key]
          ? Number(metricsData[key].replace('%', '') || 0)
          : undefined,
    }));
  }, [metricsData, progress]);

  const tabItems = useMemo(() => {
    return tabData.map((item, index) => {
      const text = stepExecData?.[item.label]?.map((item: { execRes: string }) => item.execRes)?.join('\n');
      const isFinish = item.process <= progress;
      return {
        ...item,
        icon: <i>{index + 1}</i>,
        title: (
          <Collapse
            collapsible={'icon'}
            activeKey={isFinish ? activeKeyData[item.key] : undefined}
            items={[
              {
                showArrow: false,
                key: item.key,
                label: item.label,
                children: <CodeMirror key={item.key} className={styles.codeMirror} value={text} isScroll={false} />,
                extra: isFinish && (
                  <Button
                    type="link"
                    onClick={() =>
                      setActiveKeyData((preState: any) => ({
                        ...preState,
                        [item.key]: preState[item.key] ? undefined : item.key,
                      }))
                    }
                  >
                    {activeKeyData[item.key] ? '收起' : '展开'}
                  </Button>
                ),
              },
            ]}
          />
        ),
      };
    });
  }, [stepExecData, progress, activeKeyData]);

  const columnProps = useMemo<any>(
    () => ({
      xField: 'name',
      yField: 'value',
      seriesField: 'type',
      data: columnData,
      color: ({ type }: { type?: string }) => (type === '新规则' ? '#53B1FD' : '#D0D5DD'),
      meta: { value: { formatter: (v: string) => (typeof v !== 'undefined' ? `${v}%` : undefined) } },
      maxColumnWidth: 32,
      yAxis: false,
      dodgePadding: 4,
      isGroup: true,
      columnStyle: { radius: [4, 4, 0, 0] },
      tooltip: { showTitle: false },
      legend: {
        layout: 'horizontal',
        position: 'top-right',
        offsetY: -5,
        itemSpacing: 10,
      },
      height: columnHeight,
    }),
    [JSON.stringify(columnData), columnHeight]
  );

  const handleResize = () => {
    columnBoxRef.current && setColumnHeight(columnBoxRef.current.offsetHeight - 32);
  };

  const debouncedHandleResize = debounce(handleResize, 250);

  useEffect(() => {
    debouncedHandleResize();
    window.addEventListener('resize', debouncedHandleResize);

    return () => {
      window.removeEventListener('resize', debouncedHandleResize);
    };
  }, []);

  return (
    <PageLayout style={{ background: 'transparent' }}>
      <Spin spinning={loading}>
        <div className={styles.container}>
          <div className={styles.left}>
            <div className={styles.result}>
              <h1 className={styles.title}>
                <span>
                  <ResultContrast />
                  结果对比
                </span>
              </h1>
              <div className={styles.columnBox} ref={columnBoxRef}>
                <Column {...columnProps} />
              </div>
            </div>
            <div className={styles.rule}>
              <h1 className={styles.title}>
                <span>
                  <RuleContrast />
                  规则对比
                </span>
              </h1>
              <div className={styles.ruleContent}>
                <div className={styles.ruleData}>
                  <div className={styles.header}>
                    <Tag className={styles.blue}>新规则</Tag>
                    {!!taskData.conclusion && (
                      <Tag color={taskData.conclusion === '采纳' ? 'success' : 'error'}>{taskData.conclusion}</Tag>
                    )}
                  </div>
                  <h1>{metricsData?.newRuleName}</h1>
                  {metricsData?.newRuleDesc ? <p>{metricsData.newRuleDesc}</p> : <Empty />}
                </div>
                <div className={styles.ruleData}>
                  <div className={styles.header}>
                    <Tag>原规则</Tag>
                  </div>
                  <h1>{metricsData?.originRuleName}</h1>
                  {metricsData?.originRuleDesc ? <p>{metricsData.originRuleDesc}</p> : <Empty />}
                </div>
              </div>
            </div>
          </div>
          <div className={styles.right}>
            <h1 className={styles.title}>
              <span>
                <OptimizationProcess />
                优化过程
              </span>
              <div className={styles.progressBox}>
                <span>优化进度</span>
                <span style={{ marginLeft: 8 }}>{progress}%</span>
                <Progress
                  className={styles.progress}
                  percent={progress}
                  strokeColor={antConfig.theme.token.colorPrimary}
                />
              </div>
            </h1>
            <div className={styles.rightContent}>
              <Steps
                direction="vertical"
                items={tabItems}
                current={
                  tabData?.findIndex(({ process }) => process === progress) +
                  (tabData?.findIndex(({ process }) => process === progress) === tabData.length - 1 ? 0 : 1)
                }
              />
            </div>
          </div>
        </div>
      </Spin>
    </PageLayout>
  );
};

export default RuleOptimizeDetail;
