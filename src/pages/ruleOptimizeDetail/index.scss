.container {
  display: flex;
  flex-direction: row;
  height: 100%;
  overflow: hidden;

  .title {
    border-bottom: 1px solid rgba(225, 225, 238, 0.5);
    padding: 0 20px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #1d2129;
    font-size: 16px;
    margin: 0;
    font-weight: 500;
    & > span:first-child {
      display: flex;
      align-items: center;
      svg {
        width: 20px;
        margin-right: 8px;
      }
    }
  }

  .left {
    width: calc(100% - 58% - 12px);
    display: flex;
    flex-direction: column;

    .result {
      background: #fff;
      border-radius: 8px;
      height: 30%;
      min-height: 200px;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .columnBox {
        padding: 16px 20px;
        overflow: hidden;
        flex: 1;
      }
    }

    .rule {
      flex: 1;
      margin-top: 12px;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .ruleContent {
        flex: 1;
        padding: 20px;
        overflow-y: auto;

        .ruleData {
          &:not(:last-child) {
            border-bottom: 1px solid #e5e6eb;
            box-sizing: border-box;
            margin-bottom: 16px;
            padding-bottom: 16px;
          }
          .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .blue {
              background: #e8f7ff !important;
              color: #3491fa !important;
            }
            :global {
              .ant-tag {
                &:first-child {
                  border: none;
                }
                background: #f2f3f5;
                color: #0e121b;
                font-size: 14px;
                padding: 0 8px;
                height: 24px;
                line-height: 24px;
                font-weight: 500;
                margin-right: 0;
                box-sizing: border-box;
                &.ant-tag-error {
                  color: #d02533;
                  border-color: #ffc0c5;
                  background: #ffebec;
                  border-radius: 20px;
                }
                &.ant-tag-success {
                  color: #178c4e;
                  border-color: #c2f5da;
                  background: #e0faec;
                  border-radius: 20px;
                }
              }
            }
          }
          h1 {
            font-size: 14px;
            line-height: 14px;
            color: #4e5969;
            margin: 16px 0 8px;
            font-weight: 500;
          }
          p {
            color: #1d2129;
            font-size: 14px;
            line-height: 20px;
            white-space: pre-wrap;
            word-break: break-all;
            margin: 0;
          }
        }
      }

      :global {
        .ant-empty {
          margin-top: 10px;
        }
      }
    }
  }

  .right {
    width: 58%;
    margin-left: 12px;
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;

    .rightContent {
      flex: 1;
      overflow-y: auto;

      :global {
        .ant-steps {
          padding: 20px;
          .ant-steps-item {
            &:last-child {
              .ant-collapse-content-box {
                padding-bottom: 0 !important;
              }
            }
            .ant-steps-icon {
              top: 0;
              font-size: 12px;
              line-height: 12px;
              i {
                font-style: normal;
                font-weight: 500;
              }
            }
            .ant-steps-item-icon {
              width: 18px;
              height: 18px;
              line-height: 18px;
              margin-top: 2px;
              margin-right: 12px;
              border-radius: 100%;
              text-align: center;
              inset-inline-start: 0;
            }
            .ant-steps-item-tail {
              inset-inline-start: 0;
              top: 8px !important;
              padding: 18px 0 12px !important;
              &::after {
                margin-inline-start: 8.5px !important;
                background-color: #e5e6eb !important;
              }
            }
            &.ant-steps-item-wait,
            &.ant-steps-item-active:not(:last-child) {
              .ant-steps-item-icon {
                background: #fff;
                padding-top: 1.5px;
                margin-top: 0;
              }
              .ant-steps-icon {
                width: 15px;
                height: 15px;
                background-color: #fff !important;
                border-radius: 100%;
                border: 1px solid #e5e6eb;
                box-sizing: border-box;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                i {
                  display: none;
                }
                &::after {
                  content: '';
                  display: inline-block;
                  width: 8px;
                  height: 8px;
                  background-color: #e5e6eb;
                  border-radius: 100%;
                }
              }
            }
            &.ant-steps-item-active:not(:last-child) {
              .ant-steps-icon::after {
                background-color: var(--primary-color) !important;
              }
            }
            &.ant-steps-item-finish,
            &.ant-steps-item-active:last-child {
              .ant-steps-item-icon {
                background-color: #f2f2fe;
              }
            }
          }
          .ant-steps-item-title {
            padding: 0;
            width: 100%;
            .ant-collapse {
              border: none;
              background: transparent;
              .ant-collapse-header {
                padding: 0;
                align-items: center;
                height: 23px;
                .ant-collapse-header-text {
                  color: #0e121b;
                  font-size: 14px;
                  font-weight: 500;
                  line-height: 14px;
                }
                .ant-collapse-extra {
                  .ant-btn {
                    border: none;
                    padding: 0;
                    font-weight: normal;
                    height: initial;
                    line-height: 14px;
                    font-size: 14px;
                  }
                }
              }
              .ant-collapse-content {
                border: none;
                .ant-collapse-content-box {
                  padding: 12px 0 20px;
                }
              }
            }
          }
        }
      }
    }
  }

  .codeMirror {
    :global {
      .CodeMirror {
        height: initial;
        border-radius: 8px;
        .CodeMirror {
          height: auto !important;
          overflow: hidden !important;
        }
        .CodeMirror-scroll {
          background: #f8f8f8;
          color: #1d2129;
          font-size: 14px;
          font-weight: 400;
          overflow: hidden !important;
          .CodeMirror-lines {
            padding: 12px 0;
            .CodeMirror-line {
              padding: 0 10px;
            }
          }
        }
      }
    }
  }

  .progressBox {
    font-size: 14px;
    color: #4e5969;
    display: flex;
    align-items: center;
    white-space: nowrap;
    line-height: 14px;
    .progress {
      width: 80px;
      margin-left: 8px;
      margin-top: -4px;
      :global {
        .ant-progress-inner {
          .ant-progress-bg {
            height: 6px !important;
          }
        }
        .ant-progress-text {
          display: none;
        }
      }
    }
  }
}

.ruleOptimizeDrawer {
  margin-top: var(--header-height);

  &.ruleOptimizeIframeDrawer {
    margin-top: 0;

    :global {
      .ant-drawer-content-wrapper {
        .ant-drawer-body {
          & > div {
            &:last-child {
              min-height: calc(100vh - 57px) !important;
            }
          }
          .scroll-wrapper {
            height: calc(100vh - 57px) !important;
          }
        }
      }
    }
  }

  :global {
    .ant-drawer-content-wrapper {
      box-shadow: none !important;
      .ant-drawer-header {
        display: none;
      }
      .ant-drawer-body {
        background: #eff1f4;
        padding: 0 16px 16px;
        overflow: hidden;
        & > div {
          margin: 0;

          &:last-child {
            min-height: calc(100vh - var(--header-height) - 57px) !important;
          }
        }
        .scroll-wrapper {
          overflow: hidden;
          height: calc(100vh - var(--header-height) - 57px) !important;
        }
      }
    }
  }
}
