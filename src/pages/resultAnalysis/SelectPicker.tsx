import { initialValues, timeOptions } from '@/hooks/useStrategyRuleSearch';
import { RANGE_PRESETS } from '@/utils/constants';
import { DatePicker, Form, Select } from 'antd';
import React, { FC } from 'react';
import styles from './index.scss';

const { RangePicker } = DatePicker;

const SelectPicker: FC<any> = (props) => {
  const { value = {}, onChange, pickerDefaultValue } = props;

  const handleSelectChange = (val?: string) => {
    onChange({ ...value, timeType: val });
  };

  const handleRangeChange = (dates?: any) => {
    onChange({ ...value, timeRange: dates });
  };

  return (
    <Form.Item
      className={styles.ruleTimeFormItem}
      label={
        <Select
          options={timeOptions}
          value={value.timeType}
          onChange={handleSelectChange}
          defaultValue={initialValues.timeType}
        />
      }
    >
      <RangePicker
        style={{ width: '100%' }}
        presets={RANGE_PRESETS}
        value={value.timeRange}
        onChange={handleRangeChange}
        defaultValue={pickerDefaultValue}
        allowClear={false}
      />
    </Form.Item>
  );
};

export default SelectPicker;
