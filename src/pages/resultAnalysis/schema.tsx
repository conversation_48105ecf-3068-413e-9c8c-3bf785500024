import { getAllTreeList, getFirstLevelKeys } from '@/utils';
import { latestXDay, SESSION_TYPE_OPTIONS } from '@/utils/constants';
import { TreeSelect } from 'antd';
import classNames from 'classnames';
import React from 'react';

const { SHOW_CHILD } = TreeSelect;

//默认最近一个星期
export const currentDayStartSecond = latestXDay(7)[0]; // 00:00:00
export const currentDayEndSecond = latestXDay(7)[1]; // 23:59:59

// 状态枚举, 0: 失败, 1: 成功
export const statusEnum = [
  { label: '已完成', value: 1 },
  { label: '未完成', value: 0 },
];

const getSchema = (groupIdList = []) => ({
  type: 'object',
  labelWidth: 90,
  properties: {
    strategyIds: {
      title: '策略名称',
      type: 'string',
      widget: 'treeSelect',
      placeholder: '请选择策略名称',
      span: 8,
      props: {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        maxTagCount: 'responsive',
        treeData: getAllTreeList(groupIdList, { titleKey: 'strategyName', valueKey: 'id' }) || [],
        treeDefaultExpandedKeys: getFirstLevelKeys(groupIdList, { titleKey: 'strategyName', valueKey: 'id' }),
        fieldNames: { value: 'id', label: 'strategyName', children: 'children' },
        treeTitleRender: (node: any) => (
          <span className={classNames({ red: node?.isRedLine === '1' })}>{node?.strategyName}</span>
        ),
        filterTreeNode: (input: any, option: any) => {
          return (option?.strategyName ?? '').toLowerCase().includes(input.toLowerCase());
        },
      },
    },
    sessionId: {
      title: '会话ID',
      type: 'string',
      span: 8,
      placeholder: '请输入会话ID',
      widget: 'input',
    },
    timeData: {
      span: 8,
      type: 'object',
      'ui:widget': 'selectPicker',
      props: {
        pickerDefaultValue: [currentDayStartSecond, currentDayEndSecond],
      },
    },
    sessionTypes: {
      title: '会话类型',
      type: 'string',
      widget: 'select',
      placeholder: '请选择会话类型',
      span: 8,
      props: {
        options: SESSION_TYPE_OPTIONS,
        mode: 'multiple',
      },
    },
  },
});
export default getSchema;
