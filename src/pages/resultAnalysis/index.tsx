import { ReactComponent as IconExport } from '@/assets/export.svg';
import PageLayout from '@/components/PageLayout';
import TableRender from '@/components/TableRender';
import ConsultRecordDrawer from '@/pages/consultRecord/Drawer';
import { getImgSrc } from '@/pages/strategyConfig';
import { getStraConfigList, queryListConfigNewColumns, updateFeedbackColumns } from '@/services/ce';
import { DislikeOutlined, LikeOutlined } from '@ant-design/icons';
import { useLocation, useSelector } from '@magi/magi';
import { Button, Image, message, Modal, Spin, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep, initial } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import { exportCallRecords, getSearchParams, searchApi } from './request';
import getSchema from './schema';
import SelectPicker from './SelectPicker';

const displayType = 'sessionQueryList';

const ResultAnalysis = () => {
  const location: any = useLocation();
  const { currentTenantNo: _currentTenantNo } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = _currentTenantNo || location?.query?.tenantNo;
  const [columnsSettingValue, setColumnsSettingValue] = useState<any>([]); // 列设置
  const [pagination, setPagination] = useState<{ current: number; pageSize: number }>({
    current: 1,
    pageSize: 10,
  });
  const [columns, setColumns] = useState([]); // 列
  const [total, setTotal] = useState(0);
  const [list, setList] = useState([]);
  const [groupIdList, setGroupIdList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [confirmModalVisible, setConfirmModal] = useState(false);
  const [confirmModalLoading, setConfirmModalLoading] = useState(false);
  const tableRef = useRef<any>();
  const searchDataRef = useRef<any>({});
  const [consultDrawerData, setConsultDrawerData] = useState<any>({ open: false, id: null, serviceId: null });
  const [listData, setListData] = useState<any[]>([]);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    getSelectColumns();
    localStorage.setItem('currentTenantNo', currentTenantNo);
  }, []);

  const onListChange = ({ list, total }: any) => {
    setList(list);
    setTotal(total);
  };

  useEffect(() => {
    if (!currentTenantNo) {
      return;
    }
    getStraConfigList({
      tenantNo: currentTenantNo,
    }).then((res) => {
      if (res.data.success) {
        const data = res.data.value.map((item: any) => ({ label: item.groupName, value: item.id, ...item })) || [];
        setGroupIdList(data);
      }
    });
  }, [currentTenantNo]);

  const formatColumns = (columns: any) => {
    const _columns = cloneDeep(columns);
    _columns.push(actionRow);
    setColumnsSettingValue(_columns);

    // 列具体配置
    _columns.forEach((item: any) => {
      if (item.dataIndex !== 'action') {
        if (['seatName'].includes(item.dataIndex)) {
          item.width = 100;
        } else if (['strategyName', 'sessionId'].includes(item.dataIndex)) {
          item.width = 300;
        } else if (['sessionType'].includes(item.dataIndex)) {
          item.width = 240;
        } else {
          item.width = 200;
        }
        item.render = (text: string) => (
          <Typography.Paragraph style={{ margin: 0, wordBreak: 'break-all' }} ellipsis={{ rows: 2, tooltip: text }}>
            {text}
          </Typography.Paragraph>
        );
      }
      if (item.dataIndex === 'feedbackResult') {
        item.render = (text: number) => {
          return text === 1 ? (
            <LikeOutlined style={{ color: 'blue', fontSize: '16px' }} />
          ) : (
            <DislikeOutlined style={{ color: 'red', fontSize: '16px' }} />
          );
        };
      }
      if (['responseContext', 'requestContext', 'sensitiveWords', 'errMsg'].includes(item.dataIndex)) {
        item.width = '300px';
        item.align = 'left';
        item.render = (text: any) => {
          const imgSrc = getImgSrc(text);
          return imgSrc ? (
            <Image height={58} src={imgSrc} />
          ) : (
            <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text }}>
              {item.dataIndex === 'sensitiveWords' ? text?.join(',') || '' : text}
            </Typography.Paragraph>
          );
        };
      }
      if (item.dataIndex === 'modelType') {
        item.render = (text = []) => text.join(',');
      }
      if (['startTime', 'endTime', 'gmtModified'].includes(item.dataIndex)) {
        item.width = 180;
        item.render = (text: string) => {
          // console.log('-----startTime---', text)
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        };
      }

      if (item.dataIndex === 'feedbackTag') {
        item.render = (text: string) => {
          return text
            ? text.split(',').map((item) => (
                <Tag color="processing" key={item}>
                  {item}
                </Tag>
              ))
            : null;
        };
      }
      if (item.dataIndex === 'success') {
        item.render = (status: boolean) => {
          if (status === true) {
            return '是';
          } else if (status === false) {
            return '否';
          }
          return '未知';
        };
      }
    });
    return _columns;
  };

  const getSelectColumns = async () => {
    const params = {
      tenantNo: currentTenantNo,
      displayType,
    };
    setLoading(true);
    queryListConfigNewColumns(params)
      .then((res) => {
        if (res.data.success) {
          setColumns(
            formatColumns(
              res.data.value.map((item: any) => {
                return {
                  ...item,
                  key: item.fieldCode,
                  title: item.fieldName,
                  dataIndex: item.fieldCode,
                  sort: item.sort,
                  hidden: item.hidden === 'N' ? false : true,
                };
              })
            )
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const actionRow = {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 80,
    fixed: 'right',
    render: (_: any, record: any, index: number) => {
      // console.log('------actionRow----', record, record?.sessionId)
      return (
        <Button
          onClick={() => {
            setConsultDrawerData({ open: true, id: record?.sessionId, serviceId: record?.serviceId, curIndex: index });
          }}
          type="link"
          style={{ padding: 0 }}
        >
          详情
        </Button>
      );
    },
  };

  const onColumnsSettingChange = (setting: any) => {
    const removeActionList = initial(setting);
    setColumnsSettingValue([...removeActionList, actionRow]);
    const displayFieldList: any[] = [];
    removeActionList.forEach((item: any) => {
      // debugger;
      columns.forEach((column: any) => {
        if (item.key === column.key) {
          displayFieldList.push({
            // fixed: item.fixed
            fieldCode: column.dataIndex,
            fieldName: column?.title,
            fieldNo: column.key,
            sort: column?.sort,
            hidden: item?.hidden ? 'Y' : 'N',
          });
        }
      });
    });
    updateFeedbackColumns({
      displayFieldList: displayFieldList.map((item, index) => ({ ...item, sort: index + 1 })),
      displayType,
      tenantNo: currentTenantNo,
    });
  };

  const handleConfirmModal = () => {
    setConfirmModal(true);
  };

  const onExportCallRecords = async () => {
    if (tableRef) {
      setConfirmModalLoading(true);
      const formData = {
        ...getSearchParams({ ...(searchDataRef.current || {}) }),
        tenantNo: currentTenantNo,
        displayFieldList: columnsSettingValue
          ?.filter((item: any) => item.fieldCode && item.hidden !== true)
          ?.map((item: any) => ({
            fieldCode: item.fieldCode,
            fieldName: item.fieldName,
          })),
      };
      try {
        messageApi.open({
          type: 'info',
          content: '开始下载，可能需要一段时间，请耐心等待~',
          duration: 10,
        });
        const res = await exportCallRecords(formData);
        if (res === true) {
          messageApi.destroy();
          messageApi.open({
            type: 'success',
            content: '下载完成!',
            duration: 3,
          });
          setConfirmModal(false);
        }
      } catch {
        messageApi.destroy();
        messageApi.open({
          type: 'success',
          content: '下载失败!',
          duration: 3,
        });
      } finally {
        setConfirmModalLoading(false);
      }
    }
  };

  return (
    <PageLayout>
      <Spin spinning={loading}>
        <div>
          <TableRender
            rowKey={'sessionId'}
            ref={tableRef}
            setPageData={setPagination}
            pageData={pagination}
            search={{ schema: getSchema(groupIdList), widgets: { selectPicker: SelectPicker } }}
            request={(...args) => {
              return searchApi(...args, {
                onListChange,
                setPagination,
                getSearchParams: (v: any) => (searchDataRef.current = v),
                setListData,
              });
            }}
            columns={columns}
            toolbarAction={{
              columnsSettingValue,
              onColumnsSettingChange,
            }}
            title={
              <Button type="primary" ghost icon={<IconExport style={{ marginTop: 1 }} />} onClick={handleConfirmModal}>
                导出
              </Button>
            }
          />
          {contextHolder}
        </div>
        <Modal
          title="导出确认"
          open={confirmModalVisible}
          onOk={onExportCallRecords}
          onCancel={() => {
            setConfirmModal(false);
          }}
          destroyOnClose
          confirmLoading={confirmModalLoading}
        >
          <p>当前共{total}条数据，是否导出？</p>
        </Modal>
      </Spin>
      <ConsultRecordDrawer listData={listData} drawerData={consultDrawerData} setDrawerData={setConsultDrawerData} />
    </PageLayout>
  );
};

export default ResultAnalysis;
