import { getQualityTaskResultList } from '@/services/ce';
import { fetchQueryList } from '@/utils';
import { QUERY_PAGE_SIZE } from '@/utils/constants';
import dayjs from 'dayjs';
import { currentDayEndSecond, currentDayStartSecond } from './schema';

const PREFIX = '/ce';

export const getSearchParams = (config: any) => {
  const { timeData } = config || {};
  const { timeType } = timeData || {};
  let startTime = config?.startTime || config?.timeRange?.[0] || timeData?.timeRange?.[0] || currentDayStartSecond;
  let endTime = config?.endTime || config?.timeRange?.[1] || timeData?.timeRange?.[1] || currentDayEndSecond;
  startTime && (startTime = `${dayjs(startTime).format('YYYY-MM-DD')} 00:00:00`);
  endTime && (endTime = `${dayjs(endTime).format('YYYY-MM-DD')} 23:59:59`);
  return {
    tenantNo: localStorage.getItem('currentTenantNo'),
    [timeType === '2' ? 'qualityStartTime' : 'startTime']: startTime && Date.parse(dayjs(startTime) as any),
    [timeType === '2' ? 'qualityEndTime' : 'endTime']: endTime && Date.parse(dayjs(endTime) as any),
    strategyIds: config?.strategyIds,
    sessionId: config?.sessionId,
    sessionTypes: config?.sessionTypes?.length ? config?.sessionTypes : undefined,
  };
};

export const searchApi = async (
  { current: pageNum, pageSize, ...restParams }: { current: number; pageSize: number; [key: string]: any },
  sorter,
  tabInfo,
  params
) => {
  const searchParams = getSearchParams(restParams);
  const requestParams: any = {
    pageNum,
    pageSize,
    ...searchParams,
  };
  fetchQueryList(requestParams, getQualityTaskResultList, QUERY_PAGE_SIZE).then((data) => {
    data && params.setListData(data || []);
  });
  const res = await getQualityTaskResultList(requestParams);
  params.getSearchParams({ ...searchParams, timeData: restParams.timeData });
  params.onListChange({ list: res?.data?.value?.list, total: res?.data?.value?.total });
  params.setPagination({
    current: pageNum,
    pageSize,
  });
  const obj = {
    data: res?.data?.value?.list,
    total: res?.data?.value?.total,
  };
  return obj;
};

/**
 * 导出调用日志
 * @param {*} data
 * @returns
 */
// /api/v1/qualityTaskResult/download
export const exportCallRecords = (data = {}) => {
  // 写成fetch获取文件流,并且post
  return fetch(`${PREFIX}/api/v1/qualityTaskResult/download `, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      'X-Usercenter-Session': localStorage.getItem('ATLANTIS_SESSION_ID') || '',
    },
  })
    .then((res) => {
      res.blob().then((blob) => {
        let link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        // 昵称为

        link.download = `调用日志-${dayjs().format('YYYY-MM-DD-HH:mm:ss')}.xlsx`;
        link.click();
      });
      return true;
    })
    .catch((e) => {
      console.log('下载失败:', e);
      return false;
    });
};
