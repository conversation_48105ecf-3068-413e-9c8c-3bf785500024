import IconChat from '@/assets/icon-chat.svg';
import IconHeadphones from '@/assets/icon-headphones.svg';
import IconPhone from '@/assets/icon-phone.svg';
import PageLayout from '@/components/PageLayout';
import Table from '@/components/Table';
import { getRuleList } from '@/hooks/useStrategyRuleSearch';
import ConsultRecordDrawer from '@/pages/consultRecord/Drawer';
import {
  getStraConfigList as handleStraConfigList,
  queryRealTimeData,
  queryRealTimeQualityResultPage,
} from '@/services/ce';
import { getAllTreeList, getFirstLevelKeys } from '@/utils';
import { antConfig } from '@/utils/constants';
import tableFilter from '@/utils/tableFilter';
import { SyncOutlined } from '@ant-design/icons';
import { useLocation } from '@magi/magi';
import { Button, Col, Form, Row, Select, Statistic, StatisticProps, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import CountUp from 'react-countup';
import styles from './index.scss';

const formatter: StatisticProps['formatter'] = (value) => <CountUp end={Number(value || 0)} separator="," />;

const selectOptions = [
  {
    label: '1分钟',
    value: 60000,
  },
  {
    label: '5分钟',
    value: 300000,
  },
  {
    label: '10分钟',
    value: 600000,
  },
];

const STATUS_ENUM = {
  0: '进行中',
  1: '已结束',
};

const RealHitResult = () => {
  const [pagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 10,
    searchParams: {},
  });
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [baseInfoLists, setBaseInfoLists] = useState<any>({});
  const [autoRefresh, setAutoRefresh] = useState(60000);
  const [consultDrawerData, setConsultDrawerData] = useState({ open: false, id: null });
  const [currentTime, setCurrrentTime] = useState('');
  const [straConfigList, setStraConfigList] = useState<any>([]);
  const timerRef = useRef<any>(null);
  const isOnlyRefreshListRef = useRef(false);
  const searchInput = useRef(null);
  const [form] = Form.useForm();
  const location: any = useLocation();
  const { tenantNo: currentTenantNo } = location.query;

  const searchParams = useMemo(() => pagination.searchParams, [JSON.stringify(pagination.searchParams || {})]);

  const ruleList = useMemo(
    () => getRuleList(searchParams?.strategyIdList || [], straConfigList),
    [searchParams?.strategyIdList, straConfigList]
  );

  const handleGetStrategyByGroupId = useCallback(async () => {
    if (!currentTenantNo) return;
    handleStraConfigList({
      tenantNo: currentTenantNo,
    }).then((res) => {
      if (res.data.success) {
        const data = res.data.value.map((item: any) => ({ label: item.strategyName, value: item.id, ...item })) || [];
        setStraConfigList(data);
      }
    });
  }, [currentTenantNo]);

  useEffect(() => {
    handleGetStrategyByGroupId();
  }, [handleGetStrategyByGroupId]);

  const handleQueryRealTimeData = useCallback(async () => {
    const res = await queryRealTimeData({
      tenantNo: currentTenantNo,
    });
    if (res.data.success) {
      const data = res.data.value;
      setBaseInfoLists(data);
    }
  }, [currentTenantNo]);

  const handleQuery = useCallback(
    async (pagination: any) => {
      setLoading(true);
      const { current, pageSize, searchParams } = pagination || {};
      try {
        const params = {
          pageNum: current,
          pageSize,
          tenantNo: currentTenantNo,
          ...(searchParams || {}),
        };
        const res = await queryRealTimeQualityResultPage(params);
        if (res.data.success) {
          const data = res.data.value;
          const { list = [], total } = data || {};
          setData(list?.map((item: any, index: number) => ({ ...item, key: `${index}-${pagination.current}` })));
          setTotal(total);
        }
      } finally {
        setLoading(false);
      }
    },
    [currentTenantNo]
  );

  const handleAutoRefreshQuery = useCallback(() => {
    clearTimeout(timerRef.current);
    const refresh = async () => {
      setCurrrentTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
      const promises = isOnlyRefreshListRef.current
        ? [handleQuery(pagination)]
        : [handleQueryRealTimeData(), handleQuery(pagination)];
      await Promise.all(promises);
      isOnlyRefreshListRef.current = false;
      if (autoRefresh) {
        timerRef.current = setTimeout(refresh, autoRefresh);
      }
    };
    refresh();
  }, [autoRefresh, pagination, handleQuery, handleQueryRealTimeData]);

  useEffect(() => {
    handleAutoRefreshQuery();
    return () => {
      clearTimeout(timerRef.current);
    };
  }, [handleAutoRefreshQuery]);

  const dataList = useMemo(() => {
    const createDataItem = (key: number) => [
      {
        title: '会话量',
        icon: IconPhone,
        value: baseInfoLists?.[key]?.sessionCount,
      },
      {
        title: '违规会话量',
        icon: IconHeadphones,
        value: baseInfoLists?.[key]?.violationCount,
      },
      {
        title: '违规消息数',
        icon: IconChat,
        value: baseInfoLists?.[key]?.violationMsgCount,
      },
    ];

    return [
      {
        label: '今日数据',
        datas: createDataItem(1),
      },
      {
        label: '实时数据',
        datas: createDataItem(0),
      },
    ];
  }, [JSON.stringify(baseInfoLists || {})]);

  const columns = useMemo<any[]>(() => {
    return [
      {
        title: '会话类型',
        dataIndex: 'sessionType',
        width: 100,
      },
      {
        title: '会话ID',
        dataIndex: 'sessionId',
        width: 250,
      },
      {
        title: (
          <span style={searchParams.strategyIdList?.length ? { color: antConfig.theme.token.colorPrimary } : {}}>
            策略名称
          </span>
        ),
        dataIndex: 'strategyName',
        width: 180,
        ...tableFilter({
          form,
          searchParams,
          searchInput,
          refresh: (val) => {
            isOnlyRefreshListRef.current = true;
            setPagination((preState: any) => ({
              ...preState,
              current: 1,
              searchParams: { ...preState.searchParams, strategyIdList: val, ruleIdList: undefined },
            }));
          },
          dataIndex: 'strategyIdList',
          fieldType: 'treeSelect',
          enums: getAllTreeList(straConfigList, { titleKey: 'label' }) || [],
          treeDefaultExpandedKeys: getFirstLevelKeys(straConfigList, { titleKey: 'label' }),
        }),
      },
      {
        title: (
          <span style={searchParams.ruleIdList?.length ? { color: antConfig.theme.token.colorPrimary } : {}}>
            规则名称
          </span>
        ),
        dataIndex: 'ruleName',
        width: 180,
        ...tableFilter({
          form,
          searchParams,
          searchInput,
          refresh: (val) => {
            isOnlyRefreshListRef.current = true;
            setPagination((preState: any) => ({
              ...preState,
              current: 1,
              searchParams: { ...preState.searchParams, ruleIdList: val },
            }));
          },
          dataIndex: 'ruleIdList',
          fieldType: 'treeSelect',
          enums: getAllTreeList(ruleList, { titleKey: 'label' }) || [],
          treeDefaultExpandedKeys: getFirstLevelKeys(ruleList, { titleKey: 'label' }),
        }),
      },
      {
        title: '关键词',
        dataIndex: 'sensitiveWord',
        width: 180,
      },
      {
        title: '句子原文',
        dataIndex: 'textContent',
        width: 400,
        render: (text: string) => (
          <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text?.replace(/[AB]-/g, '') }}>
            {text?.replace(/[AB]-/g, '')}
          </Typography.Paragraph>
        ),
      },
      {
        title: <span style={searchParams.seatName ? { color: antConfig.theme.token.colorPrimary } : {}}>客服姓名</span>,
        dataIndex: 'seatName',
        width: 120,
        ...tableFilter({
          form,
          searchParams,
          searchInput,
          refresh: (val) => {
            isOnlyRefreshListRef.current = true;
            setPagination((preState: any) => ({
              ...preState,
              current: 1,
              searchParams: { ...preState.searchParams, seatName: val },
            }));
          },
          dataIndex: 'seatName',
          fieldType: 'input',
        }),
      },
      {
        title: '会话开始时间',
        dataIndex: 'startTime',
        width: 180,
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        render: (text: keyof typeof STATUS_ENUM) =>
          !!STATUS_ENUM[text] && (
            <Tag className={styles.tag} color={text === 0 ? 'success' : 'default'}>
              <span className={styles.statusPoint} style={{ background: text === 0 ? '#1FC16B' : '#717784' }}></span>
              {STATUS_ENUM[text]}
            </Tag>
          ),
      },
      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        render: (_: unknown, record: any) => (
          <Button
            style={{ padding: 0 }}
            type="link"
            onClick={() => setConsultDrawerData({ open: true, id: record?.sessionId })}
          >
            查看
          </Button>
        ),
      },
    ];
  }, [form, searchParams, straConfigList, ruleList]);

  return (
    <PageLayout>
      <div className={styles.container}>
        <div className={styles.autoRefresh}>
          <span className={styles.autoRefreshText}>于 {currentTime} 刷新</span>
          <Select
            style={{ width: 75 }}
            options={selectOptions}
            value={autoRefresh}
            onChange={setAutoRefresh}
            size="small"
          />
          <Button onClick={handleAutoRefreshQuery} type="link" loading={loading} icon={<SyncOutlined />} />
        </div>
        <Row gutter={24} className={styles.dataList}>
          {dataList.map(({ label, datas }) => (
            <Col key={label} xxl={12} xl={12} lg={12} md={24} sm={24} xs={24}>
              <h1>{label}</h1>
              <Row gutter={[8, 24]} className={styles.dataListItem}>
                {datas.map(({ title, value, icon }) => (
                  <Col key={title} span={8}>
                    <div className={styles.dataListItemCon}>
                      <img src={icon} />
                      <div className={styles.dataListItemText}>
                        <Statistic value={value} formatter={formatter} />
                        <span>{title}</span>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </Col>
          ))}
        </Row>
        <Form form={form}>
          <Table
            rowKey={'key'}
            loading={loading}
            style={{ marginTop: 24 }}
            pagination={{
              total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            }}
            onChange={(newPagination) => {
              isOnlyRefreshListRef.current = true;
              setPagination({
                current: newPagination.pageSize !== pagination.pageSize ? 1 : newPagination.current || 1,
                pageSize: newPagination.pageSize || 10,
              });
            }}
            columns={columns}
            dataSource={data}
          />
        </Form>
      </div>
      <ConsultRecordDrawer
        drawerData={consultDrawerData}
        setDrawerData={setConsultDrawerData}
        currentTenantNo={currentTenantNo}
        isRealTime
      />
    </PageLayout>
  );
};

export default RealHitResult;
