.container {
  padding: 10px 20px 20px;
  position: relative;

  .autoRefresh {
    position: absolute;
    right: 20px;
    top: 15px;
    display: flex;
    align-items: center;
    z-index: 1;

    .autoRefreshText {
      font-size: 14px;
      color: #86909c;
      margin-right: 8px;
    }

    :global {
      .ant-btn {
        width: initial;
        border: none;
        margin-left: 8px;
      }
    }
  }

  .dataList {
    h1 {
      font-size: 16px;
      color: #1d2129;
      margin: 10px 0 16px;
      font-weight: 500;
    }

    .dataListItem {
      :global {
        .ant-statistic {
          line-height: 32px;
          .ant-statistic-content-value {
            font-size: 28px;
            line-height: 32px;
            font-weight: 700;
            color: #1d2129;
            font-family: 'DIN Alternate';
            word-break: break-all;
          }
        }
      }

      .dataListItemCon {
        background: #f8f8f8;
        border-radius: 6px;
        min-height: 92px;
        display: flex;
        align-items: center;
        overflow: hidden;
        padding: 0 12px;
      }

      img {
        width: 40px;
        height: 40px;
        margin-right: 10px;
      }

      .dataListItemText {
        display: flex;
        flex-direction: column;

        & > span {
          font-size: 14px;
          color: #4e5969;
          word-break: break-all;
        }
      }
    }
  }

  .tag {
    display: inline-flex !important;
    flex-direction: row;
    align-items: center;
    border-radius: 90px;
    font-size: 12px;
    font-weight: 500;

    .statusPoint {
      width: 8px;
      height: 8px;
      border-radius: 100%;
      margin-right: 8px;
    }

    &:global(.ant-tag-default) {
      color: #525866;
      border-color: #e1e4ea;
      background: #f5f7fa;
    }

    &:global(.ant-tag-success) {
      color: #1fc16b;
      border-color: #c2f5da;
      background: #e0faec;
    }
  }
}
