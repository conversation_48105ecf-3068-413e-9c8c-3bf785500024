import { ReactComponent as IconExport } from '@/assets/export.svg';
import Form, { useForm } from '@/components/Form';
import PageLayout from '@/components/PageLayout';
import Table from '@/components/Table';
import useStrategyRuleSearch, { initialValues } from '@/hooks/useStrategyRuleSearch';
import ConsultRecordDrawer from '@/pages/consultRecord/Drawer';
import { PREFIX } from '@/services';
import { getHitResult } from '@/services/ce';
import { download, fetchQueryList, getDateTimes } from '@/utils';
import { latestXDay, QUERY_PAGE_SIZE } from '@/utils/constants';
import { useLocation, useSelector } from '@magi/magi';
import { Button, Divider, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { cloneDeep } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import Highlighter from 'react-highlight-words';
import styles from './index.scss';

const HitResult = (props: any) => {
  const [form] = useForm();
  const location: any = useLocation();
  const { currentTenantNo: cTenantNo } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = props.currentTenantNo || cTenantNo || location.query?.tenantNo;
  const { currentData, searchData } = props;
  const { columns: strategyRuleSearchList, strategyList } = useStrategyRuleSearch({
    form,
    tenantNo: currentTenantNo,
    showColumns: ['time', 'rule'],
    isTimeType: true,
  });
  const [strategyIdList, setStrategyIdList] = useState<number[]>();
  const [loading, setLoading] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [data, setData] = useState([]);
  const [startDate, setStartDate] = useState<Dayjs | null>(latestXDay(7)[0]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState(0);
  const [consultDrawerData, setConsultDrawerData] = useState<any>({ open: false, id: null });
  const [currentParams, setCurrentParams] = useState({});
  const [listData, setListData] = useState<any[]>([]);

  const columns: any[] = [
    {
      title: '商机ID',
      dataIndex: 'serviceId',
      width: 200,
      render: (text: string) => (
        <Typography.Paragraph style={{ margin: 0, wordBreak: 'break-all' }} ellipsis={{ rows: 2, tooltip: text }}>
          {text}
        </Typography.Paragraph>
      ),
    },
    {
      title: '会话ID',
      dataIndex: 'sessionId',
      width: 240,
      render: (text: string) => (
        <Typography.Paragraph style={{ margin: 0, wordBreak: 'break-all' }} ellipsis={{ rows: 2, tooltip: text }}>
          {text}
        </Typography.Paragraph>
      ),
    },
    {
      title: '命中规则',
      dataIndex: 'ruleName',
      width: 400,
      render: (text: string) => (
        <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text }}>
          {text}
        </Typography.Paragraph>
      ),
    },
    {
      title: '命中策略',
      dataIndex: 'strategyName',
      width: 400,
      render: (text?: string) => (
        <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 2, tooltip: text }}>
          {strategyIdList?.length ? (
            <Highlighter
              highlightStyle={{ backgroundColor: 'transparent', color: 'red', padding: 0 }}
              searchWords={
                strategyList
                  ?.filter(({ id }: { id: number }) => strategyIdList?.includes(id))
                  ?.map(({ strategyName }: { strategyName: string }) => strategyName) || []
              }
              autoEscape
              textToHighlight={text ? text.toString() : ''}
            />
          ) : (
            text
          )}
        </Typography.Paragraph>
      ),
    },
    {
      title: '会话开始时间',
      dataIndex: 'startTime',
      width: 180,
    },
    {
      title: '质检完成时间',
      dataIndex: 'qualityTime',
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      fixed: 'right',
      render: (_: any, record: any, index: number) => {
        return (
          <Button
            onClick={() => {
              setConsultDrawerData({
                open: true,
                id: record?.sessionId,
                serviceId: record?.serviceId,
                curIndex: index,
              });
            }}
            type="link"
            style={{ padding: 0 }}
          >
            详情
          </Button>
        );
      },
    },
  ];

  const handleHitResult = async (params: { [key: string]: any }) => {
    setLoading(true);
    const res = await getHitResult({
      tenantNo: currentTenantNo,
      ...params,
    });
    if (res.data.success) {
      const data = res.data.value;
      const { list = [], total } = data || {};
      setStrategyIdList(params?.strategyIdList);
      setData(list);
      setTotal(total);
    }
    setLoading(false);
  };

  const handleQuery = async (pagination: any) => {
    let timer;
    clearTimeout(timer);
    setPagination(pagination);
    let fromValues: any = {};
    if (!currentData) {
      const fields = cloneDeep(form.getFieldsValue());
      if (fields.time?.length === 2) {
        fields.time = getDateTimes(fields.time);
      }
      fromValues = {
        groupIdList: fields.groupIdList,
        strategyIdList: fields.strategyIdList,
        ruleIdList: fields.ruleIdList,
      };
      if (fields.timeType === '2') {
        fromValues.qualityStartTime = fields.time[0] && Date.parse(dayjs(fields.time[0]) as any);
        fromValues.qualityEndTime = fields.time[1] && Date.parse(dayjs(fields.time[1]) as any);
      } else {
        fromValues.startTime = fields.time[0];
        fromValues.endTime = fields.time[1];
      }
    } else {
      setData([]);
      const { time, groupIdList, strategyIdList, ruleIdList, timeType, queryType, sessionTypes } = searchData || {};
      const newTime = getDateTimes(time);
      const { strategyId, groupId, ruleId, seatName, sessionType } = currentData;
      fromValues = {
        groupIdList: groupId ? [groupId] : groupIdList?.length ? groupIdList : undefined,
        strategyIdList: strategyId ? [strategyId] : strategyIdList?.length ? strategyIdList : undefined,
        ruleIdList: ruleId ? [ruleId] : ruleIdList?.length ? ruleIdList : undefined,
        sessionTypes: sessionType ? [sessionType] : sessionTypes?.length ? sessionTypes : undefined,
        seatName,
        type: 1,
        queryType,
      };
      if (timeType === '2') {
        fromValues.qualityStartTime = newTime?.[0] && Date.parse(dayjs(newTime[0]) as any);
        fromValues.qualityEndTime = newTime?.[1] && Date.parse(dayjs(newTime[1]) as any);
      } else {
        fromValues.startTime = newTime?.[0];
        fromValues.endTime = newTime?.[1];
      }
    }
    setCurrentParams(fromValues);
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...fromValues,
    };
    await handleHitResult(params);
    if (params.pageNum === 1) {
      timer = setTimeout(() => {
        fetchQueryList({ tenantNo: currentTenantNo, ...params }, getHitResult, QUERY_PAGE_SIZE).then((data) => {
          data && setListData(data || []);
        });
      }, 5000);
    }
  };

  useEffect(() => {
    handleQuery(pagination);
  }, []);

  const exportResult = () => {
    const fields = cloneDeep(form.getFieldsValue());
    if (fields.time?.length === 2) {
      fields.time = getDateTimes(fields.time);
    }
    setDownloadLoading(true);
    const params = {
      tenantNo: currentTenantNo,
      ...(currentParams || {}),
    };
    download(`${PREFIX}/api/v1/qualityTask/download`, params, `质检结果.xlsx`).finally(() => {
      setDownloadLoading(false);
    });
  };

  const formColumns = useMemo(() => {
    return strategyRuleSearchList?.map((item: any) => {
      if (item.name === 'time') {
        return {
          ...item,
          disabledDate: (current: dayjs.Dayjs) => {
            if (!startDate) return false;
            const maxDays = 30;
            const maxDate = dayjs(startDate).add(maxDays, 'day');
            const minDate = dayjs(startDate).subtract(maxDays, 'day');
            return current && (current > maxDate || current < minDate);
          },
          onCalendarChange: (dates: dayjs.Dayjs[]) => setStartDate(dates?.[0]),
          onChange: (dates: dayjs.Dayjs[]) => setStartDate((dates as Dayjs[])?.[0]),
        };
      }
      return item;
    });
  }, [strategyRuleSearchList, startDate]);

  return (
    <PageLayout>
      <div className={styles.container}>
        {!currentData && (
          <>
            <Form
              form={form}
              initialValues={{
                ...initialValues,
                time: latestXDay(7),
              }}
              columns={formColumns}
              loading={loading}
              onReset={() => handleQuery({ ...pagination, current: 1 })}
              onQuery={() => handleQuery({ ...pagination, current: 1 })}
            />
            <Divider style={{ margin: '0 0 16px' }} />
          </>
        )}
        <Button
          onClick={exportResult}
          icon={<IconExport style={{ marginTop: 1 }} />}
          type="primary"
          ghost
          loading={downloadLoading}
        >
          导出
        </Button>
        <Table
          rowKey={'sessionId'}
          loading={loading}
          style={{ marginTop: 20 }}
          pagination={{
            total,
            current: pagination.current,
            pageSize: pagination.pageSize,
          }}
          onChange={(newPagination) => {
            setPagination({
              current: newPagination.pageSize !== pagination.pageSize ? 1 : newPagination.current || 1,
              pageSize: newPagination.pageSize || 10,
            });
          }}
          columns={columns}
          dataSource={data}
        />
      </div>
      <ConsultRecordDrawer
        mask={!!currentData}
        rootClassName={currentData && styles.consultRecordDrawer}
        drawerData={consultDrawerData}
        setDrawerData={setConsultDrawerData}
        currentTenantNo={currentTenantNo}
        listData={listData?.length ? listData : data}
      />
    </PageLayout>
  );
};

export default HitResult;
