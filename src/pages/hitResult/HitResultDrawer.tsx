import { drawerWidth } from '@/utils/constants';
import { Drawer } from 'antd';
import React, { FC } from 'react';
import HitResult from './index';
import styles from './index.scss';

interface IProps {
  drawerData: {
    open: boolean;
    data: null;
  };
  setDrawerData: React.Dispatch<
    React.SetStateAction<{
      open: boolean;
      data: null;
    }>
  >;
  currentTenantNo: string;
  searchData?: { [key: string]: any };
}

const HitResultDrawer: FC<IProps> = ({ drawerData, setDrawerData, currentTenantNo, searchData }) => {
  return (
    <Drawer
      maskClosable={false}
      title={'详情'}
      width={drawerWidth}
      open={drawerData.open}
      push={false}
      rootClassName={styles.hitResultDrawer}
      onClose={() => setDrawerData({ open: false, data: null })}
    >
      {drawerData.open && (
        <HitResult currentTenantNo={currentTenantNo} currentData={drawerData.data} searchData={searchData} />
      )}
    </Drawer>
  );
};

export default HitResultDrawer;
