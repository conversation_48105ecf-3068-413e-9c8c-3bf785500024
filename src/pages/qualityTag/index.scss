.container {
  padding: 20px;

  :global {
    .ant-col {
      margin-top: 10px;
      .ant-form-item {
        margin-bottom: 0;
        .ant-col {
          margin: 0;
          .ant-input-affix-wrapper {
            padding: 6px 11px;
          }
        }
      }
    }
    .ant-empty {
      margin: 50px auto;
    }
  }
  .desc {
    color: #86909c;
    font-size: 12px;
    margin: 0 0 24px;
  }
  .displayForm {
    background-color: #eff1f4;
    color: #1d2129;
    font-size: 14px;
    padding: 8px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    .labelText {
      flex: 1;
    }
    .editBtn {
      padding: 0;
      color: #4e5969;
      display: none;
      height: initial;
      border: none;
      svg {
        width: 16px;
        height: 16px;
      }
    }
    &:hover {
      background-color: #f2f2fe;
      .editBtn {
        display: flex;
      }
    }
  }
}
