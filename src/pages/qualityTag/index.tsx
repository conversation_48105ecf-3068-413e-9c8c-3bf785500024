import { ReactComponent as EditIcon } from '@/assets/edit.svg';
import Empty from '@/components/Empty';
import PageLayout from '@/components/PageLayout';
import useRobotData from '@/hooks/useRobotData';
import { getLabelList, putLabel } from '@/services/ce';
import { customIsEmpty } from '@/utils';
import { useDispatch, useSelector } from '@magi/magi';
import { Button, Col, Form, Input, Row, Spin } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.scss';

const QualityTag = () => {
  const { labelList, currentTenantNo } = useSelector((state: { global: any }) => state.global);
  const dispatch = useDispatch();
  const { isRedLineRobot } = useRobotData();
  const [form] = Form.useForm();
  const [newLabelList, setNewLabelList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const isEditing = useMemo(() => !!newLabelList.some((item: any) => item.isEdit), [newLabelList]);

  useEffect(() => {
    setNewLabelList([...(labelList?.sort((a: any, b: any) => b.sort - a.sort) || [])]);
  }, [labelList]);

  const fetchLabelList = useCallback(async () => {
    setLoading(true);
    getLabelList(currentTenantNo)
      .then((res: any) => {
        const list = (res.data.value || []).map((item: any) => {
          return {
            ...item,
            isRedLine: isRedLineRobot ? '1' : undefined,
            value: item.id,
            label: item.labelName,
          };
        });
        dispatch({
          type: 'global/save',
          payload: {
            labelList: list,
            ...(isRedLineRobot ? { redLabelList: list } : {}),
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, [currentTenantNo, dispatch, isRedLineRobot]);

  const handlePutLabel = useCallback(
    async (labelName: string, id?: number, sort?: number) => {
      try {
        setLoading(true);
        const res = await putLabel({
          tenantNo: currentTenantNo,
          labelName,
          id,
          sort,
        });
        if (res.data.success) {
          fetchLabelList();
        } else {
          setLoading(false);
        }
      } catch {
        setLoading(false);
      }
    },
    [fetchLabelList, currentTenantNo]
  );

  const editLabel = useCallback((item: any) => {
    form.setFieldsValue({
      label: item.label || '',
    });
    setNewLabelList((preState) => {
      if (customIsEmpty(item.value)) {
        return [{ isEdit: true }, ...preState];
      }
      return [...(preState || [])].map((i: any) => {
        if (i.value === item.value) {
          return {
            ...i,
            isEdit: true,
          };
        }
        return i;
      });
    });
  }, []);

  const onInputCancel = useCallback((item: any) => {
    setNewLabelList((preState) => {
      if (customIsEmpty(item.value)) {
        return preState.filter((item: any) => !item.isEdit);
      }
      return preState.map((i: any) => (i.isEdit ? { ...i, isEdit: false } : i));
    });
  }, []);

  const onInputSave = useCallback(
    async (item: any) => {
      const value = await form.validateFields();
      handlePutLabel(value.label, item.value, item.sort ?? newLabelList[1]?.sort + 1);
    },
    [handlePutLabel, newLabelList]
  );

  return (
    <PageLayout>
      <Spin spinning={loading}>
        <div className={styles.container}>
          <p className={styles.desc}>
            质检标签可关联到质检结果，可以在配置规则时绑定，也可在对质检结果进行标注时进行选择。
          </p>
          <Button type="primary" onClick={editLabel} disabled={isEditing}>
            + 添加标签
          </Button>
          <Form form={form} clearOnDestroy>
            <Row gutter={24}>
              {newLabelList?.map((item: any, index: number) => {
                return (
                  <Col xs={24} sm={12} md={8} lg={8} xl={6} key={item.value ?? `add-${index}`}>
                    {item.isEdit ? (
                      <div style={{ display: 'flex' }}>
                        <Form.Item
                          name={'label'}
                          rules={[{ required: true, message: '请输入标签' }]}
                          style={{ flex: 1 }}
                        >
                          <Input autoFocus placeholder="请输入" maxLength={8} showCount />
                        </Form.Item>
                        <Button
                          size="small"
                          type="primary"
                          style={{ marginLeft: 8, height: 36 }}
                          onClick={() => onInputSave(item)}
                        >
                          保存
                        </Button>
                        <Button size="small" style={{ marginLeft: 8, height: 36 }} onClick={() => onInputCancel(item)}>
                          取消
                        </Button>
                      </div>
                    ) : (
                      <div className={styles.displayForm}>
                        <span className={styles.labelText}>{item.label}</span>
                        <Button
                          type="link"
                          className={styles.editBtn}
                          disabled={isEditing}
                          onClick={() => editLabel(item)}
                        >
                          <EditIcon />
                        </Button>
                      </div>
                    )}
                  </Col>
                );
              })}
              {!newLabelList.length && <Empty />}
            </Row>
          </Form>
        </div>
      </Spin>
    </PageLayout>
  );
};

export default QualityTag;
