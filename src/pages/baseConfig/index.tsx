import { ReactComponent as IconDisplay } from '@/assets/displayIcon.svg';
import PageLayout from '@/components/PageLayout';
import Table from '@/components/Table';
import { createField, getFieldList, updateFieldStatus } from '@/services/ce';
import { EFieldsType } from '@/utils/constants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useLocation, useSelector } from '@magi/magi';
import { Button, Form, Input, message, Modal, Popconfirm, Select, Switch, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.scss';

let editId = Date.now();

const BaseConfig = () => {
  const location: any = useLocation();
  const { currentTenantNo: _currentTenantNo } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = _currentTenantNo || location?.query?.tenantNo;
  const [displayForm] = Form.useForm();
  const [pagination, setPagination] = useState<{ current: number; pageSize: number }>(() => {
    return {
      current: 1,
      pageSize: 10,
    };
  });
  const [list, setList] = React.useState<IFieldConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [displayModalData, setDisplayModalData] = useState<{ visible: boolean; data?: any }>({ visible: false });
  const [loadingDisplaySubmit, setLoadingDisplaySubmit] = useState(false);

  const handleAdd = () => {
    const newRow: IFieldConfig = {
      showName: '',
      sourceFieldCode: '',
      storageCode: '',
      targetFieldCode: '',
      index: list.length + 1,
      editing: true,
      id: editId++,
      creator: '',
      fieldClassify: 0,
      fieldType: EFieldsType.String,
      gmtCreated: '',
      gmtModified: '',
      isDeleted: '',
      modifier: '',
      showStatus: 1,
      status: 1,
      tenantNo: currentTenantNo,
      tokenizer: 0,
    };
    const newList = [...list, newRow];
    const newPageNum = Math.ceil(newList.length / pagination.pageSize);
    setPagination({
      ...pagination,
      current: newPageNum,
    });
    setList(newList);
    setIsEditing(true);
  };

  const getList = async (currentTenantNo: string) => {
    const res = await getFieldList(currentTenantNo);

    if (res?.data?.value?.length > 0) {
      const list: IFieldConfig[] = res.data.value;
      return list.map((item, index) => {
        return {
          ...item,
          index: index + 1,
        };
      });
    }
    return [];
  };

  const save = async (currentTenantNo: string, list: IFieldConfig[]) => {
    const editingRowIndex = list.findIndex((row) => !!row.editing);
    const editingRow = list[editingRowIndex];
    const obj: ICreateFieldConfig = {
      showName: editingRow.showName,
      sourceFieldCode: editingRow.sourceFieldCode,
      status: editingRow.status,
      showStatus: editingRow.showStatus,
      targetFieldCode: editingRow.targetFieldCode,
      fieldType: editingRow.fieldType,
      tenantNo: currentTenantNo,
      tokenizer: editingRow.tokenizer,
    };
    const hasEmptyValue = Object.values(obj).filter((v) => v === '' || v === undefined || v === null).length > 0;
    if (hasEmptyValue) {
      message.error('字段没有完全填写');
      return;
    }
    if (!editingRow) {
      return;
    }
    setLoading(true);

    const res = await createField(obj);
    if (!res.data.success) {
      setLoading(false);
    } else {
      const newList = await getList(currentTenantNo);
      setList(newList);
      setIsEditing(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    const run = async () => {
      if (!currentTenantNo) {
        return;
      }
      setLoading(true);
      const list = await getList(currentTenantNo);
      setList(list);
      setLoading(false);
    };
    run();
  }, [currentTenantNo]);

  const handleEditField = (record: IFieldConfig, field: string, value: any) => {
    const newList = list.map((item) => {
      if (item.id === record.id) {
        const obj = {
          ...item,
          [field]: value,
        };
        if (field === 'fieldType') {
          obj.tokenizer = 0;
        }
        return obj;
      }
      return item;
    });
    setList(newList);
  };

  const columns = useMemo(() => {
    const columns = [
      {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        sorter: (a: any, b: any) => a.index - b.index,
      },
      {
        title: '展示名称',
        dataIndex: 'showName',
        key: 'showName',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) {
            return (
              <Input
                maxLength={32}
                onInput={(e: any) => {
                  handleEditField(record, 'showName', e.target.value);
                }}
                value={record.showName}
              />
            );
          }
          return text;
        },
      },
      {
        title: '存储字段名',
        dataIndex: 'targetFieldCode',
        key: 'targetFieldCode',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) {
            return (
              <Input
                maxLength={32}
                onInput={(e: any) => {
                  handleEditField(record, 'targetFieldCode', e.target.value);
                }}
                value={record.targetFieldCode}
              />
            );
          }
          return text;
        },
      },
      {
        title: '外部原始字段',
        dataIndex: 'sourceFieldCode',
        key: 'sourceFieldCode',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) {
            return (
              <Input
                maxLength={32}
                onInput={(e: any) => {
                  handleEditField(record, 'sourceFieldCode', e.target.value);
                }}
                value={record.sourceFieldCode}
              />
            );
          }
          return text;
        },
      },
      {
        title: '数据格式',
        dataIndex: 'fieldType',
        key: 'fieldType',
        render: (text: number, record: IFieldConfig) => {
          if (record.editing) {
            return (
              <Select
                options={[
                  {
                    label: '字符串',
                    value: EFieldsType.String,
                  },
                  {
                    label: '日期',
                    value: EFieldsType.Date,
                  },
                  {
                    label: '数字',
                    value: EFieldsType.Number,
                  },
                  {
                    label: '列表',
                    value: EFieldsType.List,
                  },
                ]}
                value={record.fieldType}
                onChange={(e) => handleEditField(record, 'fieldType', e)}
              />
            );
          }
          switch (text) {
            case EFieldsType.String:
              return '字符串';
            case EFieldsType.Date:
              return '日期';
            case EFieldsType.Number:
              return '数字';
            case EFieldsType.List:
              return '列表';
            default:
              return '';
          }
        },
      },
      {
        title: '是否展示',
        dataIndex: 'displayConfig',
        key: 'displayConfig',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) return '';
          text = typeof text === 'string' ? JSON.parse(text) : text;
          const isDisposition = Object.values(text || {}).some((v) => v === '1');
          const disabled = record.fieldClassify === 0 ? true : false;
          return (
            <Button
              className={styles.displayButton}
              style={{ ...(isDisposition ? {} : { color: '#86909C' }), ...(disabled ? { opacity: 0.65 } : {}) }}
              type="link"
              disabled={disabled}
              onClick={() => {
                displayForm.setFieldsValue(
                  Object.fromEntries(Object.entries(text || {}).map(([key, value]) => [key, value === '1']))
                );
                setDisplayModalData({
                  visible: true,
                  data: {
                    ...record,
                    disabled,
                  },
                });
              }}
            >
              <IconDisplay />
              {isDisposition ? '已配置' : '未配置'}
            </Button>
          );
        },
      },
      {
        title: '查询方式',
        dataIndex: 'tokenizer',
        key: 'tokenizer',
        render: (text: number, record: IFieldConfig) => {
          if (record.editing) {
            return (
              <Select
                disabled={record.fieldType !== EFieldsType.String}
                options={[
                  {
                    label: '模糊匹配',
                    value: 1,
                  },
                  {
                    label: '精确匹配',
                    value: 0,
                  },
                ]}
                value={record.tokenizer}
                onChange={(e) => handleEditField(record, 'tokenizer', e)}
              />
            );
          }
          return !!text ? '模糊匹配' : '精确匹配';
        },
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) return '';
          return text;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreated',
        key: 'gmtCreated',
        sorter: (a: any, b: any) => new Date(a.gmtCreated).getTime() - new Date(b.gmtCreated).getTime(),
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) return '';
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '更新人',
        dataIndex: 'modifier',
        key: 'modifier',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) return '';
          return text;
        },
      },
      {
        title: '更新时间',
        dataIndex: 'gmtModified',
        key: 'gmtModified',
        sorter: (a: any, b: any) => new Date(a.gmtModified).getTime() - new Date(b.gmtModified).getTime(),
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) return '';
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        render: (_: string, record: IFieldConfig) => {
          if (record.editing) {
            return (
              <div>
                <Button onClick={() => save(currentTenantNo, list)} style={{ marginRight: 8, padding: 0 }} type="link">
                  保存
                </Button>
                <Popconfirm
                  title="确定取消吗?"
                  onConfirm={() => {
                    const newList = list.filter((item) => item.id !== record.id);
                    setList(newList);
                    setIsEditing(false);
                  }}
                >
                  <Button type="link" style={{ padding: 0 }} danger>
                    取消
                  </Button>
                </Popconfirm>
              </div>
            );
          }
          return (
            <Switch
              checkedChildren="启用"
              unCheckedChildren="停用"
              onChange={(e) => {
                const status = e ? 1 : 0;
                const newList = list.map((item) => {
                  if (item.id === record.id) {
                    return {
                      ...item,
                      status,
                    };
                  }
                  return item;
                });
                setList(newList);
                updateFieldStatus({ id: record.id, status });
              }}
              value={!!record.status}
              defaultValue={!!record.status}
              disabled={record.fieldClassify === 0 ? true : false}
            />
          );
        },
      },
    ];
    return columns.map((item: any) => {
      // 通过配置 给每个单元格添加不换行属性
      const fun = () => ({ style: { whiteSpace: 'nowrap' } });
      item.onHeaderCell = fun;
      item.onCell = fun;
      return item;
    });
  }, [list, currentTenantNo]);

  const onDisplayCancel = useCallback(() => {
    setDisplayModalData({ visible: false });
    displayForm.resetFields();
  }, [displayForm]);

  const onDisplaySubmit = useCallback(async () => {
    const values = await displayForm.validateFields();
    setLoadingDisplaySubmit(true);
    const displayConfig = Object.fromEntries(Object.entries(values).map(([key, value]) => [key, value ? '1' : '0']));
    try {
      await updateFieldStatus({
        id: displayModalData.data?.id,
        displayConfig: JSON.stringify(displayConfig),
      });
      setList((preState) =>
        [...(preState || [])].map((item) => (item.id === displayModalData.data?.id ? { ...item, displayConfig } : item))
      );
      onDisplayCancel();
    } finally {
      setLoadingDisplaySubmit(false);
    }
  }, [displayForm, displayModalData, onDisplayCancel]);

  if (!currentTenantNo) {
    return null;
  }

  return (
    <PageLayout>
      <div className={styles.container}>
        <Button disabled={isEditing} onClick={handleAdd} style={{ marginBottom: 20 }} type="primary">
          新建
        </Button>
        <Table
          loading={loading}
          pagination={pagination}
          onChange={(pagination) => {
            const obj = {
              current: pagination.current || 1,
              pageSize: pagination.pageSize || 10,
            };
            setPagination(obj);
          }}
          columns={columns}
          dataSource={list}
        />
      </div>
      <Modal
        title={`${displayModalData.data?.showName || ''}-展示配置`}
        open={displayModalData.visible}
        onCancel={onDisplayCancel}
        onOk={onDisplaySubmit}
        okButtonProps={{ loading: loadingDisplaySubmit }}
        footer={displayModalData.data?.disabled ? null : undefined}
        width={450}
      >
        <Form form={displayForm} disabled={displayModalData.data?.disabled} className={styles.displayForm}>
          {[
            {
              label: (
                <>
                  展示到会话查询列表
                  <Tooltip title="开启后，在【会话查询】页面列表中展示此字段">
                    <QuestionCircleOutlined
                      style={{ fontSize: 14, color: '#BABEC4', marginLeft: 4, cursor: 'pointer' }}
                    />
                  </Tooltip>
                </>
              ),
              name: 'sessionQueryList',
            },
            {
              label: (
                <>
                  展示到质检详情页
                  <Tooltip title="开启后，在【质检结果详情】页面右侧的质检结果-基本信息中展示此字段">
                    <QuestionCircleOutlined
                      style={{ fontSize: 14, color: '#BABEC4', marginLeft: 4, cursor: 'pointer' }}
                    />
                  </Tooltip>
                </>
              ),
              name: 'qualityDetails',
            },
            {
              label: (
                <>
                  添加到结果标注查询条件
                  <Tooltip title="开启后，在【结果标注查询】页面新增此字段为查询条件，根据此字段查询方式决定查询条件是模糊查询还是精确查询">
                    <QuestionCircleOutlined
                      style={{ fontSize: 14, color: '#BABEC4', marginLeft: 4, cursor: 'pointer' }}
                    />
                  </Tooltip>
                </>
              ),
              name: 'resultTagQuery',
              disabled: displayModalData.data?.fieldType !== EFieldsType.String,
            },
          ].map(({ label, name, disabled }) => (
            <Form.Item key={name} label={label} name={name} valuePropName="checked">
              <Switch disabled={disabled} />
            </Form.Item>
          ))}
        </Form>
      </Modal>
    </PageLayout>
  );
};

export default BaseConfig;
