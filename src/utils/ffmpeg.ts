(window as any)._silk2arraybuftag = 20000;
(window as any)._silk2arraybufurl2filename = {};
(window as any)._silk2arraybufurl2tag = {};

const { FFmpeg } = (window as any).FFmpegWASM || {};
const ffmpeg = FFmpeg && new FFmpeg();

const toBlobURL = async (url: string, mimeType: string) => {
  const buf = await (await fetch(url)).arrayBuffer();
  const blob = new Blob([buf], { type: mimeType });
  return URL.createObjectURL(blob);
};

export const getFile = (filename: string) => {
  const _ = () => {
    return new Promise((res, rej) => {
      setTimeout(() => {
        try {
          const data = (window as any).FS.readFile(filename);
          console.log('转码完成');
          res(data);
        } catch (error) {
          console.log('还没转码完成');
          _();
        }
      }, 1000);
    });
  };
  return _();
};

export const handlesilk = async (url: string) => {
  (window as any)._silk2arraybuftag++;
  const silk2arraybuftag = (window as any)._silk2arraybuftag;
  (window as any)._silk2arraybufurl2tag[url] = silk2arraybuftag;
  (window as any).Module._silk2arraybuf((window as any).Module.allocateUTF8(url));
  const pcmdata: any = await getFile(`${silk2arraybuftag}.pcm`);
  await ffmpeg.writeFile(`pcm_${silk2arraybuftag}.pcm`, pcmdata);
  await ffmpeg.exec([
    '-y',
    '-f',
    's16le',
    '-ar',
    '24000',
    '-ac',
    '1',
    '-i',
    `pcm_${silk2arraybuftag}.pcm`,
    `wav${silk2arraybuftag}.wav`,
  ]);
  const data: any = await ffmpeg.readFile(`wav${silk2arraybuftag}.wav`);
  return URL.createObjectURL(new Blob([data.buffer], { type: 'audio/wav' }));
};

// initffmpeg的代码
export const initFFmpeg = async () => {
  if (!ffmpeg) return;
  console.log('initFFmpeg start');
  const baseURL = 'https://one.zhongan.com/sh/qastatic';
  await ffmpeg.load({
    coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
    wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
  });
  console.log('initFFmpeg end');
};

export const isSilkAudio = async (url: string) => {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Range: 'bytes=0-1023', // 获取前1KB的数据
      },
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const buffer = await response.arrayBuffer();
    const text = new TextDecoder('utf-8').decode(buffer);
    // 转换为小写并检查是否包含'silk'
    return text.toLowerCase().includes('silk');
  } catch (error) {
    console.error('Error checking SILK format:', error);
    return false;
  }
};
