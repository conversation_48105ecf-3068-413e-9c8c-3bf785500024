/**
 * 记录节点的父级
 * module_0@模块|menu_1@菜单2|menu_3@菜单2.2
 * @param {*} arr
 * @param {*} levelInfo //新增属性存放父级信息
 * @param {*} fieldNames
 */
function formatTree(arr: any[], levelInfo = '', fieldNames = { label: 'title', value: 'value', children: 'children' }) {
  return arr.map((item) => {
    const newParent = levelInfo
      ? levelInfo + '|' + item[fieldNames.value] + '@' + item[fieldNames.label]
      : '' + item[fieldNames.value] + '@' + item[fieldNames.label];
    const temp = {
      ...item,
      levelInfo: newParent,
    };
    if (item[fieldNames.children]) {
      temp[fieldNames.children] = formatTree(item[fieldNames.children], newParent, fieldNames);
    }
    return temp;
  });
}

/**
 * 去重对象数组
 * @param {*} array
 * @param {*} key
 */
function uniqueArr(array: any[], key?: string) {
  var arr = array;
  var n = [arr[0]];
  for (var i = 1; i < arr.length; i++) {
    if (key === undefined) {
      if (n.indexOf(arr[i]) === -1) n.push(arr[i]);
    } else {
      // eslint-disable-next-line
      inner: {
        var has = false;
        for (var j = 0; j < n.length; j++) {
          if (arr[i][key] === n[j][key]) {
            has = true;
            // eslint-disable-next-line
            break inner;
          }
        }
      }
      if (!has) {
        n.push(arr[i]);
      }
    }
  }
  return n;
}

/**
 * 遍历树，提供callback
 * @param {*} treeNodes
 * @param {*} cb
 * @param {*} level
 */
function loopTree(treeNodes: any[], cb?: (...args: any[]) => void, level = 0, params = {}) {
  if (!treeNodes || !treeNodes.length) return;
  const childrenKey = (params as any)?.childrenKey || 'children';
  for (var i = 0, len = treeNodes.length; i < len; i++) {
    let children = treeNodes[i][childrenKey];
    cb && cb(treeNodes[i], level);
    if (children && children.length > 0) {
      loopTree(children, cb, level + 1, params);
    }
  }
}

/**
 * 寻找节点
 * @param {*} treeNodes
 * @param {*} currentValue
 * @param {*} cb
 */
function findNode(treeNodes?: any[], currentValue?: string, cb?: (...args: any[]) => void) {
  if (!treeNodes || !treeNodes.length) return;

  for (var i = 0, len = treeNodes.length; i < len; i++) {
    let children = treeNodes[i].children;
    if (currentValue === treeNodes[i].value) {
      cb && cb([treeNodes[i]]);
      return;
    }
    if (children && children.length > 0) {
      findNode(children, currentValue, cb);
    }
  }
}

export default {
  findNode,
  formatTree,
  uniqueArr,
  loopTree,
};
