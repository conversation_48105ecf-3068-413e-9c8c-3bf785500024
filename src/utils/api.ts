import request from '../services';
import { ssoServiceName, userCenterHost } from './sso';

// ticket置换session接口
export const fetchSessionByTicket = (params: any) => {
  return request.get(`${userCenterHost()}/validate2?service=${params.service}&ticket=${params.ticket}`);
};

// sessionID置换用户信息接口
export const fetchUserInfoBySession = (params: any) => {
  return request.get(`${userCenterHost()}/userinfo?service=${params.service}`, {
    headers: {
      'X-Usercenter-Session': params.encryptedSession || localStorage.getItem('ATLANTIS_SESSION_ID') || '',
      'X-Service-Name': params.service || ssoServiceName, //申请得到的sso service
    },
  });
};
