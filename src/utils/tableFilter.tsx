import { selectSearch } from '@/utils';
import { antConfig } from '@/utils/constants';
import { FilterOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Form, FormInstance, Input, Select, Space, TreeSelect } from 'antd';
import classNames from 'classnames';
import React from 'react';

const { SHOW_CHILD } = TreeSelect;

interface IProps {
  form: FormInstance;
  refresh: (...argns: any[]) => void;
  searchParams: any;
  searchInput: any;
  dataIndex: string;
  fieldType: string;
  disabled?: boolean;
  enums?: any[];
  [key: string]: any;
}

const tableFilter = ({
  form,
  refresh,
  searchParams,
  searchInput,
  dataIndex,
  fieldType,
  disabled = false,
  enums,
  ...props
}: IProps) => {
  let inputValue: any = undefined;

  return {
    filterDropdown: ({ close, visible }: { close: () => void; visible: boolean }) => {
      inputValue = searchParams instanceof Function ? searchParams()[dataIndex] : searchParams[dataIndex];
      if (!visible) {
        form.setFieldsValue({
          [dataIndex]: inputValue,
        });
      }
      return (
        <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
          <Form.Item noStyle name={dataIndex}>
            {fieldType === 'treeSelect' ? (
              <TreeSelect
                disabled={disabled}
                ref={searchInput}
                treeData={enums}
                fieldNames={{ value: 'value', label: 'label', children: 'children' }}
                placeholder="请选择"
                style={{ marginBottom: 8, display: 'block' }}
                maxTagCount="responsive"
                showCheckedStrategy={SHOW_CHILD}
                treeCheckable
                treeTitleRender={(node: any) => (
                  <span className={classNames({ red: node?.isRedLine === '1' })}>{node?.label}</span>
                )}
                onChange={(value) => {
                  inputValue = value;
                }}
                filterTreeNode={selectSearch.filterOption}
                {...props}
              />
            ) : fieldType === 'select' ? (
              <Select
                disabled={disabled}
                ref={searchInput}
                options={enums}
                fieldNames={{ label: 'desc', value: 'value' }}
                placeholder="请选择"
                style={{ marginBottom: 8, display: 'block' }}
                onChange={(value) => {
                  inputValue = value;
                }}
              />
            ) : (
              <Input
                disabled={disabled}
                ref={searchInput}
                placeholder="请输入"
                style={{ marginBottom: 8, display: 'block' }}
                onPressEnter={(e) => {
                  // @ts-ignore
                  refresh(e?.target?.value || undefined);
                  close();
                }}
                onChange={(e) => {
                  inputValue = e?.target?.value || undefined;
                }}
              />
            )}
          </Form.Item>
          <Space>
            <Button
              disabled={disabled}
              type="primary"
              onClick={() => {
                refresh(inputValue);
                close();
              }}
              size="small"
              style={{ width: 90 }}
            >
              查询
            </Button>
            <Button
              disabled={disabled}
              onClick={() => {
                inputValue = undefined;
                form.setFieldsValue({ [dataIndex]: undefined });
              }}
              size="small"
              style={{ width: 90 }}
            >
              重置
            </Button>
            <Button disabled={disabled} type="link" size="small" onClick={() => close()}>
              关闭
            </Button>
          </Space>
        </div>
      );
    },
    filterIcon: () => {
      const data = searchParams instanceof Function ? searchParams()[dataIndex] : searchParams[dataIndex];
      const isChoose = Array.isArray(data) ? data.length > 0 : !!data;
      return (
        <>
          {['select', 'treeSelect'].includes(fieldType) ? (
            <FilterOutlined
              style={{
                color: isChoose ? antConfig.theme.token.colorPrimary : '#99A0AE',
              }}
            />
          ) : (
            <SearchOutlined
              style={{
                fontSize: '14px',
                color: isChoose ? antConfig.theme.token.colorPrimary : '#99A0AE',
              }}
            />
          )}
        </>
      );
    },
    onFilterDropdownOpenChange: (visible: boolean) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select?.(), 100);
      }
    },
  };
};

export default tableFilter;
