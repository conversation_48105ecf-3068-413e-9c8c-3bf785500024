import { message } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty as _isEmpty } from 'lodash';
import treeOperate from './treeOperate';

export const formatGroupTree = (_tree: any) => {
  const tree = cloneDeep(_tree);
  const map: any = {};
  treeOperate.loopTree(
    tree,
    (node: any) => {
      node.title = node.groupName;
      node.key = node.id;
      node.value = node.id;
      node.children = node.chidrenList;
      map[node.id] = node;
    },
    0,
    { childrenKey: 'chidrenList' }
  );
  return { tree, map };
};

export const download = (url: string, params: any, filename?: string) => {
  return fetch(url, {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      'X-Usercenter-Session': localStorage.getItem('ATLANTIS_SESSION_ID') || '',
    },
  })
    .then((res) => {
      res.blob().then((blob) => {
        let link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        !!filename && (link.download = filename);
        link.click();
      });
      return true;
    })
    .catch((e) => {
      console.log('下载失败:', e);
      return false;
    });
};

export const selectSearch = {
  showSearch: true,
  filterOption: (input: any, option: any) => {
    return (option?.label ?? '').toLowerCase().includes(input?.toLowerCase());
  },
};

const loadedMap: Record<string, boolean> = {};
export const loadScript = (url: string) => {
  if (loadedMap[url]) {
    return Promise.resolve();
  }
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = url;
    script.onload = () => {
      loadedMap[url] = true;
      resolve(true);
    };
    script.onerror = (e) => {
      reject(e);
    };
    document.body.appendChild(script);
  });
};

export const getNumFromPx = (str: string) => {
  return Number(str.replace('px', ''));
};

export const copy = (text: string) => {
  const tempTextArea = document.createElement('textarea');

  tempTextArea.value = text;
  document.body.appendChild(tempTextArea);
  tempTextArea.select();
  try {
    message.destroy();
    document.execCommand('copy');
    message.success('复制成功');
  } catch (err) {
    console.error('Failed to copy text: ', err);
    message.error('复制失败');
  }
  document.body.removeChild(tempTextArea);
};

export const numberToLetter = (number: number) => {
  // 使用模运算符 (%) 实现循环，确保 number 在 1 到 26 之间
  const normalizedNumber = ((number - 1) % 26) + 1;
  return String.fromCharCode(65 + normalizedNumber - 1);
};

export const convertToChineseNumber = (number: number) => {
  const chineseDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const units = ['', '十', '百', '千', '万', '亿'];

  let result = '';
  let numStr = number.toString();
  let length = numStr.length;

  for (let i = 0; i < length; i++) {
    let digit: any = numStr.charAt(i);
    let chineseDigit = chineseDigits[digit];
    let unit = units[length - i - 1];

    if (chineseDigit !== '零') {
      result += chineseDigit + unit;
    } else {
      // Avoid consecutive "零"
      if (result.charAt(result.length - 1) !== '零' && length - i - 1 !== 0) {
        result += chineseDigit;
      }
    }
  }

  // Remove trailing "零"
  if (result.charAt(result.length - 1) === '零') {
    result = result.slice(0, -1);
  }

  // Special case for numbers like "10", "20", "30", etc.
  if (number >= 10 && number < 20) {
    result = result.replace(/^一十/, '十');
  }

  return result;
};

export const getDateTimes = (timeData: any[]) => {
  const newTime = timeData?.map((item: any, index: number) => {
    let date;
    if (index === 0) {
      date = item?.format('YYYY-MM-DD 00:00:00');
    } else {
      date = item?.format('YYYY-MM-DD 23:59:59');
    }
    return dayjs(date)?.toDate();
  });

  return newTime;
};

export const disableSelectedOptions = (options: any[], selectedKeys: any[], noFilterRed?: boolean) => {
  return options
    ?.filter((option) => noFilterRed || option.isRedLine !== '1')
    ?.map((option) => {
      const isSelected = selectedKeys?.includes(option.value);
      const disabledOption = {
        ...option,
        disabled: isSelected,
      };
      if (option.children) {
        disabledOption.children = disableSelectedOptions(option.children, selectedKeys, noFilterRed);
      }
      return disabledOption;
    });
};

export const customIsEmpty = (value: any) => {
  if (typeof value === 'number') return false;
  return _isEmpty(value);
};

export const getAllTreeList = (tree?: any[], opts?: { titleKey?: string; valueKey?: string; chlidrenKey?: string }) => {
  if (!tree?.length || tree?.[0]?.[opts?.titleKey || 'title'] === '全部') return tree;
  return [
    {
      [opts?.titleKey || 'title']: '全部',
      [opts?.valueKey || 'value']: tree?.[0]?.parentId ?? '-1',
      [opts?.chlidrenKey || 'children']: tree,
    },
  ];
};

export const getFirstLevelKeys = (
  tree?: any[],
  opts?: { titleKey?: string; valueKey?: string; chlidrenKey?: string }
) => {
  const firstLevelKeys = getAllTreeList(tree, opts)?.[0]?.[opts?.valueKey || 'value'];
  return customIsEmpty(firstLevelKeys) ? undefined : [firstLevelKeys];
};

export const fetchQueryList = async (params: any, apiFetch: any, pageSize?: number) => {
  if (params.pageNum !== 1) return;
  const res = await apiFetch({ ...params, pageSize });
  return res?.data?.value?.list || [];
};

export const downloadFile = (url: string, fileName: string) => {
  const link = document.createElement('a');
  link.href = url?.startsWith('http:') ? url?.replace('http:', 'https:') : url;
  link.download = fileName;
  link.click();
};
