import locale from 'antd/lib/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import AiRule from '../assets/aiRule.svg';
import AiRuleTemplate from '../assets/aiRuleTemplate.svg';
import CeRule from '../assets/ceRule.svg';
import CustomRule from '../assets/customRule.svg';
import NormalRule from '../assets/normalRule.svg';
dayjs.locale('zh-cn');

const isIframe = window.location.href.indexOf('isIframe=true') !== -1;

export const menuWidth = isIframe ? '0px' : 'var(--menu-width)';

export const drawerWidth = isIframe ? '100%' : 'calc(100% - var(--menu-width))';

export const headerHeight = isIframe ? '0px' : 'var(--header-height)';

export const STATIC_URL = 'https://static.zhongan.com';

export const antConfig = {
  theme: {
    token: {
      fontFamily:
        '-apple-system, BlinkMacSystemFont, PingFang SC, Segoe UI, Roboto, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Source Han Sans CN, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol',
      colorPrimary: '#5D5FEF',
      colorLink: '#5D5FEF',
      colorSplit: '#E5E6EB',
      colorBgContainerDisabled: '#F2F3F5',
      colorText: '#1D2129',
      colorBorder: '#D0D5DD',
      colorIcon: '#4E5969',
      colorTextPlaceholder: '#86909C',
      colorTextDisabled: '#4E5969',
    },
    components: {
      Button: {
        defaultBorderColor: '#D0D5DD',
        defaultColor: '#4E5969',
      },
      Table: {
        headerBorderRadius: 6,
        headerBg: '#eaedf3',
        headerColor: '#1d2129',
        cellPaddingBlock: 8,
      },
      Form: {
        labelColor: '#4E5969',
      },
    },
  },
  locale,
};

export const enum EFieldsType {
  String = 1,
  Date = 3,
  Number = 2,
  List = 4,
}

export const enum ETime {
  REAL_TIME = 1,
  REGULAR_TIME = 2,
}

export const requiredRules = [{ required: true, message: '请填写${label}' }];

export const enum ERuleType {
  /**关键词规则 */
  CUSTOM = 1,
  /**通用规则 */
  NORMAL = 3,
  /**质检技能 */
  CE = 0,
  /**AI规则 */
  AI = 2,
  /**AI规则模版 */
  AI_TEMPLATE = 4,
}

export const RuleTypeMap = {
  [ERuleType.CUSTOM]: {
    color: '#2D92F3',
    bgColor: '#E3EFFA',
    img: CustomRule,
    tagColor: '#026AA2',
    tagBorderColor: '#B9E6FE',
    tagBgColor: '#F0F9FF',
  },
  [ERuleType.NORMAL]: {
    color: '#EF5D89',
    bgColor: '#EF5D891A',
    img: NormalRule,
    tagColor: '#C11574',
    tagBorderColor: '#FCCEEE',
    tagBgColor: '#FDF2FA',
  },
  [ERuleType.CE]: {
    color: '#27AE60',
    bgColor: '#E8F7E8',
    img: CeRule,
    tagColor: '#178C4E',
    tagBorderColor: '#ABEFC6',
    tagBgColor: '#ECFDF3',
  },
  [ERuleType.AI]: {
    color: '#F2994A',
    bgColor: '#FFF0E3',
    img: AiRule,
    tagColor: '#B54708',
    tagBorderColor: '#F9DBAF',
    tagBgColor: '#FFFAEB',
  },
  [ERuleType.AI_TEMPLATE]: {
    color: '#5D5FEF',
    bgColor: '#F2F2FE',
    img: AiRuleTemplate,
    tagColor: '#6941C6',
    tagBorderColor: '#D9D6FE',
    tagBgColor: '#F9F5FF',
  },
};

export const enum ECondition {
  AND = 1,
  OR = 2,
}

export const MSG_ID_PREFIX = 'MSG_ID_PREFIX';

/**结果查询列表缓存key */
export const RESULT_CACHE_KEY = 'RESULT_CACHE_KEY';

/**质检结果查询列表key */
export const RESULT_CACHE_KEY_V2 = 'RESULT_CACHE_KEY_V2';
export const enum ESessionType {
  ONLINE = '在线',
  CALL = '电话',
}

export const IS_TEST = location.host.includes('test');
export const IS_LOCAL_OR_TEST = location.host.includes('localhost') || IS_TEST;
export const IS_PRD = !IS_LOCAL_OR_TEST;

export const getResultRich = () => {
  return `    <div class="rich-content"><p><strong>召回率</strong>衡量的是AI识别所有违规会话的能力。它表示在所有实际违规的会话中，AI正确识别了多少。召回率高意味着违规会话很少被遗漏。</p>
  <p><strong>准确率</strong>衡量的是AI整体分类的准确性。它表示在所有被AI分类的会话中，有多少是正确分类的。准确率高意味着AI的判断总体上是准确的。</p>
  <p>以我们的例子来说，假设我们有100条会话，其中30条违规，70条正常。AI检测出40条违规会话，其中20条是真正的违规；同时，AI将60条正常会话判断为正常，但这其中实际上有10条是违规的。</p>
  <p>根据这些数据，我们可以计算出：</p>
  <ul>
  <li><strong>召回率</strong>：AI正确识别了20条违规会话，但实际有30条违规会话，所以召回率是 <span class="math math-inline"><span class="katex"><span class="katex-html" aria-hidden="true"><span class="base"><span class="strut" style="height:1.1901em;vertical-align:-0.345em;"></span><span class="mord"><span class="mopen nulldelimiter"></span><span class="mfrac"><span class="vlist-t vlist-t2"><span class="vlist-r"><span class="vlist" style="height:0.8451em;"><span style="top:-2.655em;"><span class="pstrut" style="height:3em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">30</span></span></span></span><span style="top:-3.23em;"><span class="pstrut" style="height:3em;"></span><span class="frac-line" style="border-bottom-width:0.04em;"></span></span><span style="top:-3.394em;"><span class="pstrut" style="height:3em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">20</span></span></span></span></span><span class="vlist-s">&ZeroWidthSpace;</span></span><span class="vlist-r"><span class="vlist" style="height:0.345em;"><span></span></span></span></span></span><span class="mclose nulldelimiter"></span></span><span class="mspace" style="margin-right:0.2778em;"></span><span class="mrel">≈</span><span class="mspace" style="margin-right:0.2778em;"></span></span><span class="base"><span class="strut" style="height:0.8056em;vertical-align:-0.0556em;"></span><span class="mord">66.67%</span></span></span></span></span></li>
  <li><strong>准确率</strong>：AI将60条正常会话正确判断为正常，同时错误地将20条正常会话判断为违规，所以准确率是 <span class="math math-inline"><span class="katex"><span class="katex-html" aria-hidden="true"><span class="base"><span class="strut" style="height:1.2173em;vertical-align:-0.345em;"></span><span class="mord"><span class="mopen nulldelimiter"></span><span class="mfrac"><span class="vlist-t vlist-t2"><span class="vlist-r"><span class="vlist" style="height:0.8723em;"><span style="top:-2.655em;"><span class="pstrut" style="height:3em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">100</span><span class="mord cjk_fallback mtight">（总会话数）</span></span></span></span><span style="top:-3.23em;"><span class="pstrut" style="height:3em;"></span><span class="frac-line" style="border-bottom-width:0.04em;"></span></span><span style="top:-3.394em;"><span class="pstrut" style="height:3em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">20</span><span class="mord cjk_fallback mtight">（正确违规）</span><span class="mbin mtight">+</span><span class="mord mtight">60</span><span class="mord cjk_fallback mtight">（正确正常）</span></span></span></span></span><span class="vlist-s">&ZeroWidthSpace;</span></span><span class="vlist-r"><span class="vlist" style="height:0.345em;"><span></span></span></span></span></span><span class="mclose nulldelimiter"></span></span><span class="mspace" style="margin-right:0.2778em;"></span><span class="mrel">≈</span><span class="mspace" style="margin-right:0.2778em;"></span></span><span class="base"><span class="strut" style="height:0.8056em;vertical-align:-0.0556em;"></span><span class="mord">72.73%</span></span></span></span></span></li>
  </ul><!--84--></div>`;
};

export const latestXDay = (day: number) => {
  const now = dayjs();

  const startOfRange = now.subtract(day, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss');
  const endOfRange = now.endOf('day').format('YYYY-MM-DD HH:mm:ss');

  return [dayjs(startOfRange), dayjs(endOfRange)];
};

export const RANGE_PRESETS: any = [
  {
    label: '今天',
    value: [
      dayjs(dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')),
      dayjs(dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')),
    ],
  },
  {
    label: '最近7天',
    value: latestXDay(7),
  },
  { label: '最近14天', value: latestXDay(14) },
  { label: '最近30天', value: latestXDay(30) },
];

export const STATUS_OPTIONS = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '启用',
    value: 1,
  },
  {
    label: '禁用',
    value: 0,
  },
];

export const SESSION_TYPE_OPTIONS = [
  { label: '电话', value: '电话' },
  { label: '在线', value: '在线' },
  { label: '企微', value: '企微' },
];

export const RULE_TAGGING = [
  {
    label: '正确',
    value: '1',
  },
  {
    label: '错误',
    value: '0',
  },
  {
    label: '正确但不扣罚坐席',
    value: '2',
  },
];

export const RULE_TAGGING_OPTIONS = [
  ...RULE_TAGGING,
  {
    label: '未反馈',
    value: '-1',
  },
];

export enum QUALITY_TYPE {
  SINGLE = 1,
  MULTI = 2,
  REAL_TIME = 3,
}

export const QUALITY_OPTIONS = [
  {
    label: '单会话',
    value: QUALITY_TYPE.SINGLE,
  },
  {
    label: '多会话',
    value: QUALITY_TYPE.MULTI,
  },
  {
    label: '实时质检',
    value: QUALITY_TYPE.REAL_TIME,
  },
];

export const isShowMsgNum = [2, 4, 5];

export const QUALITY_MSG_NUM_TYPE_OPTIONS = [
  {
    label: '最新一条',
    value: 1,
  },
  {
    label: '最近X条',
    value: 2,
  },
  {
    label: '会话截止到最新所有消息',
    value: 3,
  },
  {
    label: '客户最近X条（仅当客户消息请求时触发）',
    value: 4,
  },
  {
    label: '坐席最近X条（仅当坐席消息请求时触发）',
    value: 5,
  },
];

export const MINIATURE_USE_TYPE_OPTIONS = [
  {
    label: 'AB测试',
    value: 'ab_test',
  },
  {
    label: '大小模型融合',
    value: 'model_fusion',
  },
];

export const MODEL_TYPE_DARA = {
  big: '仅大模型',
  small: '大小模型融合',
};

export const QUERY_PAGE_SIZE = 200;
export const TEXT_SET_QUERY_PAGE_SIZE = 1000;
