export const ssoServiceName = 'za-ark-ce';

export const userCenterHost = () => {
  return getSsoHost();
  // return env && env.url
};

/**
 * 是否国际地址
 * @returns
 */
export function isIntl() {
  if ((typeof window === 'object' && /\.(in|if)\.za$/.test(window.location.hostname)) || process.env.MODE === 'za') {
    return true;
  }
  return false;
}

/**
 * 获取环境
 * @returns {string} env
 */
export function getEnv() {
  const str = location.hostname;
  const prdENv = ['eagle.in.za', 'eagle.zhonganonline.com', 'eagle.zhongan.com'];
  if (prdENv.includes(str)) {
    return 'prd';
  }
  const uatEnv = ['eagle-test.in.za'];
  if (uatEnv.includes(str)) {
    return 'uat';
  }
  const testEnv = ['eagle-test.in.za', 'eagle-test.zhonganonline.com', 'eagle-test.zhongan.com'];
  if (testEnv.includes(str)) {
    return 'test';
  }
  // 国际域名规则
  // 匹配 'eagle-xxx.in.za' 格式的字符串
  const regex = /^eagle-(\w+)\.in\.za$/;
  const match = str.match(regex);
  if (match) {
    return match[1];
  }
  // 保险域名规则
  const insureRegex = /^za-ark-ce-static\.(\w+)\.za\.biz$/;
  const insureMatch = str.match(insureRegex);
  if (insureMatch) {
    return insureMatch[1];
  } else {
    return isIntl() ? 'uat' : 'dev';
  }
}

/**
 * 通过环境获取国际sso登录域名
 * @param env string
 * @returns host string
 */
function getIntlSsoHost(env = 'dev') {
  const intlSsoHost: any = {
    dev: 'https://za-dev-uc.in.za',
    test: 'https://za-dev-uc.in.za',
    sit: 'https://za-sit-uc.in.za',
    uat: 'https://za-uat-uc.in.za',
    prd: 'https://za-uc.in.za',
  };
  return intlSsoHost[env];
}

/**
 * 获取保险sso登录域名
 * @returns host string
 */
function getBxSsoHost(env = 'dev') {
  const ssoHost: any = {
    dev: 'https://nsso-test.zhonganonline.com',
    test: 'https://nsso-test.zhonganonline.com',
    prd: 'https://nsso.zhonganonline.com',
  };
  return ssoHost[env];
}

/**
 * 获取sso主机
 * @returns
 */
export function getSsoHost() {
  const isInternational = isIntl();
  const currentEnv = getEnv();
  return isInternational ? getIntlSsoHost(currentEnv) : getBxSsoHost(currentEnv);
}

// 创建Target地址
const createTarget = (isHome?: boolean) => {
  const params = new URLSearchParams(window.location.search);
  params.delete('ticket');
  params.delete('session_id');
  let hashParamsString = window.location.hash?.split('?')[1];
  const searchParamsString = params.toString();
  let targetUrl = `${window.location.origin}${window.location.pathname}?${searchParamsString}`;
  if (hashParamsString) {
    const urlParams = new URLSearchParams(hashParamsString);
    urlParams.delete('ticket');
    urlParams.delete('session_id');
    hashParamsString = urlParams.toString();
  }
  if (!isHome) {
    let urlParams = '';
    if (hashParamsString && searchParamsString) {
      urlParams = `?${hashParamsString}&${searchParamsString}`;
    } else if (hashParamsString || searchParamsString) {
      hashParamsString && (urlParams = `?${hashParamsString}`);
      searchParamsString && (urlParams = `?${searchParamsString}`);
    }
    targetUrl = `${window.location.origin}${window.location.pathname}${
      window.location.hash?.split('?')[0] || ''
    }${urlParams}`;
  }
  return encodeURIComponent(targetUrl);
};

const clearSsoStorage = () => {
  window.localStorage.removeItem('ATLANTIS_SESSION_ID');
  window.localStorage.removeItem('userinfo');
  window.localStorage.removeItem('currentTenantNo');
};

// sso登录
export const SSOLogIn = () => {
  clearSsoStorage();
  // 绕过SSO登录，直接设置模拟的session ID
  const mockSessionId = 'mock-session-id-123456';
  window.localStorage.setItem('ATLANTIS_SESSION_ID', mockSessionId);

  // 设置模拟的用户信息
  const mockUserInfo = {
    id: '12345',
    name: '测试用户',
    email: '<EMAIL>',
    phone: '13800138000',
    roles: ['admin'],
    permissions: ['*'],
  };
  window.localStorage.setItem('userinfo', JSON.stringify(mockUserInfo));

  // 刷新页面以应用新的用户信息
  window.location.reload();

  // 注释掉原来的重定向代码
  // window.location.href = `${userCenterHost()}/login?service=${ssoServiceName}&target=${createTarget()}`;
};

// sso登出
export const SSOLogOut = () => {
  clearSsoStorage();
  window.location.href = `${userCenterHost()}/logout?target=${createTarget(true)}`;
};

// 设置请求头部
export const SSOReqHeader = (config: any) => {
  // 所有的请求头都需要添加 X-Service-Name，设置为当前应用服务名称
  const headers: any = {
    'X-Service-Name': ssoServiceName, //申请得到的sso service
  };
  // 验证接口validate2以外都接口都需添加 X-Usercenter-Session
  if (config && config.url !== '/validate2') {
    headers['X-Usercenter-Session'] = localStorage.getItem('ATLANTIS_SESSION_ID') || '';
  }

  return headers;
};

// 获取ticket
export const SSOGetTicket = () => {
  // 绕过ticket验证，直接返回模拟的ticket
  return 'mock-ticket-123456';

  // 注释掉原来的代码
  // let params = new URLSearchParams(window.location.search);
  // if (!params.get('ticket')) {
  //   SSOLogIn();
  // }
  // return params.get('ticket');
};

// ticket置换session接口
// 如用户中心已经登录，客户端调用login接口将会重定向至携带ticket参数的客户端回调地址。客户端需要使用ticket置换session信息。
export const FetchSessionByTicket = (callback: any) => {
  // 绕过API调用，直接设置模拟的session ID
  const mockSessionId = 'mock-session-id-123456';
  window.localStorage.setItem('ATLANTIS_SESSION_ID', mockSessionId);

  // 直接调用回调函数，传入模拟的用户信息
  if (callback) {
    const mockUserInfo = {
      id: '12345',
      name: '测试用户',
      email: '<EMAIL>',
      phone: '13800138000',
      roles: ['admin'],
      permissions: ['*'],
    };
    callback(mockUserInfo);
  }

  // 注释掉原来的API调用代码
  // return fetchSessionByTicket({
  //   service: ssoServiceName,
  //   ticket: SSOGetTicket(),
  // }).then((data: any) => {
  //   if (data && data.data) {
  //     const { result, success } = data.data;
  //     if (!success) {
  //       message.error(data.message);
  //       SSOLogIn();
  //     } else {
  //       window.localStorage.setItem('ATLANTIS_SESSION_ID', result);
  //       FetchUserInfoBySession(result, callback);
  //     }
  //   }
  // });
};

// sessionID置换用户信息接口
// 如客户端从request Cookie中获取到了当前应用的SessionID,可通过该接口换取用户信息。
export const FetchUserInfoBySession = (sid: any, callback: any) => {
  // 绕过API调用，直接设置模拟的session ID和用户信息
  const mockSessionId = 'mock-session-id-123456';
  localStorage.setItem('ATLANTIS_SESSION_ID', mockSessionId);

  // 直接调用回调函数，传入模拟的用户信息
  if (callback) {
    const mockUserInfo = {
      id: '12345',
      name: '测试用户',
      email: '<EMAIL>',
      phone: '13800138000',
      roles: ['admin'],
      permissions: ['*'],
    };
    callback(mockUserInfo);
  }

  // 注释掉原来的代码
  // let session_id;
  //
  // // 首先尝试从哈希中获取session_id
  // const hash = window.location.hash;
  // if (hash.includes('?')) {
  //   const hashParams = new URLSearchParams(hash.split('?')[1]);
  //   const rawSessionId = hashParams.get('session_id');
  //   session_id = rawSessionId ? encodeURIComponent(rawSessionId) : null;
  // }
  //
  // // 如果哈希中没有session_id，则尝试从标准的查询参数中获取
  // if (!session_id) {
  //   const params = new URLSearchParams(window.location.search);
  //   session_id = params.get('session_id');
  // }
  //
  // console.log('Session ID:', session_id);
  //
  // if (session_id) {
  //   // 如果URL中有session_id，则使用它
  //   localStorage.setItem('ATLANTIS_SESSION_ID', session_id);
  // }
  //
  // // 然后尝试获取加密的Session
  // const encryptedSession = sid ? sid : localStorage.getItem('ATLANTIS_SESSION_ID');
  // if (!encryptedSession) {
  //   // 如果没有加密的Session，则尝试获取Ticket
  //   return FetchSessionByTicket(callback);
  // } else {
  //   fetchUserInfoBySession({
  //     service: ssoServiceName,
  //     encryptedSession,
  //   }).then((data: any) => {
  //     if (data && data.data) {
  //       const { result, success } = data.data;
  //       if (!success) {
  //         message.error(data.message);
  //         FetchSessionByTicket(callback);
  //       } else {
  //         if (callback) callback(result);
  //       }
  //     }
  //   });
  // }
};
