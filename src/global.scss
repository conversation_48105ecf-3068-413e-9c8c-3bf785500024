// 全局样式
$primary-color: #5d5fef; // 全局主色
$header-height: 60px; // 头部高度
$menu-width: 90px; // 侧边栏宽度

:root {
  --primary-color: #{$primary-color};
  --header-height: #{$header-height};
  --menu-width: #{$menu-width};
}

@font-face {
  font-family: 'DIN Alternate';
  src: url('./assets/fonts/DIN-Alternate.ttf') format('truetype');
}

html,
body {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.red {
  color: red;
}

#root {
  width: 100%;
  height: 100%;
  background-color: #f2f3f5;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    PingFang SC,
    Segoe UI,
    Roboto,
    Hiragino Sans GB,
    Microsoft YaHei UI,
    Microsoft YaHei,
    Source Han Sans CN,
    sans-serif,
    Apple Color Emoji,
    Segoe UI Emoji,
    Segoe UI Symbol;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.3s;
}

.strategy-detail-drawer {
  .ant-drawer-body {
    padding: 0 !important;
    background-color: #eff1f4;
  }
}

#aiRuleName_help {
  margin-left: 52px;
}

#aiRuleDesc_help {
  margin-left: 32px;
}

// antd
.ant-input-affix-wrapper.ant-input-textarea-affix-wrapper.ant-input-show-count {
  .ant-input {
    resize: vertical !important;
  }
}

.tr-toolbar-right {
  .tr-toolbar-column-setting {
    box-shadow: none;
  }

  .anticon-column-height,
  .anticon-setting {
    vertical-align: -3px;
  }
}

// 超出两行省略
.ant-table-row .ant-table-cell {
  &>* {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
    /* 显示的行数 */
  }
}

.ant-modal-root .ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.6) !important;
}

.ant-switch {
  background-color: rgba(225, 225, 238, 1);
}

.ant-table {
  .ant-table-container {
    .ant-table-thead {
      .ant-table-cell {
        border-color: #e5e6eb;
        font-weight: 500;

        &::before {
          display: none;
        }
      }
    }

    .ant-table-tbody {
      .ant-table-cell {
        border-color: #e5e6eb;
      }
    }
  }
}

.ant-select-arrow {
  color: #4e5969 !important;

  .ant-select-suffix.anticon-down {
    width: 9px;
    height: 6px;
    background-image: url("data:image/svg+xml,%3Csvg width='9' height='6' viewBox='0 0 9 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M4.79289 5.45703L0.585787 1.24992L1.29289 0.542818L4.79289 4.04282L8.29289 0.542818L9 1.24992L4.79289 5.45703Z' fill='%234E5969'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;

    svg {
      display: none;
    }
  }
}

.ant-modal {
  .ant-modal-content {
    padding: 0 !important;
    border-radius: 16px !important;

    .ant-modal-close {
      top: 28px;
      right: 24px;
    }

    .ant-modal-body {
      padding: 0 32px;
    }

    .ant-modal-header {
      border-radius: 16px 16px 0 0 !important;
      color: #1d2129;
      font-size: 20px;
      line-height: 20px;
      height: inherit;

      .ant-modal-title {
        padding: 32px 32px 24px;
      }

      &::after {
        content: '';
        display: block;
        width: 100%;
        height: 0.5px;
        background-color: #d6d8de;
      }
    }

    .ant-modal-footer {
      padding: 0.5px 32px 0;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: relative;

      &::before {
        content: '';
        display: block;
        width: 100%;
        height: 0.5px;
        background-color: #d6d8de;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }

  &.ant-modal-confirm {
    .ant-modal-content {
      padding: 20px 0 !important;

      .ant-modal-confirm-btns {
        .ant-btn-default {
          &:hover {
            color: var(--primary-color);
            border-color: var(--primary-color);
          }
        }

        .ant-btn-primary {
          background: var(--primary-color);

          &:hover {
            background: #8b91fc;
          }
        }
      }
    }
  }
}