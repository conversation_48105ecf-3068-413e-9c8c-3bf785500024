import { SSOLogIn, ssoServiceName } from '@/utils/sso';
import { message } from 'antd';
import axios from 'axios';

// 创建一个axios实例
const request = axios.create({
  // 在这里设置默认的请求头信息
  headers: {
    'X-Service-Name': ssoServiceName, //申请得到的sso service
  },
});

// 在创建实例后，添加请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在这里添加请求被发送前的处理逻辑，比如给请求头添加认证信息
    // config.headers.Authorization = 'Bearer ' + getToken();
    if (!config.url?.includes('validate2')) {
      config.headers['X-Usercenter-Session'] = localStorage.getItem('ATLANTIS_SESSION_ID') || '';
      config.headers['X-Service-Name'] = ssoServiceName;
    }
    return config;
  },
  (error) => {
    // 在请求发生错误时的处理逻辑
    return Promise.reject(error);
  }
);
// 添加响应拦截器
request.interceptors.response.use(
  (response) => {
    // 在这里对响应进行处理，比如解析返回的数据
    // const data = response.data;
    if (!response.data.success) {
      response.data.errorMsg && message.error(response.data.errorMsg);
    }
    if (response.data.code === 401 || response.data.errorCode === '401') {
      SSOLogIn();
    }
    return response;
  },
  (error) => {
    message.error('服务端异常，请稍后重试');
    // 在响应发生错误时的处理逻辑
    return Promise.reject(error);
  }
);
export default request;

export const get = request.get;
export const post = request.post;
export const PREFIX = '/ce';
