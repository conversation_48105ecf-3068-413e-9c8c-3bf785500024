import { get, post, PREFIX } from '.';

export const getRobotList = () => {
  return get(PREFIX + '/api/v1/robot/availableRobotList');
};

export const getRobotAvailableList = (tenantNo: string) => {
  return get(`${PREFIX}/api/v1/robot/availableList?tenantNo=${tenantNo}`);
};

//获取质检结果详情
export const getQualityTaskResult = (params: ICreateFieldConfig) => {
  return post(`${PREFIX}/api/v1/qualityTaskResult/detail`, params);
};

export const feedbackQualityResults = (params: FeedbackQualityResult[]) => {
  return post(`${PREFIX}/api/v1/qualityTaskResult/feedbackQualityResults`, params);
};

export const feedbackQualityRealTimeResults = (params: FeedbackQualityResult[]) => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/feedbackQualityResults`, params);
};

//获取默认配置列表
export const fetchListConfigColumns = (params: any) => {
  return get(`${PREFIX}/api/v1/display/listConfig`, { params });
};

export const queryListConfigNewColumns = (params: any) => {
  return get(`${PREFIX}/api/v1/display/listConfigNew`, { params });
};

//添加列表配置
export const updateFeedbackColumns = (params: {
  displayFieldList: any[];
  displayType: number | string;
  tenantNo: string;
}) => {
  return post(`${PREFIX}/api/v1/display/create`, params);
};

export const createField = (params: ICreateFieldConfig) => {
  return post(PREFIX + '/api/v1/tenantFieldConfig/create', params);
};

export const getFieldList = (tenantNo: string, isDeleted?: string, showStatus?: number) => {
  return post(PREFIX + '/api/v1/tenantFieldConfig/listConfig', { tenantNo, isDeleted, showStatus });
};

export const updateFieldStatus = (params: {
  id: number;
  status?: number;
  showStatus?: number;
  displayConfig?: any;
}) => {
  return post(PREFIX + '/api/v1/tenantFieldConfig/updateStatus', params);
};

export const getStrategyGroupList = (params: { tenantNo: string }) => {
  return post(`${PREFIX}/api/v1/strategyGroup/listConfig?tenantNo=${params.tenantNo}`, params);
};

export const getStraConfigList = (params: { tenantNo: string }) => {
  return post(`${PREFIX}/api/v1/strategyConfig/queryConfig?tenantNo=${params.tenantNo}`, params);
};

export const getStrategyConfigList = (params: {
  pageNum: number;
  pageSize: number;
  status: number;
  strategyCode: string;
  strategyName: string;
  tenantNo: string;
}) => {
  return post(PREFIX + '/api/v1/strategyConfig/page', params);
};

//质检结果分析查询列表接口
export const getQualityTaskResultList = (params: {
  pageNum: number;
  pageSize: number;
  strategyName: string;
  sessionId: string;
  status: number;
  tenantNo: string;
  startTime: string;
  endTime: string;
}) => {
  return post(PREFIX + '/api/v1/qualityTaskResult/page', params);
};

export const createStrategyGroup = (params: { groupName: string; tenantNo: string }) => {
  return post(PREFIX + '/api/v1/strategyGroup/create', params);
};

export const getOperatorList = () => {
  return get(PREFIX + '/api/v1/tenantFieldConfig/listOperator');
};
export const getRobotSkillList = (robotNo: string) => {
  return get(`${PREFIX}/api/v1/robot/skillList?robotNo=${robotNo}`);
};

export const addStrategyConfig = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/add`, params);
};

export const updateStrategyConfig = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/update`, params);
};

export const getStrategyConfigDetail = (id: string) => {
  return post(`${PREFIX}/api/v1/strategyConfig/detail?id=${id}`);
};

export const maintainAnalyzerDict = (params: any) => {
  return post(`${PREFIX}/api/v1/dict/maintainAnalyzerDict`, params);
};

export const summitRunningData = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/configTest`, params);
};

export const getConfigTestPage = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/configTestPage`, params);
};

export const regularTestData = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/regularTest`, params);
};

/**获取分组树 */
export const getGroupTree = (tenantNo: string) => {
  return get(`${PREFIX}/api/v1/strategyGroup/tree?tenantNo=${tenantNo}`);
};

export const updateGroup = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyGroup/update`, params);
};

export const moveStrategyConfig = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/move`, params);
};

export const addGroup = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyGroup/create`, params);
};

export const doDeleteGroup = (id: number, tenantNo: string) => {
  return get(`${PREFIX}/api/v1/strategyGroup/delete?id=${id}&tenantNo=${tenantNo}`);
};

export const getRuleTypeList = () => {
  return get(`${PREFIX}/api/v1/robot/ruleTypeList`);
};

export const getAvailableNewList = (tenantNo: string, ruleType: number) => {
  return get(`${PREFIX}/api/v1/robot/availableNewList?tenantNo=${tenantNo}&ruleType=${ruleType}`);
};

export const testAiRule = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/aiRuleTest`, params);
};

export const testBotRule = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/botRuleTest`, params);
};

export const batchImport = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleTest/batchImport`, params);
};

export const botBatchImport = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleTest/botBatchImport`, params);
};

export const getRuleTestList = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleTest/page`, params);
};

export const reTestRule = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleTest/reTest`, params);
};

export const downloadTestResult = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleTest/download`, params);
};

export const getRuleTestDetail = (batchNo: string) => {
  return get(`${PREFIX}/api/v1/ruleTest/detail?batchNo=${batchNo}`);
};

export const deleteRuleTest = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleTest/delete`, params);
};

export const getStrategyByGroupId = (groupId: any) => {
  return get(`${PREFIX}/api/v1/strategyConfig/getAllByGroupId?groupId=${groupId}`);
};

export const queryQualityStatistics = (params: any) => {
  return post(`${PREFIX}/api/v1/dailyReport/queryQualityStatistics`, params);
};

export const queryQualityDetailsStatistics = (params: any) => {
  return post(`${PREFIX}/api/v1/dailyReport/queryQualityDetailsStatistics`, params);
};

export const queryMetricCycles = (params: any) => {
  return post(`${PREFIX}/api/v1/dailyReport/metricCycles`, params);
};

export const queryReportTrendMonitoring = (params: any) => {
  return post(`${PREFIX}/api/v1/dailyReport/reportTrendMonitoring`, params);
};

export const saveDraft = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/drafts/save`, params);
};
export const commitDraft = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/drafts/submit`, params);
};

export const deleteDraft = (id: number) => {
  return post(`${PREFIX}/api/v1/strategyConfig/drafts/delete`, { id });
};

export const getStrategyConfigDraftList = (params: any) => {
  return post(`${PREFIX}/api/v1/strategyConfig/drafts/page`, params);
};

export const getStrategyDraftDetail = (id: string, isRedLine?: string) => {
  return post(`${PREFIX}/api/v1/strategyConfig/drafts/detail?id=${id}${isRedLine ? `&isRedLine=${isRedLine}` : ''}`);
};

export const getHitResult = (params: any) => {
  return post(`${PREFIX}/api/v1/qualityTask/resultPage`, params);
};

export const getLabelList = (tenantNo: any, isRedLine?: string) => {
  return post(`${PREFIX}/api/v1/label/query`, { tenantNo, isRedLine });
};

export const putLabel = (params: any) => {
  return post(`${PREFIX}/api/v1/label/put`, params);
};

export const getRiskList = (tenantNo: any, isRedLine?: string) => {
  return post(`${PREFIX}/api/v1/tenantConfig/riskLevel`, { tenantNo, isRedLine });
};

export const exportBatchTest = (params: any) => {
  return post(`${PREFIX}/api/v1/brTest/export`, params);
};

export const queryBatchExecuteTaskList = (params: { tenantNo: string; pageSize: number; pageNum: number }) => {
  return post(`${PREFIX}/api/v1/brTest/queryBatchExecuteTaskList`, params);
};

export const endRuleOptimizeTask = (params: { id: number }) => {
  return get(`${PREFIX}/api/v1/brTest/endRuleOptimizeTask`, { params });
};

export const queryBatchExecuteTaskCount = (params: any) => {
  return post(`${PREFIX}/api/v1/brTest/queryBatchExecuteTaskCount`, params);
};

export const createBatchExecuteTask = (params: any) => {
  return post(`${PREFIX}/api/v1/brTest/createBatchExecuteTask`, params);
};

export const uploadBatchExecuteFile = (formData: any) => {
  return post(`${PREFIX}/api/v1/brTest/uploadBatchExecuteFile`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const addStrategyTemplate = (params: any) => {
  return post(`${PREFIX}/api/v1/strategy/template/add`, params);
};

export const updateStrategyTemplate = (params: any) => {
  return post(`${PREFIX}/api/v1/strategy/template/update`, params);
};

export const getStrategyTemplateList = (params: any) => {
  return post(`${PREFIX}/api/v1/strategy/template/page`, params);
};

export const getStrategyTemplateDetail = (id: number) => {
  return post(`${PREFIX}/api/v1/strategy/template/detail?id=${id}`);
};

export const getStrategyTemplateNameList = () => {
  return post(`${PREFIX}/api/v1/strategy/template/strategyNameList?strategyName=`);
};

export const deleteStrategyTemplate = (id: number) => {
  return post(`${PREFIX}/api/v1/strategy/template/del`, { id });
};

export const getStrategyConfigTemplateLimit = () => {
  return get(`${PREFIX}/api/v1/strategy/template/getStrategyConfigTemplateLimit`);
};

export const addAiRuleTemplate = (params: any) => {
  return post(`${PREFIX}/api/v1/aiRule/template/add`, params);
};

export const getAiRuleTemplateList = () => {
  return post(`${PREFIX}/api/v1/aiRule/template/all`);
};

export const deleteAiRuleTemplate = (params: any) => {
  return post(`${PREFIX}/api/v1/aiRule/template/del`, params);
};

export const updateAiRuleTemplate = (params: any) => {
  return post(`${PREFIX}/api/v1/aiRule/template/update`, params);
};

export const getAiRuleTemplateDetail = (id: number) => {
  return post(`${PREFIX}/api/v1/aiRule/template/detail?id=${id}`);
};

// 智能规则优化
export const queryQualityResultList = (params: any) => {
  return post(`${PREFIX}/api/v1/qualityTask/queryQualityResultList`, params);
};

// 导出标注质检结果
export const downloadQualityResultList = (params: any) => {
  return post(`${PREFIX}/api/v1/qualityTask/downloadQualityResultList`, params);
};

// 导出按钮状态
export const queryDownloadStatus = (params: { tenantNo: string }) => {
  return post(`${PREFIX}/api/v1/qualityTask/downloadStatus`, params);
};

// 导出记录列表
export const queryDownloadCsvList = (params: { tenantNo: string }) => {
  return post(`${PREFIX}/api/v1/qualityTask/downloadCsvList`, params);
};

// 删除导出记录
export const delDownloadDetail = (params: { taskId: string }) => {
  return post(`${PREFIX}/api/v1/qualityTask/delDownloadDetail`, params);
};

export const queryRuleOptimizeTaskList = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleOptimize/queryRuleOptimizeTaskList`, params);
};

export const createRuleOptimizeTask = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleOptimize/createRuleOptimizeTask`, params);
};

export const deleteRuleOptimizeTask = (id: number) => {
  return get(`${PREFIX}/api/v1/ruleOptimize/deleteRuleOptimizeTask?id=${id}`);
};

export const queryTaskDetail = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleOptimize/queryTaskDetail`, params);
};

export const queryStepExec = (params: any) => {
  return post(`${PREFIX}/api/v1/ruleOptimize/queryStepExec`, params);
};

export const uploadExample = (formData: any) => {
  return post(`${PREFIX}/api/v1/example/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const addQualityViolationItems = (params: any) => {
  return post(`${PREFIX}/api/v1/qualityTaskResult/addQualityViolationItems`, params);
};

export const addRealTimeQualityViolationItems = (params: any) => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/addQualityViolationItems`, params);
};

export const delQualityViolationItems = (params: any) => {
  return post(`${PREFIX}/api/v1/qualityTaskResult/delQualityViolationItems`, params);
};

export const delRealTimeQualityViolationItems = (params: any) => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/delQualityViolationItems`, params);
};

export const queryQualityResultTagLog = (params: { tenantNo: string; sessionId: string }) => {
  return post(`${PREFIX}/api/v1/qualityTaskResult/queryQualityResultTagLog`, params);
};

export const getNoViolation = (params: { tenantNo: string; sessionId: string; tagResult: string }) => {
  return post(`${PREFIX}/api/v1/qualityTaskResult/noViolation`, params);
};

export const queryRealTimeQualityResultTagLog = (params: { tenantNo: string; sessionId: string }) => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/queryQualityResultTagLog`, params);
};

export const getWhitelistPermission = () => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/whitelistPermission`);
};

export const queryRealTimeData = (params: { tenantNo: string }) => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/realTimeData`, params);
};

export const queryRealTimeQualityResultPage = (params: { tenantNo: string; pageSize: number; pageNum: number }) => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/realTimeQualityResultPage`, params);
};

export const getQualityRealTimeTaskResult = (params: ICreateFieldConfig) => {
  return post(`${PREFIX}/api/v1/realTimeQualityResult/detail`, params);
};

export const getListSupportedModels = () => {
  return post(`${PREFIX}/api/v1/strategyConfig/drafts/list_supported_models`);
};

export const queryAbTestMonitoring = (params: any) => {
  return post(`${PREFIX}/api/v1/dailyReport/abTestMonitoring`, params);
};

export const appealStatus = (params: any) => {
  return post(`${PREFIX}/api/v1/appeal/status`, params);
};
