import { post, PREFIX } from '.';

export const getRedLineListConfig = (params: {
  tenantNos?: string[];
  tenantNo: string;
  isDeleted?: string;
  showStatus?: number;
}) => {
  return post(PREFIX + '/api/v1/tenantFieldConfig/redLineListConfig', params);
};

export const getDataScreening = (params: {
  startTime?: string;
  endTime: string;
  groupIdList?: string[];
  strategyIdList?: string[];
  ruleIdList?: string[];
}) => {
  return post(PREFIX + '/api/v1/redLine/dataScreening', params);
};

export const queryRedLineReportTrendMonitoring = (params: any) => {
  return post(`${PREFIX}/api/v1/redLine/reportTrendMonitoring`, params);
};
