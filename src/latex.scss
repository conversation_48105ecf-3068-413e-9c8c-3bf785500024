.rich-content {
  .CodeMirror {
    font-family: monospace;
    height: 300px;
    color: #000;
    direction: ltr;
  }

  .CodeMirror-lines {
    padding: 4px 0;
  }

  .CodeMirror pre.CodeMirror-line,
  .CodeMirror pre.CodeMirror-line-like {
    padding: 0 4px;
  }

  .CodeMirror-gutter-filler,
  .CodeMirror-scrollbar-filler {
    background-color: #fff;
  }

  .CodeMirror-gutters {
    border-right: 1px solid #ddd;
    background-color: #f7f7f7;
    white-space: nowrap;
  }

  .CodeMirror-linenumber {
    padding: 0 3px 0 5px;
    min-width: 20px;
    text-align: right;
    color: #999;
    white-space: nowrap;
  }

  .CodeMirror-guttermarker {
    color: #000;
  }

  .CodeMirror-guttermarker-subtle {
    color: #999;
  }

  .CodeMirror-cursor {
    border-left: 1px solid #000;
    border-right: none;
    width: 0;
  }

  .CodeMirror div.CodeMirror-secondarycursor {
    border-left: 1px solid silver;
  }

  .cm-fat-cursor .CodeMirror-cursor {
    width: auto;
    border: 0 !important;
    background: #7e7;
  }

  .cm-fat-cursor div.CodeMirror-cursors {
    z-index: 1;
  }

  .cm-fat-cursor .CodeMirror-line::selection,
  .cm-fat-cursor .CodeMirror-line > span::selection,
  .cm-fat-cursor .CodeMirror-line > span > span::selection {
    background: transparent;
  }

  .cm-fat-cursor .CodeMirror-line::-moz-selection,
  .cm-fat-cursor .CodeMirror-line > span::-moz-selection,
  .cm-fat-cursor .CodeMirror-line > span > span::-moz-selection {
    background: transparent;
  }

  .cm-fat-cursor {
    caret-color: transparent;
  }

  @-webkit-keyframes blink {
    50% {
      background-color: transparent;
    }
  }

  @keyframes blink {
    50% {
      background-color: transparent;
    }
  }

  .cm-tab {
    display: inline-block;
    text-decoration: inherit;
  }

  .CodeMirror-rulers {
    position: absolute;
    inset: -50px 0 0;
    overflow: hidden;
  }

  .CodeMirror-ruler {
    border-left: 1px solid #ccc;
    top: 0;
    bottom: 0;
    position: absolute;
  }

  .cm-s-default .cm-header {
    color: #00f;
  }

  .cm-s-default .cm-quote {
    color: #090;
  }

  .cm-negative {
    color: #d44;
  }

  .cm-positive {
    color: #292;
  }

  .cm-header,
  .cm-strong {
    font-weight: 700;
  }

  .cm-em {
    font-style: italic;
  }

  .cm-link {
    text-decoration: underline;
  }

  .cm-strikethrough {
    text-decoration: line-through;
  }

  .cm-s-default .cm-keyword {
    color: #708;
  }

  .cm-s-default .cm-atom {
    color: #219;
  }

  .cm-s-default .cm-number {
    color: #164;
  }

  .cm-s-default .cm-def {
    color: #00f;
  }

  .cm-s-default .cm-variable-2 {
    color: #05a;
  }

  .cm-s-default .cm-type,
  .cm-s-default .cm-variable-3 {
    color: #085;
  }

  .cm-s-default .cm-comment {
    color: #a50;
  }

  .cm-s-default .cm-string {
    color: #a11;
  }

  .cm-s-default .cm-string-2 {
    color: #f50;
  }

  .cm-s-default .cm-meta,
  .cm-s-default .cm-qualifier {
    color: #555;
  }

  .cm-s-default .cm-builtin {
    color: #30a;
  }

  .cm-s-default .cm-bracket {
    color: #997;
  }

  .cm-s-default .cm-tag {
    color: #170;
  }

  .cm-s-default .cm-attribute {
    color: #00c;
  }

  .cm-s-default .cm-hr {
    color: #999;
  }

  .cm-s-default .cm-link {
    color: #00c;
  }

  .cm-invalidchar,
  .cm-s-default .cm-error {
    color: red;
  }

  .CodeMirror-composing {
    border-bottom: 2px solid;
  }

  div.CodeMirror span.CodeMirror-matchingbracket {
    color: #0b0;
  }

  div.CodeMirror span.CodeMirror-nonmatchingbracket {
    color: #a22;
  }

  .CodeMirror-matchingtag {
    background: rgba(255, 150, 0, 0.3);
  }

  .CodeMirror-activeline-background {
    background: #e8f2ff;
  }

  .CodeMirror {
    position: relative;
    overflow: hidden;
    background: #fff;
  }

  .CodeMirror-scroll {
    overflow: scroll !important;
    margin-bottom: -50px;
    margin-right: -50px;
    padding-bottom: 50px;
    height: 100%;
    outline: none;
    position: relative;
    z-index: 0;
  }

  .CodeMirror-sizer {
    position: relative;
    border-right: 50px solid transparent;
  }

  .CodeMirror-gutter-filler,
  .CodeMirror-hscrollbar,
  .CodeMirror-scrollbar-filler,
  .CodeMirror-vscrollbar {
    position: absolute;
    z-index: 6;
    display: none;
    outline: none;
  }

  .CodeMirror-vscrollbar {
    right: 0;
    top: 0;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  .CodeMirror-hscrollbar {
    bottom: 0;
    left: 0;
    overflow-y: hidden;
    overflow-x: scroll;
  }

  .CodeMirror-scrollbar-filler {
    right: 0;
    bottom: 0;
  }

  .CodeMirror-gutter-filler {
    left: 0;
    bottom: 0;
  }

  .CodeMirror-gutters {
    position: absolute;
    left: 0;
    top: 0;
    min-height: 100%;
    z-index: 3;
  }

  .CodeMirror-gutter {
    white-space: normal;
    height: 100%;
    display: inline-block;
    vertical-align: top;
    margin-bottom: -50px;
  }

  .CodeMirror-gutter-wrapper {
    position: absolute;
    z-index: 4;
    background: none !important;
    border: none !important;
  }

  .CodeMirror-gutter-background {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 4;
  }

  .CodeMirror-gutter-elt {
    position: absolute;
    cursor: default;
    z-index: 4;
  }

  .CodeMirror-gutter-wrapper ::selection {
    background-color: transparent;
  }

  .CodeMirror-gutter-wrapper ::-moz-selection {
    background-color: transparent;
  }

  .CodeMirror-lines {
    cursor: text;
    min-height: 1px;
  }

  .CodeMirror pre.CodeMirror-line,
  .CodeMirror pre.CodeMirror-line-like {
    border-radius: 0;
    border-width: 0;
    background: transparent;
    font-family: inherit;
    font-size: inherit;
    margin: 0;
    white-space: pre;
    word-wrap: normal;
    line-height: inherit;
    color: inherit;
    z-index: 2;
    position: relative;
    overflow: visible;
    -webkit-tap-highlight-color: transparent;
    -webkit-font-variant-ligatures: contextual;
    font-variant-ligatures: contextual;
  }

  .CodeMirror-wrap pre.CodeMirror-line,
  .CodeMirror-wrap pre.CodeMirror-line-like {
    word-wrap: break-word;
    white-space: pre-wrap;
    word-break: normal;
  }

  .CodeMirror-linebackground {
    position: absolute;
    inset: 0;
    z-index: 0;
  }

  .CodeMirror-linewidget {
    position: relative;
    z-index: 2;
    padding: 0.1px;
  }

  .CodeMirror-rtl pre {
    direction: rtl;
  }

  .CodeMirror-code {
    outline: none;
  }

  .CodeMirror-gutter,
  .CodeMirror-gutters,
  .CodeMirror-linenumber,
  .CodeMirror-scroll,
  .CodeMirror-sizer {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
  }

  .CodeMirror-measure {
    position: absolute;
    width: 100%;
    height: 0;
    overflow: hidden;
    visibility: hidden;
  }

  .CodeMirror-cursor {
    position: absolute;
    pointer-events: none;
  }

  .CodeMirror-measure pre {
    position: static;
  }

  div.CodeMirror-cursors {
    visibility: hidden;
    position: relative;
    z-index: 3;
  }

  .CodeMirror-focused div.CodeMirror-cursors,
  div.CodeMirror-dragcursors {
    visibility: visible;
  }

  .CodeMirror-selected {
    background: #d9d9d9;
  }

  .CodeMirror-focused .CodeMirror-selected {
    background: #d7d4f0;
  }

  .CodeMirror-crosshair {
    cursor: crosshair;
  }

  .CodeMirror-line::selection,
  .CodeMirror-line > span::selection,
  .CodeMirror-line > span > span::selection {
    background: #d7d4f0;
  }

  .CodeMirror-line::-moz-selection,
  .CodeMirror-line > span::-moz-selection,
  .CodeMirror-line > span > span::-moz-selection {
    background: #d7d4f0;
  }

  .cm-searching {
    background-color: #ffa;
    background-color: rgba(255, 255, 0, 0.4);
  }

  .cm-force-border {
    padding-right: 0.1px;
  }

  @media print {
    .CodeMirror div.CodeMirror-cursors {
      visibility: hidden;
    }
  }

  .cm-tab-wrap-hack:after {
    content: '';
  }

  span.CodeMirror-selectedtext {
    background: none;
  }

  .tippy-box[data-animation='fade'][data-state='hidden'] {
    opacity: 0;
  }

  [data-tippy-root] {
    max-width: calc(100vw - 10px);
  }

  .tippy-box {
    position: relative;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    white-space: normal;
    outline: 0;
    -webkit-transition-property: visibility, opacity, -webkit-transform;
    transition-property: visibility, opacity, -webkit-transform;
    transition-property: transform, visibility, opacity;
    transition-property: transform, visibility, opacity, -webkit-transform;
  }

  .tippy-box[data-placement^='top'] > .tippy-arrow {
    bottom: 0;
  }

  .tippy-box[data-placement^='top'] > .tippy-arrow:before {
    bottom: -7px;
    left: 0;
    border-width: 8px 8px 0;
    border-top-color: initial;
    -webkit-transform-origin: center top;
    transform-origin: center top;
  }

  .tippy-box[data-placement^='bottom'] > .tippy-arrow {
    top: 0;
  }

  .tippy-box[data-placement^='bottom'] > .tippy-arrow:before {
    top: -7px;
    left: 0;
    border-width: 0 8px 8px;
    border-bottom-color: initial;
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
  }

  .tippy-box[data-placement^='left'] > .tippy-arrow {
    right: 0;
  }

  .tippy-box[data-placement^='left'] > .tippy-arrow:before {
    border-width: 8px 0 8px 8px;
    border-left-color: initial;
    right: -7px;
    -webkit-transform-origin: center left;
    transform-origin: center left;
  }

  .tippy-box[data-placement^='right'] > .tippy-arrow {
    left: 0;
  }

  .tippy-box[data-placement^='right'] > .tippy-arrow:before {
    left: -7px;
    border-width: 8px 8px 8px 0;
    border-right-color: initial;
    -webkit-transform-origin: center right;
    transform-origin: center right;
  }

  .tippy-box[data-inertia][data-state='visible'] {
    -webkit-transition-timing-function: cubic-bezier(0.54, 1.5, 0.38, 1.11);
    transition-timing-function: cubic-bezier(0.54, 1.5, 0.38, 1.11);
  }

  .tippy-arrow {
    width: 16px;
    height: 16px;
    color: #333;
  }

  .tippy-arrow:before {
    content: '';
    position: absolute;
    border-color: transparent;
    border-style: solid;
  }

  .tippy-content {
    position: relative;
    padding: 5px 9px;
    z-index: 1;
  }

  .tippy-box[data-theme~='light-border'] {
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 8, 16, 0.15);
    color: #333;
    -webkit-box-shadow: 0 4px 14px -2px rgba(0, 8, 16, 0.08);
    box-shadow: 0 4px 14px -2px rgba(0, 8, 16, 0.08);
  }

  .tippy-box[data-theme~='light-border'] > .tippy-backdrop {
    background-color: #fff;
  }

  .tippy-box[data-theme~='light-border'] > .tippy-arrow:after,
  .tippy-box[data-theme~='light-border'] > .tippy-svg-arrow:after {
    content: '';
    position: absolute;
    z-index: -1;
  }

  .tippy-box[data-theme~='light-border'] > .tippy-arrow:after {
    border-color: transparent;
    border-style: solid;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='top'] > .tippy-arrow:before {
    border-top-color: #fff;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='top'] > .tippy-arrow:after {
    border-top-color: rgba(0, 8, 16, 0.2);
    border-width: 7px 7px 0;
    top: 17px;
    left: 1px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='top'] > .tippy-svg-arrow > svg {
    top: 16px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='top'] > .tippy-svg-arrow:after {
    top: 17px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='bottom'] > .tippy-arrow:before {
    border-bottom-color: #fff;
    bottom: 16px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='bottom'] > .tippy-arrow:after {
    border-bottom-color: rgba(0, 8, 16, 0.2);
    border-width: 0 7px 7px;
    bottom: 17px;
    left: 1px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='bottom'] > .tippy-svg-arrow > svg {
    bottom: 16px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='bottom'] > .tippy-svg-arrow:after {
    bottom: 17px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='left'] > .tippy-arrow:before {
    border-left-color: #fff;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='left'] > .tippy-arrow:after {
    border-left-color: rgba(0, 8, 16, 0.2);
    border-width: 7px 0 7px 7px;
    left: 17px;
    top: 1px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='left'] > .tippy-svg-arrow > svg {
    left: 11px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='left'] > .tippy-svg-arrow:after {
    left: 12px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='right'] > .tippy-arrow:before {
    border-right-color: #fff;
    right: 16px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='right'] > .tippy-arrow:after {
    border-width: 7px 7px 7px 0;
    right: 17px;
    top: 1px;
    border-right-color: rgba(0, 8, 16, 0.2);
  }

  .tippy-box[data-theme~='light-border'][data-placement^='right'] > .tippy-svg-arrow > svg {
    right: 11px;
  }

  .tippy-box[data-theme~='light-border'][data-placement^='right'] > .tippy-svg-arrow:after {
    right: 12px;
  }

  .tippy-box[data-theme~='light-border'] > .tippy-svg-arrow {
    fill: #fff;
  }

  .tippy-box[data-theme~='light-border'] > .tippy-svg-arrow:after {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCA2czEuNzk2LS4wMTMgNC42Ny0zLjYxNUM1Ljg1MS45IDYuOTMuMDA2IDggMGMxLjA3LS4wMDYgMi4xNDguODg3IDMuMzQzIDIuMzg1QzE0LjIzMyA2LjAwNSAxNiA2IDE2IDZIMHoiIGZpbGw9InJnYmEoMCwgOCwgMTYsIDAuMikiLz48L3N2Zz4=);
    background-size: 16px 6px;
    width: 16px;
    height: 6px;
  }

  .bytemd {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Arial, sans-serif, Apple Color Emoji,
      Segoe UI Emoji;
    color: #24292e;
    border: 1px solid #e1e4e8;
    background-color: #fff;
    height: 300px;
  }

  .bytemd,
  .bytemd * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .bytemd-hidden {
    display: none !important;
  }

  .bytemd .CodeMirror-gutter,
  .bytemd .CodeMirror-gutters,
  .bytemd .CodeMirror-linenumber,
  .bytemd .CodeMirror-scroll,
  .bytemd .CodeMirror-sizer {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
  }

  .bytemd .CodeMirror,
  .bytemd code,
  .bytemd kbd {
    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
  }

  .bytemd-toolbar {
    padding: 4px 12px;
    border-bottom: 1px solid #e1e4e8;
    background-color: #fafbfc;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
  }

  .bytemd-toolbar-left {
    float: left;
  }

  .bytemd-toolbar-right {
    float: right;
  }

  .bytemd-toolbar-tab {
    display: inline-block;
    cursor: pointer;
    padding-left: 8px;
    padding-right: 8px;
    line-height: 24px;
    font-size: 14px;
  }

  .bytemd-toolbar-tab-active {
    color: #0366d6;
  }

  .bytemd-toolbar-icon {
    display: inline-block;
    vertical-align: top;
    cursor: pointer;
    border-radius: 4px;
    margin-left: 6px;
    margin-right: 6px;
  }

  .bytemd-toolbar-icon img,
  .bytemd-toolbar-icon svg {
    display: block;
    padding: 4px;
    width: 24px;
    height: 24px;
  }

  .bytemd-toolbar-icon:hover {
    background-color: #e1e4e8;
  }

  .bytemd-toolbar-icon-active {
    color: #0366d6;
  }

  .bytemd-toolbar .tippy-content {
    padding-left: 0;
    padding-right: 0;
  }

  .bytemd-dropdown {
    max-height: 300px;
    overflow: auto;
    font-size: 14px;
  }

  .bytemd-dropdown-title {
    margin: 0 12px;
    font-weight: 500;
    border-bottom: 1px solid #e1e4e8;
    line-height: 32px;
    color: #444d56;
  }

  .bytemd-dropdown-item {
    padding: 4px 12px;
    height: 32px;
    cursor: pointer;
  }

  .bytemd-dropdown-item:hover {
    background-color: #f6f8fa;
  }

  .bytemd-dropdown-item-icon {
    display: inline-block;
  }

  .bytemd-dropdown-item-icon svg {
    display: block;
    padding: 4px;
    width: 24px;
    height: 24px;
  }

  .bytemd-dropdown-item-title {
    display: inline-block;
    line-height: 24px;
    vertical-align: top;
  }

  .bytemd-body {
    height: calc(100% - 58px);
    overflow: auto;
  }

  .bytemd-editor {
    display: inline-block;
    vertical-align: top;
    height: 100%;
    overflow: hidden;
  }

  .bytemd-editor .CodeMirror {
    height: 100%;
    font-size: 14px;
    line-height: 1.5;
  }

  .bytemd-editor .CodeMirror pre.CodeMirror-placeholder {
    color: #959da5;
  }

  .bytemd-editor .CodeMirror .CodeMirror-lines {
    max-width: 800px;
    margin: 0 auto;
    padding: 16px 0;
  }

  .bytemd-editor .CodeMirror pre.CodeMirror-line,
  .bytemd-editor .CodeMirror pre.CodeMirror-line-like {
    padding: 0 4%;
  }

  .bytemd-preview {
    display: inline-block;
    vertical-align: top;
    height: 100%;
    overflow: auto;
  }

  .bytemd-preview .markdown-body {
    max-width: 800px;
    margin: 0 auto;
    padding: 16px 4%;
  }

  .bytemd-sidebar {
    display: inline-block;
    vertical-align: top;
    height: 100%;
    overflow: auto;
    font-size: 16px;
    border-left: 1px solid #e1e4e8;
    width: 280px;
    position: relative;
    padding: 0 16px;
  }

  .bytemd-sidebar-close {
    position: absolute;
    padding: 16px;
    top: 0;
    right: 0;
    cursor: pointer;
  }

  .bytemd-sidebar-close:hover {
    color: #0366d6;
  }

  .bytemd-sidebar h2 {
    font-size: 16px;
    font-weight: 600;
    margin: 32px 0 16px;
  }

  .bytemd-sidebar ul {
    padding-left: 0;
    color: #959da5;
  }

  .bytemd-help {
    font-size: 13px;
  }

  .bytemd-help ul {
    line-height: 20px;
  }

  .bytemd-help ul svg {
    width: 16px;
    height: 16px;
    display: block;
  }

  .bytemd-help ul div {
    display: inline-block;
    vertical-align: top;
  }

  .bytemd-help li {
    list-style: none;
    margin-bottom: 12px;
  }

  .bytemd-help-icon {
    padding: 2px 0;
  }

  .bytemd-help-title {
    padding-left: 8px;
  }

  .bytemd-help-content {
    float: right;
    font-size: 12px;
  }

  .bytemd-toc li {
    list-style: none;
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 2;
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .bytemd-toc-active {
    color: #0366d6;
    background-color: #f6f8fa;
  }

  .bytemd-toc-first {
    font-weight: 500;
  }

  .bytemd-status {
    font-size: 12px;
    line-height: 24px;
    border-top: 1px solid #e1e4e8;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .bytemd-status-left {
    float: left;
  }

  .bytemd-status-left span {
    padding-left: 16px;
  }

  .bytemd-status-left strong {
    font-weight: 600;
  }

  .bytemd-status-right {
    float: right;
  }

  .bytemd-status-right label,
  .bytemd-status-right span {
    margin-right: 16px;
    cursor: pointer;
  }

  .bytemd-status-right span:hover {
    color: #0366d6;
  }

  .bytemd-status-right input {
    vertical-align: middle;
    margin-right: 3px;
  }

  .bytemd-status-error {
    color: #d73a49;
  }

  .bytemd-fullscreen.bytemd {
    position: fixed;
    inset: 0;
    border: none;
    height: 100vh !important;
  }

  .bytemd-split .bytemd-preview {
    border-left: 1px solid #e1e4e8;
  }

  .tippy-box {
    font-size: 12px;
  }

  .katex {
    font: normal 1.21em KaTeX_Main, Times New Roman, serif;
    line-height: 1.2;
    text-indent: 0;
    text-rendering: auto;
    border-color: currentColor;
  }

  .katex * {
    -ms-high-contrast-adjust: none !important;
  }

  .katex .katex-version:after {
    content: '0.12.0';
  }

  .katex .katex-mathml {
    position: absolute;
    clip: rect(1px, 1px, 1px, 1px);
    padding: 0;
    border: 0;
    height: 1px;
    width: 1px;
    overflow: hidden;
  }

  .katex .katex-html > .newline {
    display: block;
  }

  .katex .base {
    position: relative;
    white-space: nowrap;
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content;
  }

  .katex .base,
  .katex .strut {
    display: inline-block;
  }

  .katex .textbf {
    font-weight: 700;
  }

  .katex .textit {
    font-style: italic;
  }

  .katex .textrm {
    font-family: KaTeX_Main;
  }

  .katex .textsf {
    font-family: KaTeX_SansSerif;
  }

  .katex .texttt {
    font-family: KaTeX_Typewriter;
  }

  .katex .mathnormal {
    font-family: KaTeX_Math;
    font-style: italic;
  }

  .katex .mathit {
    font-family: KaTeX_Main;
    font-style: italic;
  }

  .katex .mathrm {
    font-style: normal;
  }

  .katex .mathbf {
    font-family: KaTeX_Main;
    font-weight: 700;
  }

  .katex .boldsymbol {
    font-family: KaTeX_Math;
    font-weight: 700;
    font-style: italic;
  }

  .katex .amsrm,
  .katex .mathbb,
  .katex .textbb {
    font-family: KaTeX_AMS;
  }

  .katex .mathcal {
    font-family: KaTeX_Caligraphic;
  }

  .katex .mathfrak,
  .katex .textfrak {
    font-family: KaTeX_Fraktur;
  }

  .katex .mathtt {
    font-family: KaTeX_Typewriter;
  }

  .katex .mathscr,
  .katex .textscr {
    font-family: KaTeX_Script;
  }

  .katex .mathsf,
  .katex .textsf {
    font-family: KaTeX_SansSerif;
  }

  .katex .mathboldsf,
  .katex .textboldsf {
    font-family: KaTeX_SansSerif;
    font-weight: 700;
  }

  .katex .mathitsf,
  .katex .textitsf {
    font-family: KaTeX_SansSerif;
    font-style: italic;
  }

  .katex .mainrm {
    font-family: KaTeX_Main;
    font-style: normal;
  }

  .katex .vlist-t {
    display: inline-table;
    table-layout: fixed;
    border-collapse: collapse;
  }

  .katex .vlist-r {
    display: table-row;
  }

  .katex .vlist {
    display: table-cell;
    vertical-align: bottom;
    position: relative;
  }

  .katex .vlist > span {
    display: block;
    height: 0;
    position: relative;
  }

  .katex .vlist > span > span {
    display: inline-block;
  }

  .katex .vlist > span > .pstrut {
    overflow: hidden;
    width: 0;
  }

  .katex .vlist-t2 {
    margin-right: -2px;
  }

  .katex .vlist-s {
    display: table-cell;
    vertical-align: bottom;
    font-size: 1px;
    width: 2px;
    min-width: 2px;
  }

  .katex .vbox {
    display: -ms-inline-flexbox;
    display: -webkit-inline-box;
    display: inline-flex;
    -ms-flex-direction: column;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
  }

  .katex .hbox {
    width: 100%;
  }

  .katex .hbox,
  .katex .thinbox {
    display: -ms-inline-flexbox;
    display: -webkit-inline-box;
    display: inline-flex;
    -ms-flex-direction: row;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
  }

  .katex .thinbox {
    width: 0;
    max-width: 0;
  }

  .katex .msupsub {
    text-align: left;
  }

  .katex .mfrac > span > span {
    text-align: center;
  }

  .katex .mfrac .frac-line {
    display: inline-block;
    width: 100%;
    border-bottom-style: solid;
  }

  .katex .hdashline,
  .katex .hline,
  .katex .mfrac .frac-line,
  .katex .overline .overline-line,
  .katex .rule,
  .katex .underline .underline-line {
    min-height: 1px;
  }

  .katex .mspace {
    display: inline-block;
  }

  .katex .clap,
  .katex .llap,
  .katex .rlap {
    width: 0;
    position: relative;
  }

  .katex .clap > .inner,
  .katex .llap > .inner,
  .katex .rlap > .inner {
    position: absolute;
  }

  .katex .clap > .fix,
  .katex .llap > .fix,
  .katex .rlap > .fix {
    display: inline-block;
  }

  .katex .llap > .inner {
    right: 0;
  }

  .katex .clap > .inner,
  .katex .rlap > .inner {
    left: 0;
  }

  .katex .clap > .inner > span {
    margin-left: -50%;
    margin-right: 50%;
  }

  .katex .rule {
    display: inline-block;
    border: 0 solid;
    position: relative;
  }

  .katex .hline,
  .katex .overline .overline-line,
  .katex .underline .underline-line {
    display: inline-block;
    width: 100%;
    border-bottom-style: solid;
  }

  .katex .hdashline {
    display: inline-block;
    width: 100%;
    border-bottom-style: dashed;
  }

  .katex .sqrt > .root {
    margin-left: 0.27777778em;
    margin-right: -0.55555556em;
  }

  .katex .fontsize-ensurer.reset-size1.size1,
  .katex .sizing.reset-size1.size1 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size1.size2,
  .katex .sizing.reset-size1.size2 {
    font-size: 1.2em;
  }

  .katex .fontsize-ensurer.reset-size1.size3,
  .katex .sizing.reset-size1.size3 {
    font-size: 1.4em;
  }

  .katex .fontsize-ensurer.reset-size1.size4,
  .katex .sizing.reset-size1.size4 {
    font-size: 1.6em;
  }

  .katex .fontsize-ensurer.reset-size1.size5,
  .katex .sizing.reset-size1.size5 {
    font-size: 1.8em;
  }

  .katex .fontsize-ensurer.reset-size1.size6,
  .katex .sizing.reset-size1.size6 {
    font-size: 2em;
  }

  .katex .fontsize-ensurer.reset-size1.size7,
  .katex .sizing.reset-size1.size7 {
    font-size: 2.4em;
  }

  .katex .fontsize-ensurer.reset-size1.size8,
  .katex .sizing.reset-size1.size8 {
    font-size: 2.88em;
  }

  .katex .fontsize-ensurer.reset-size1.size9,
  .katex .sizing.reset-size1.size9 {
    font-size: 3.456em;
  }

  .katex .fontsize-ensurer.reset-size1.size10,
  .katex .sizing.reset-size1.size10 {
    font-size: 4.148em;
  }

  .katex .fontsize-ensurer.reset-size1.size11,
  .katex .sizing.reset-size1.size11 {
    font-size: 4.976em;
  }

  .katex .fontsize-ensurer.reset-size2.size1,
  .katex .sizing.reset-size2.size1 {
    font-size: 0.83333333em;
  }

  .katex .fontsize-ensurer.reset-size2.size2,
  .katex .sizing.reset-size2.size2 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size2.size3,
  .katex .sizing.reset-size2.size3 {
    font-size: 1.16666667em;
  }

  .katex .fontsize-ensurer.reset-size2.size4,
  .katex .sizing.reset-size2.size4 {
    font-size: 1.33333333em;
  }

  .katex .fontsize-ensurer.reset-size2.size5,
  .katex .sizing.reset-size2.size5 {
    font-size: 1.5em;
  }

  .katex .fontsize-ensurer.reset-size2.size6,
  .katex .sizing.reset-size2.size6 {
    font-size: 1.66666667em;
  }

  .katex .fontsize-ensurer.reset-size2.size7,
  .katex .sizing.reset-size2.size7 {
    font-size: 2em;
  }

  .katex .fontsize-ensurer.reset-size2.size8,
  .katex .sizing.reset-size2.size8 {
    font-size: 2.4em;
  }

  .katex .fontsize-ensurer.reset-size2.size9,
  .katex .sizing.reset-size2.size9 {
    font-size: 2.88em;
  }

  .katex .fontsize-ensurer.reset-size2.size10,
  .katex .sizing.reset-size2.size10 {
    font-size: 3.45666667em;
  }

  .katex .fontsize-ensurer.reset-size2.size11,
  .katex .sizing.reset-size2.size11 {
    font-size: 4.14666667em;
  }

  .katex .fontsize-ensurer.reset-size3.size1,
  .katex .sizing.reset-size3.size1 {
    font-size: 0.71428571em;
  }

  .katex .fontsize-ensurer.reset-size3.size2,
  .katex .sizing.reset-size3.size2 {
    font-size: 0.85714286em;
  }

  .katex .fontsize-ensurer.reset-size3.size3,
  .katex .sizing.reset-size3.size3 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size3.size4,
  .katex .sizing.reset-size3.size4 {
    font-size: 1.14285714em;
  }

  .katex .fontsize-ensurer.reset-size3.size5,
  .katex .sizing.reset-size3.size5 {
    font-size: 1.28571429em;
  }

  .katex .fontsize-ensurer.reset-size3.size6,
  .katex .sizing.reset-size3.size6 {
    font-size: 1.42857143em;
  }

  .katex .fontsize-ensurer.reset-size3.size7,
  .katex .sizing.reset-size3.size7 {
    font-size: 1.71428571em;
  }

  .katex .fontsize-ensurer.reset-size3.size8,
  .katex .sizing.reset-size3.size8 {
    font-size: 2.05714286em;
  }

  .katex .fontsize-ensurer.reset-size3.size9,
  .katex .sizing.reset-size3.size9 {
    font-size: 2.46857143em;
  }

  .katex .fontsize-ensurer.reset-size3.size10,
  .katex .sizing.reset-size3.size10 {
    font-size: 2.96285714em;
  }

  .katex .fontsize-ensurer.reset-size3.size11,
  .katex .sizing.reset-size3.size11 {
    font-size: 3.55428571em;
  }

  .katex .fontsize-ensurer.reset-size4.size1,
  .katex .sizing.reset-size4.size1 {
    font-size: 0.625em;
  }

  .katex .fontsize-ensurer.reset-size4.size2,
  .katex .sizing.reset-size4.size2 {
    font-size: 0.75em;
  }

  .katex .fontsize-ensurer.reset-size4.size3,
  .katex .sizing.reset-size4.size3 {
    font-size: 0.875em;
  }

  .katex .fontsize-ensurer.reset-size4.size4,
  .katex .sizing.reset-size4.size4 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size4.size5,
  .katex .sizing.reset-size4.size5 {
    font-size: 1.125em;
  }

  .katex .fontsize-ensurer.reset-size4.size6,
  .katex .sizing.reset-size4.size6 {
    font-size: 1.25em;
  }

  .katex .fontsize-ensurer.reset-size4.size7,
  .katex .sizing.reset-size4.size7 {
    font-size: 1.5em;
  }

  .katex .fontsize-ensurer.reset-size4.size8,
  .katex .sizing.reset-size4.size8 {
    font-size: 1.8em;
  }

  .katex .fontsize-ensurer.reset-size4.size9,
  .katex .sizing.reset-size4.size9 {
    font-size: 2.16em;
  }

  .katex .fontsize-ensurer.reset-size4.size10,
  .katex .sizing.reset-size4.size10 {
    font-size: 2.5925em;
  }

  .katex .fontsize-ensurer.reset-size4.size11,
  .katex .sizing.reset-size4.size11 {
    font-size: 3.11em;
  }

  .katex .fontsize-ensurer.reset-size5.size1,
  .katex .sizing.reset-size5.size1 {
    font-size: 0.55555556em;
  }

  .katex .fontsize-ensurer.reset-size5.size2,
  .katex .sizing.reset-size5.size2 {
    font-size: 0.66666667em;
  }

  .katex .fontsize-ensurer.reset-size5.size3,
  .katex .sizing.reset-size5.size3 {
    font-size: 0.77777778em;
  }

  .katex .fontsize-ensurer.reset-size5.size4,
  .katex .sizing.reset-size5.size4 {
    font-size: 0.88888889em;
  }

  .katex .fontsize-ensurer.reset-size5.size5,
  .katex .sizing.reset-size5.size5 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size5.size6,
  .katex .sizing.reset-size5.size6 {
    font-size: 1.11111111em;
  }

  .katex .fontsize-ensurer.reset-size5.size7,
  .katex .sizing.reset-size5.size7 {
    font-size: 1.33333333em;
  }

  .katex .fontsize-ensurer.reset-size5.size8,
  .katex .sizing.reset-size5.size8 {
    font-size: 1.6em;
  }

  .katex .fontsize-ensurer.reset-size5.size9,
  .katex .sizing.reset-size5.size9 {
    font-size: 1.92em;
  }

  .katex .fontsize-ensurer.reset-size5.size10,
  .katex .sizing.reset-size5.size10 {
    font-size: 2.30444444em;
  }

  .katex .fontsize-ensurer.reset-size5.size11,
  .katex .sizing.reset-size5.size11 {
    font-size: 2.76444444em;
  }

  .katex .fontsize-ensurer.reset-size6.size1,
  .katex .sizing.reset-size6.size1 {
    font-size: 0.5em;
  }

  .katex .fontsize-ensurer.reset-size6.size2,
  .katex .sizing.reset-size6.size2 {
    font-size: 0.6em;
  }

  .katex .fontsize-ensurer.reset-size6.size3,
  .katex .sizing.reset-size6.size3 {
    font-size: 0.7em;
  }

  .katex .fontsize-ensurer.reset-size6.size4,
  .katex .sizing.reset-size6.size4 {
    font-size: 0.8em;
  }

  .katex .fontsize-ensurer.reset-size6.size5,
  .katex .sizing.reset-size6.size5 {
    font-size: 0.9em;
  }

  .katex .fontsize-ensurer.reset-size6.size6,
  .katex .sizing.reset-size6.size6 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size6.size7,
  .katex .sizing.reset-size6.size7 {
    font-size: 1.2em;
  }

  .katex .fontsize-ensurer.reset-size6.size8,
  .katex .sizing.reset-size6.size8 {
    font-size: 1.44em;
  }

  .katex .fontsize-ensurer.reset-size6.size9,
  .katex .sizing.reset-size6.size9 {
    font-size: 1.728em;
  }

  .katex .fontsize-ensurer.reset-size6.size10,
  .katex .sizing.reset-size6.size10 {
    font-size: 2.074em;
  }

  .katex .fontsize-ensurer.reset-size6.size11,
  .katex .sizing.reset-size6.size11 {
    font-size: 2.488em;
  }

  .katex .fontsize-ensurer.reset-size7.size1,
  .katex .sizing.reset-size7.size1 {
    font-size: 0.41666667em;
  }

  .katex .fontsize-ensurer.reset-size7.size2,
  .katex .sizing.reset-size7.size2 {
    font-size: 0.5em;
  }

  .katex .fontsize-ensurer.reset-size7.size3,
  .katex .sizing.reset-size7.size3 {
    font-size: 0.58333333em;
  }

  .katex .fontsize-ensurer.reset-size7.size4,
  .katex .sizing.reset-size7.size4 {
    font-size: 0.66666667em;
  }

  .katex .fontsize-ensurer.reset-size7.size5,
  .katex .sizing.reset-size7.size5 {
    font-size: 0.75em;
  }

  .katex .fontsize-ensurer.reset-size7.size6,
  .katex .sizing.reset-size7.size6 {
    font-size: 0.83333333em;
  }

  .katex .fontsize-ensurer.reset-size7.size7,
  .katex .sizing.reset-size7.size7 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size7.size8,
  .katex .sizing.reset-size7.size8 {
    font-size: 1.2em;
  }

  .katex .fontsize-ensurer.reset-size7.size9,
  .katex .sizing.reset-size7.size9 {
    font-size: 1.44em;
  }

  .katex .fontsize-ensurer.reset-size7.size10,
  .katex .sizing.reset-size7.size10 {
    font-size: 1.72833333em;
  }

  .katex .fontsize-ensurer.reset-size7.size11,
  .katex .sizing.reset-size7.size11 {
    font-size: 2.07333333em;
  }

  .katex .fontsize-ensurer.reset-size8.size1,
  .katex .sizing.reset-size8.size1 {
    font-size: 0.34722222em;
  }

  .katex .fontsize-ensurer.reset-size8.size2,
  .katex .sizing.reset-size8.size2 {
    font-size: 0.41666667em;
  }

  .katex .fontsize-ensurer.reset-size8.size3,
  .katex .sizing.reset-size8.size3 {
    font-size: 0.48611111em;
  }

  .katex .fontsize-ensurer.reset-size8.size4,
  .katex .sizing.reset-size8.size4 {
    font-size: 0.55555556em;
  }

  .katex .fontsize-ensurer.reset-size8.size5,
  .katex .sizing.reset-size8.size5 {
    font-size: 0.625em;
  }

  .katex .fontsize-ensurer.reset-size8.size6,
  .katex .sizing.reset-size8.size6 {
    font-size: 0.69444444em;
  }

  .katex .fontsize-ensurer.reset-size8.size7,
  .katex .sizing.reset-size8.size7 {
    font-size: 0.83333333em;
  }

  .katex .fontsize-ensurer.reset-size8.size8,
  .katex .sizing.reset-size8.size8 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size8.size9,
  .katex .sizing.reset-size8.size9 {
    font-size: 1.2em;
  }

  .katex .fontsize-ensurer.reset-size8.size10,
  .katex .sizing.reset-size8.size10 {
    font-size: 1.44027778em;
  }

  .katex .fontsize-ensurer.reset-size8.size11,
  .katex .sizing.reset-size8.size11 {
    font-size: 1.72777778em;
  }

  .katex .fontsize-ensurer.reset-size9.size1,
  .katex .sizing.reset-size9.size1 {
    font-size: 0.28935185em;
  }

  .katex .fontsize-ensurer.reset-size9.size2,
  .katex .sizing.reset-size9.size2 {
    font-size: 0.34722222em;
  }

  .katex .fontsize-ensurer.reset-size9.size3,
  .katex .sizing.reset-size9.size3 {
    font-size: 0.40509259em;
  }

  .katex .fontsize-ensurer.reset-size9.size4,
  .katex .sizing.reset-size9.size4 {
    font-size: 0.46296296em;
  }

  .katex .fontsize-ensurer.reset-size9.size5,
  .katex .sizing.reset-size9.size5 {
    font-size: 0.52083333em;
  }

  .katex .fontsize-ensurer.reset-size9.size6,
  .katex .sizing.reset-size9.size6 {
    font-size: 0.5787037em;
  }

  .katex .fontsize-ensurer.reset-size9.size7,
  .katex .sizing.reset-size9.size7 {
    font-size: 0.69444444em;
  }

  .katex .fontsize-ensurer.reset-size9.size8,
  .katex .sizing.reset-size9.size8 {
    font-size: 0.83333333em;
  }

  .katex .fontsize-ensurer.reset-size9.size9,
  .katex .sizing.reset-size9.size9 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size9.size10,
  .katex .sizing.reset-size9.size10 {
    font-size: 1.20023148em;
  }

  .katex .fontsize-ensurer.reset-size9.size11,
  .katex .sizing.reset-size9.size11 {
    font-size: 1.43981481em;
  }

  .katex .fontsize-ensurer.reset-size10.size1,
  .katex .sizing.reset-size10.size1 {
    font-size: 0.24108004em;
  }

  .katex .fontsize-ensurer.reset-size10.size2,
  .katex .sizing.reset-size10.size2 {
    font-size: 0.28929605em;
  }

  .katex .fontsize-ensurer.reset-size10.size3,
  .katex .sizing.reset-size10.size3 {
    font-size: 0.33751205em;
  }

  .katex .fontsize-ensurer.reset-size10.size4,
  .katex .sizing.reset-size10.size4 {
    font-size: 0.38572806em;
  }

  .katex .fontsize-ensurer.reset-size10.size5,
  .katex .sizing.reset-size10.size5 {
    font-size: 0.43394407em;
  }

  .katex .fontsize-ensurer.reset-size10.size6,
  .katex .sizing.reset-size10.size6 {
    font-size: 0.48216008em;
  }

  .katex .fontsize-ensurer.reset-size10.size7,
  .katex .sizing.reset-size10.size7 {
    font-size: 0.57859209em;
  }

  .katex .fontsize-ensurer.reset-size10.size8,
  .katex .sizing.reset-size10.size8 {
    font-size: 0.69431051em;
  }

  .katex .fontsize-ensurer.reset-size10.size9,
  .katex .sizing.reset-size10.size9 {
    font-size: 0.83317261em;
  }

  .katex .fontsize-ensurer.reset-size10.size10,
  .katex .sizing.reset-size10.size10 {
    font-size: 1em;
  }

  .katex .fontsize-ensurer.reset-size10.size11,
  .katex .sizing.reset-size10.size11 {
    font-size: 1.19961427em;
  }

  .katex .fontsize-ensurer.reset-size11.size1,
  .katex .sizing.reset-size11.size1 {
    font-size: 0.20096463em;
  }

  .katex .fontsize-ensurer.reset-size11.size2,
  .katex .sizing.reset-size11.size2 {
    font-size: 0.24115756em;
  }

  .katex .fontsize-ensurer.reset-size11.size3,
  .katex .sizing.reset-size11.size3 {
    font-size: 0.28135048em;
  }

  .katex .fontsize-ensurer.reset-size11.size4,
  .katex .sizing.reset-size11.size4 {
    font-size: 0.32154341em;
  }

  .katex .fontsize-ensurer.reset-size11.size5,
  .katex .sizing.reset-size11.size5 {
    font-size: 0.36173633em;
  }

  .katex .fontsize-ensurer.reset-size11.size6,
  .katex .sizing.reset-size11.size6 {
    font-size: 0.40192926em;
  }

  .katex .fontsize-ensurer.reset-size11.size7,
  .katex .sizing.reset-size11.size7 {
    font-size: 0.48231511em;
  }

  .katex .fontsize-ensurer.reset-size11.size8,
  .katex .sizing.reset-size11.size8 {
    font-size: 0.57877814em;
  }

  .katex .fontsize-ensurer.reset-size11.size9,
  .katex .sizing.reset-size11.size9 {
    font-size: 0.69453376em;
  }

  .katex .fontsize-ensurer.reset-size11.size10,
  .katex .sizing.reset-size11.size10 {
    font-size: 0.83360129em;
  }

  .katex .fontsize-ensurer.reset-size11.size11,
  .katex .sizing.reset-size11.size11 {
    font-size: 1em;
  }

  .katex .delimsizing.size1 {
    font-family: KaTeX_Size1;
  }

  .katex .delimsizing.size2 {
    font-family: KaTeX_Size2;
  }

  .katex .delimsizing.size3 {
    font-family: KaTeX_Size3;
  }

  .katex .delimsizing.size4 {
    font-family: KaTeX_Size4;
  }

  .katex .delimsizing.mult .delim-size1 > span {
    font-family: KaTeX_Size1;
  }

  .katex .delimsizing.mult .delim-size4 > span {
    font-family: KaTeX_Size4;
  }

  .katex .nulldelimiter {
    display: inline-block;
    width: 0.12em;
  }

  .katex .delimcenter,
  .katex .op-symbol {
    position: relative;
  }

  .katex .op-symbol.small-op {
    font-family: KaTeX_Size1;
  }

  .katex .op-symbol.large-op {
    font-family: KaTeX_Size2;
  }

  .katex .accent > .vlist-t,
  .katex .op-limits > .vlist-t {
    text-align: center;
  }

  .katex .accent .accent-body {
    position: relative;
  }

  .katex .accent .accent-body:not(.accent-full) {
    width: 0;
  }

  .katex .overlay {
    display: block;
  }

  .katex .mtable .vertical-separator {
    display: inline-block;
    min-width: 1px;
  }

  .katex .mtable .arraycolsep {
    display: inline-block;
  }

  .katex .mtable .col-align-c > .vlist-t {
    text-align: center;
  }

  .katex .mtable .col-align-l > .vlist-t {
    text-align: left;
  }

  .katex .mtable .col-align-r > .vlist-t {
    text-align: right;
  }

  .katex .svg-align {
    text-align: left;
  }

  .katex svg {
    display: block;
    position: absolute;
    width: 100%;
    height: inherit;
    fill: currentColor;
    stroke: currentColor;
    fill-rule: nonzero;
    fill-opacity: 1;
    stroke-width: 1;
    stroke-linecap: butt;
    stroke-linejoin: miter;
    stroke-miterlimit: 4;
    stroke-dasharray: none;
    stroke-dashoffset: 0;
    stroke-opacity: 1;
  }

  .katex svg path {
    stroke: none;
  }

  .katex img {
    border-style: none;
    min-width: 0;
    min-height: 0;
    max-width: none;
    max-height: none;
  }

  .katex .stretchy {
    width: 100%;
    display: block;
    position: relative;
    overflow: hidden;
  }

  .katex .stretchy:after,
  .katex .stretchy:before {
    content: '';
  }

  .katex .hide-tail {
    width: 100%;
    position: relative;
    overflow: hidden;
  }

  .katex .halfarrow-left {
    position: absolute;
    left: 0;
    width: 50.2%;
    overflow: hidden;
  }

  .katex .halfarrow-right {
    position: absolute;
    right: 0;
    width: 50.2%;
    overflow: hidden;
  }

  .katex .brace-left {
    position: absolute;
    left: 0;
    width: 25.1%;
    overflow: hidden;
  }

  .katex .brace-center {
    position: absolute;
    left: 25%;
    width: 50%;
    overflow: hidden;
  }

  .katex .brace-right {
    position: absolute;
    right: 0;
    width: 25.1%;
    overflow: hidden;
  }

  .katex .x-arrow-pad {
    padding: 0 0.5em;
  }

  .katex .mover,
  .katex .munder,
  .katex .x-arrow {
    text-align: center;
  }

  .katex .boxpad {
    padding: 0 0.3em 0 0.3em;
  }

  .katex .fbox,
  .katex .fcolorbox {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 0.04em solid;
  }

  .katex .cancel-pad {
    padding: 0 0.2em 0 0.2em;
  }

  .katex .cancel-lap {
    margin-left: -0.2em;
    margin-right: -0.2em;
  }

  .katex .sout {
    border-bottom-style: solid;
    border-bottom-width: 0.08em;
  }

  .katex-display {
    display: block;
    margin: 1em 0;
    text-align: center;
  }

  .katex-display > .katex {
    display: block;
    text-align: center;
    white-space: nowrap;
  }

  .katex-display > .katex > .katex-html {
    display: block;
    position: relative;
  }

  .katex-display > .katex > .katex-html > .tag {
    position: absolute;
    right: 0;
  }

  .katex-display.leqno > .katex > .katex-html > .tag {
    left: 0;
    right: auto;
  }

  .katex-display.fleqn > .katex {
    text-align: left;
    padding-left: 2em;
  }

  .tippy-box[data-theme~='light'] {
    color: #26323d;
    box-shadow: 0 0 20px 4px rgba(154, 161, 177, 0.15), 0 4px 80px -8px rgba(36, 40, 47, 0.25),
      0 4px 4px -2px rgba(91, 94, 105, 0.15);
    background-color: #fff;
  }

  .tippy-box[data-theme~='light'][data-placement^='top'] > .tippy-arrow:before {
    border-top-color: #fff;
  }

  .tippy-box[data-theme~='light'][data-placement^='bottom'] > .tippy-arrow:before {
    border-bottom-color: #fff;
  }

  .tippy-box[data-theme~='light'][data-placement^='left'] > .tippy-arrow:before {
    border-left-color: #fff;
  }

  .tippy-box[data-theme~='light'][data-placement^='right'] > .tippy-arrow:before {
    border-right-color: #fff;
  }

  .tippy-box[data-theme~='light'] > .tippy-backdrop {
    background-color: #fff;
  }

  .tippy-box[data-theme~='light'] > .tippy-svg-arrow {
    fill: #fff;
  }
}
