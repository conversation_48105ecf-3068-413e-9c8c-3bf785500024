import { ADMIN_URL } from './constants/api';
import { IS_LOCAL_OR_TEST, IS_TEST } from './utils/constants';
import { isIntl } from './utils/sso';

/**
 * 服务端接口基础路径
 * @param env
 * @returns {string} url
 */
export function serverApiBaseUrl() {
  // const currentEnv = getEnv()
  // const prefix = currentEnv ? `https://zaip-${currentEnv}-opsgw.in.za` : `https://zaip-opsgw.in.za`
  // return `${prefix}/pms-prompt`
  // return isIntl() ? '/api/proxy/intl' : `/api/proxy`
  return '/api/proxy';
}

// 国际地址
const intlAdminUrls: any = {
  dev: 'https://aigc-admin-dev.in.za',
  sit: 'https://aigc-admin-sit.in.za',
  uat: 'https://aigc-admin-uat.in.za',
  prd: 'https://aigc-admin.in.za',
};

export function getAdminUrl() {
  const currentEnv = getEnv();
  // 国际地址判断
  if (isIntl()) {
    return intlAdminUrls[currentEnv === 'dev' ? 'uat' : currentEnv] || intlAdminUrls['uat'];
  }

  // 公网地址判断
  if (isPublicEnv()) {
    const publicUrls: any = {
      prd: 'https://aigc-admin.zhongan.com',
      default: 'https://aigc-admin-test.zhongan.com',
    };
    return publicUrls[currentEnv] || publicUrls['default'];
  }

  const localUrls: any = {
    dev: ADMIN_URL,
    prd: 'https://aigc-admin.zhonganonline.com',
    pre: 'https://aigc-admin-pre.zhonganonline.com',
    default: ADMIN_URL,
  };
  return localUrls[currentEnv] || localUrls['default'];
}

/**
 * 获取环境
 * @returns {string} env
 */
export function getEnv(): string {
  if (IS_TEST) return 'uat';
  if (IS_LOCAL_OR_TEST) return 'dev';
  return 'prd';
}

export function isPrdEnv() {
  return getEnv() === 'prd';
}

export function isPublicEnv() {
  return ['eagle.zhongan.com', 'eagle-test.zhongan.com'].includes(location.hostname);
}
