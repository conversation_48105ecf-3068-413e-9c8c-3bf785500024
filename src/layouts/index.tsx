import Empty from '@/components/Empty';
import LeftNav from '@/components/LeftNav';
import TopNav from '@/components/TopNav';
import useAuth from '@/hooks/useAuth';
import {
  getGroupTree,
  getLabelList,
  getRiskList,
  getStrategyConfigTemplateLimit,
  getWhitelistPermission,
} from '@/services/ce';
import { formatGroupTree } from '@/utils';
import { antConfig } from '@/utils/constants';
import { initFFmpeg } from '@/utils/ffmpeg';
import { StyleProvider } from '@ant-design/cssinjs';
import { useDispatch, useLocation, useSelector } from '@magi/magi';
import { ConfigProvider } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import React, { FC, useCallback, useEffect } from 'react';
import styles from './index.scss';

dayjs.locale('zh-cn');
const Layout: FC<any> = ({ children }) => {
  const location: any = useLocation();
  const { currentTenantNo } = useSelector((state: { global: any }) => state.global);
  const { channel, token, isIframe: _isIframe, tenantNo }: any = location.query || {};
  const isIframe = _isIframe === 'true' || false;
  const hideNav = ['/', '/landing'].includes(location.pathname) || isIframe;
  const dispatch = useDispatch();
  const { init } = useAuth({ tenantNo: (tenantNo || '') as string, channel, token, hideNav });

  // 初始化FFmpeg
  const handleInitFFmpeg = useCallback(async () => {
    await initFFmpeg();
    dispatch({
      type: 'global/save',
      payload: {
        isInitFFmpeg: true,
      },
    });
  }, []);

  useEffect(() => {
    handleInitFFmpeg();
  }, [handleInitFFmpeg]);

  // 获取是否开启白名单
  const handleStrategyConfigTemplateLimit = useCallback(() => {
    getStrategyConfigTemplateLimit().then((res) => {
      dispatch({
        type: 'global/save',
        payload: {
          isStrategy: res?.data?.value ?? false,
        },
      });
    });
  }, []);

  // 菜单权限
  const handleWhitelistPermission = useCallback(() => {
    getWhitelistPermission().then((res) => {
      dispatch({
        type: 'global/save',
        payload: {
          whitelistPermission: res?.data?.value ?? {},
        },
      });
    });
  }, []);

  useEffect(() => {
    handleStrategyConfigTemplateLimit();
    handleWhitelistPermission();
  }, [handleStrategyConfigTemplateLimit, handleWhitelistPermission]);

  const handleLabelList = useCallback(
    (isRedLine?: string) => {
      if (!currentTenantNo) return;
      getLabelList(currentTenantNo, isRedLine).then((res: any) => {
        const list = (res.data.value || []).map((item: any) => {
          return {
            ...item,
            isRedLine,
            value: item.id,
            label: item.labelName,
          };
        });
        dispatch({
          type: 'global/save',
          payload: {
            [isRedLine === '1' ? 'redLabelList' : 'labelList']: list,
          },
        });
      });
    },
    [currentTenantNo]
  );

  const handleRiskList = useCallback(
    (isRedLine?: string) => {
      if (!currentTenantNo) return;
      getRiskList(currentTenantNo, isRedLine).then((res: any) => {
        const list = (res.data.value || []).map((item: any) => {
          return {
            isRedLine,
            label: item,
            value: item,
          };
        });
        dispatch({
          type: 'global/save',
          payload: {
            [isRedLine === '1' ? 'redRiskList' : 'riskList']: list,
          },
        });
      });
    },
    [currentTenantNo]
  );

  useEffect(() => {
    if (!currentTenantNo) return;
    getGroupTree(currentTenantNo).then((res: any) => {
      if (res.data.success && res.data.value) {
        const { tree, map } = formatGroupTree(res.data.value.strategyGroupDTOList);
        dispatch({
          type: 'global/save',
          payload: {
            groupList: tree,
            groupMap: map,
            strategyGroupCountMap: res.data.value.strategyGroupCountMap,
          },
        });
      }
    });
    handleLabelList();
    // 获取红线的标签列表
    handleLabelList('1');
    handleRiskList();
    // 获取红线风险等级
    handleRiskList('1');
  }, [currentTenantNo, handleLabelList, handleRiskList]);

  const renderEmpty = useCallback(() => <Empty className={styles.empty} />, []);

  return (
    // 降级
    <StyleProvider hashPriority="high">
      <ConfigProvider {...antConfig} renderEmpty={renderEmpty}>
        {init && (
          <div className={styles.container}>
            {!hideNav && <LeftNav />}
            <div className={styles.right}>
              {!hideNav && <TopNav />}
              <div className={styles.content}>{children}</div>
            </div>
          </div>
        )}
      </ConfigProvider>
    </StyleProvider>
  );
};

export default Layout;
