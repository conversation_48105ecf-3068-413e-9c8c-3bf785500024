interface IFieldConfig {
  creator: string;
  fieldClassify: number;
  fieldType: number;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  isDeleted: string;
  modifier: string;
  showName: string;
  showStatus: number;
  sourceFieldCode: string;
  status: number;
  storageCode: string;
  targetFieldCode: string;
  tenantNo: string;
  index?: number;
  editing?: boolean;
  tokenizer: number;
}

type ICreateFieldConfig = Pick<
  IFieldConfig,
  'tokenizer' | 'showName' | 'sourceFieldCode' | 'targetFieldCode' | 'fieldType' | 'showStatus' | 'status' | 'tenantNo'
>;

interface FeedbackQualityResult {
  id: number;
  tenantNo: string;
  ruleTagging: string;
  failMessage?: string;
}

interface IGroupConfig {
  creator: string;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  isDeleted: string;
  modifier: string;
  tenantNo: string;
  groupName: string;
  label: string;
  value: number;
}

interface IStrategyConfig {
  creator: string;
  gmtCreated: string;
  gmtModified: string;
  id: number;
  isDeleted: string;
  modifier: string;
  status: number;
  strategyCode: string;
  strategyDefine: string;
  strategyName: string;
  strategyExpressShow: string;
}

interface IRuleField {
  fieldCode: string;
  fieldName: string;
  fieldOperator: string;
  fieldType: number;
  fieldValue: string;
  id?: number;
  rowKey: number;
}

interface IStrategyRule {
  robotNo: string;
  skillName: string;
  skillNo: string;
  strategyId?: number;
  id?: string;
  rowKey: number;
  rate?: number;
  level?: string;
  tag?: string;
}

interface IOption {
  label: string;
  value: string;
}

interface IBot {
  botCode: string;
  botName: string;
  status: string;
}

interface ISkill {
  skillName: string;
  skillNo: string;
  status: string;
  type: string;
}

type IBotOption = IBot & IOption;

type ISkillOption = ISkill & IOption;
