import { Collapse as ACollapse, CollapseProps } from 'antd';
import classNames from 'classnames';
import React, { useCallback, useMemo, useState } from 'react';
import styles from './index.scss';

interface IProps extends CollapseProps {
  key?: string | number;
  activeLabel: React.ReactNode;
  children: React.ReactNode;
  extra?: React.ReactNode;
  unActiveLabel: React.ReactNode;
  defaultOpen?: boolean;
}

const Collapse = (props: IProps) => {
  const {
    key = '0',
    activeLabel,
    children = true,
    extra,
    unActiveLabel,
    defaultOpen = false,
    collapsible,
    className,
  } = props;
  const [activeKey, setActiveKey] = useState<any>(defaultOpen ? key : '');

  const items = useMemo(
    () => [
      {
        showArrow: false,
        key,
        label: activeKey === key ? unActiveLabel : activeLabel,
        children,
        forceRender: true,
        extra,
      },
    ],
    [activeKey, key, unActiveLabel, activeLabel, children, extra]
  );

  const onChange = useCallback((e) => {
    if (e.length === 0) {
      setActiveKey('');
    } else {
      setActiveKey(e[0]);
    }
  }, []);

  return (
    <div className={classNames(styles.container, className)}>
      <ACollapse
        activeKey={!!activeKey ? activeKey : undefined}
        onChange={onChange}
        items={items}
        collapsible={collapsible}
      />
    </div>
  );
};

export default Collapse;
