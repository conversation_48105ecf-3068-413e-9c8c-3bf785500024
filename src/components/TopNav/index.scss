.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: var(--header-height);
  border-bottom: 0.5px solid #f1f1f1;
  flex-shrink: 0;
  background-color: #fff;
  box-sizing: border-box;

  :global {
    .ant-select {
      .ant-select-selection-item {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
      }
    }
  }

  .title {
    font-size: 16px;
    padding: 0 0 0 16px;
    margin: 0 0 0 5px;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: #4e5969;
    position: relative;

    &::before {
      width: 1px;
      height: 12px;
      background: #e5e6eb;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      content: '';
    }
  }

  .menu {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    & > div {
      color: #525866;
      font-size: 14px;
      font-weight: 600;
      height: 35px;
      display: flex;
      align-items: center;
      padding: 0 14px;
      margin: 0 14px;
      cursor: pointer;
      border-radius: 8px;
      svg {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
      &:hover {
        color: var(--primary-color);
      }
      &.active {
        box-shadow: 0 4px 12px 0 rgba(24, 27, 37, 0.08);
        color: var(--primary-color);
      }
    }
  }
}
