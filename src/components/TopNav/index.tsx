import { getFirstPage } from '@/components/LeftNav';
import { getPageData, interiorMenu } from '@/components/LeftNav/menu';
import RobotSvg from '@/pages/consultRecord/ChatHistory/img/robotTop.svg';
import { history, useDispatch, useLocation, useSelector } from '@magi/magi';
import { message, Select } from 'antd';
import React, { useCallback, useMemo } from 'react';
import styles from './index.scss';

const TopNav = () => {
  const location: any = useLocation();
  const {
    robotList = [],
    currentTenantNo: _currentTenantNo,
    isStrategy,
  } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = _currentTenantNo || location?.query?.tenantNo;
  const dispatch = useDispatch();
  const { botNo } = useMemo(
    () => robotList.find((item: any) => item.botNo === currentTenantNo) || {},
    [robotList, currentTenantNo]
  );

  const pageData = useMemo(() => getPageData(location.pathname.substring(1)), [location.pathname]);

  const menus = useMemo(() => {
    const pathname = location.pathname.substring(1);
    let list;
    for (const key in interiorMenu) {
      if ((interiorMenu as any)[key].find((v: any) => v.key === pathname)) {
        list = (interiorMenu as any)[key];
        break;
      }
    }
    return list || [];
  }, [location.pathname]);

  const onSelectBot = useCallback(
    (value: string, botItem: any) => {
      dispatch({
        type: 'global/save',
        payload: {
          currentTenantNo: value,
        },
      });
      history.replace(getFirstPage({ isRedLine: botItem.isRedLine, tenantNo: value, isStrategy }));
      message.info(`切换到 ${botItem.botName}`);
    },
    [dispatch, history, isStrategy]
  );

  const filterOption = useCallback((input: string, option?: any) => {
    if (option) return option.botName?.toLowerCase()?.indexOf(input.toLowerCase()) >= 0;
    return false;
  }, []);

  const onChangeMenu = useCallback(
    (item: any) => {
      history.push(currentTenantNo ? `/${item.key}?tenantNo=${currentTenantNo}` : `/${item.key}`);
    },
    [history, currentTenantNo]
  );

  return (
    <div className={styles.container}>
      {!!botNo && (
        <div style={{ display: 'flex', alignItems: 'center', ...(menus?.length ? { width: '30vw' } : {}) }}>
          <img src={RobotSvg} width={28} height={28} />
          <Select
            showSearch
            variant="borderless"
            value={botNo}
            placeholder="请选择工作空间"
            optionFilterProp="children"
            onChange={onSelectBot}
            filterOption={filterOption}
            options={robotList}
            fieldNames={{ label: 'botName', value: 'botNo' }}
            size="large"
            dropdownStyle={{ minWidth: 200 }}
          />
          {!!(!menus?.length && pageData) && <h1 className={styles.title}>{pageData.label}</h1>}
        </div>
      )}
      {!!menus?.length && (
        <>
          <div className={styles.menu}>
            {menus.map((item: any) => {
              return (
                <div
                  key={item.key}
                  onClick={() => onChangeMenu(item)}
                  className={item.key === location.pathname?.substring(1) ? styles.active : ''}
                >
                  {item.icon}
                  {item.label}
                </div>
              );
            })}
          </div>
          {!!botNo && <div style={{ width: '30vw' }} />}
        </>
      )}
    </div>
  );
};

export default TopNav;
