import { LeftOutlined } from '@ant-design/icons';
import { history } from '@magi/magi';
import React, { FC } from 'react';
import styles from './index.scss';

interface IProps {
  title?: React.ReactNode;
  goBack?: () => void;
  style?: React.CSSProperties;
}

const SubTitle: FC<IProps> = ({ title, goBack, style }) => {
  return (
    <div className={styles.container} style={style}>
      <LeftOutlined className={styles.icon} />
      <span
        onClick={() => {
          goBack ? goBack() : history.goBack();
        }}
        className={styles.back}
      >
        返回
      </span>
      <span className={styles.title}>{title}</span>
    </div>
  );
};

export default SubTitle;
