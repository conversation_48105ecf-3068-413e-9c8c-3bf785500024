.markdown {

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  ul,
  ol {
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 0;

    &:first-child {
      margin-top: 0;
    }
  }

  @for $i from 1 through 6 {
    h#{$i} {
      font-size: #{floor(16 - ($i - 1) * (6 / 5))}px;
    }
  }

  :global {
    table {
      border-collapse: collapse;
      box-sizing: border-box;
      font-size: 12px;

      thead {
        background: #eaedf3;

        th {
          border: 1px solid #e5e6eb;
          padding: 5px 10px;
        }
      }

      tbody {
        background: #fff;

        td {
          border: 1px solid #e5e6eb;
          padding: 2px 10px;
        }
      }
    }
  }
}

.highlight {
  background-color: #ffe500;
}