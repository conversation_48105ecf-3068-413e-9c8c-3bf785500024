import React, { FC, memo, ReactNode, useCallback, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import { Components } from 'react-markdown/lib/ast-to-react';
import remarkGfm from 'remark-gfm';
import styles from './index.scss';

interface IProps {
  markdown?: string;
  highlightWords?: string[];
}

const MarkdownRender: FC<IProps> = ({ markdown, highlightWords = [] }) => {
  const processedMarkdown = useMemo(() => markdown?.replace(/<at id="\d+">(.*?)<\/at>/g, '@$1'), [markdown]);

  const highlightText = useCallback(
    (text: string) => {
      if (!highlightWords.length) return text;

      const regex = new RegExp(`(${highlightWords.join('|')})`, 'gi');
      return text.split(regex).map((part, index) =>
        regex.test(part) ? (
          <span key={index} className={styles.highlight}>
            {part}
          </span>
        ) : (
          part
        )
      );
    },
    [highlightWords]
  );

  const processChildren = useCallback(
    (children: ReactNode) =>
      React.Children.map(children, (child) => (typeof child === 'string' ? highlightText(child) : child)),
    [highlightText]
  );

  const components: Components = useMemo(
    () => ({
      p: ({ children }) => <p>{processChildren(children)}</p>,
      li: ({ children }) => <li>{processChildren(children)}</li>,
      td: ({ children }) => <td>{processChildren(children)}</td>,
      th: ({ children }) => <th>{processChildren(children)}</th>,
      del: ({ children }) => <del>{processChildren(children)}</del>,
      h1: ({ children }) => <h1>{processChildren(children)}</h1>,
      h2: ({ children }) => <h2>{processChildren(children)}</h2>,
      h3: ({ children }) => <h3>{processChildren(children)}</h3>,
      h4: ({ children }) => <h4>{processChildren(children)}</h4>,
      h5: ({ children }) => <h5>{processChildren(children)}</h5>,
      h6: ({ children }) => <h6>{processChildren(children)}</h6>,
      a: ({ href, children }) => <a href={href}>{processChildren(children)}</a>,
      em: ({ children }) => <em>{processChildren(children)}</em>,
      strong: ({ children }) => <strong>{processChildren(children)}</strong>,
      code: ({ children }) => <code>{processChildren(children)}</code>,
    }),
    [processChildren]
  );

  const renderPlainText = useCallback(
    (text: string) => {
      const highlightedText = highlightText(text);
      return <div style={{ whiteSpace: 'pre-wrap' }}>{highlightedText}</div>;
    },
    [highlightText]
  );

  if (!processedMarkdown) return null;

  return processedMarkdown ? (
    <ReactMarkdown remarkPlugins={[remarkGfm]} className={styles.markdown} components={components}>
      {processedMarkdown}
    </ReactMarkdown>
  ) : (
    renderPlainText(processedMarkdown)
  );
};

export default memo(MarkdownRender);
