import { Space } from 'antd';
import { defaults } from 'lodash';
import React from 'react';
import { ToolbarActionConfig } from '../types';
import ColumnSetting from './ColumnSetting';
import DensityIcon from './DensityIcon';
import FullScreenIcon from './FullScreenIcon';
import ReloadIcon from './ReloadIcon';
// import defaults from 'lodash.defaults';

const defaultConfig: ToolbarActionConfig = {
  enabled: ['columnsSetting', 'density', 'fullScreen', 'refresh'],
};

const ToolBar: React.FC<{
  fullScreen: () => Promise<void>;
  refresh: () => void;
  toolbarAction: ToolbarActionConfig;
  size?: string;
  updateSize?: (size: string) => void;
  columns?: any;
}> = ({ refresh, fullScreen, toolbarAction = false, size, updateSize, columns }) => {
  const toolbarActionConfig =
    typeof toolbarAction === 'boolean' && toolbarAction ? defaultConfig : defaults(toolbarAction, defaultConfig);

  const { columnsSettingValue, onColumnsSettingChange, enabled = [] } = toolbarActionConfig;

  if (!toolbarAction) return null;

  return (
    <Space size={14} style={{ fontSize: 18 }}>
      {enabled.includes('refresh') && <ReloadIcon refresh={refresh} />}
      {enabled.includes('density') && <DensityIcon size={size} updateSize={updateSize} />}
      {enabled.includes('fullScreen') && <FullScreenIcon fullScreen={fullScreen} />}
      {enabled.includes('columnsSetting') && (
        <ColumnSetting
          columns={columns}
          columnsSettingValue={columnsSettingValue}
          onColumnsSettingChange={onColumnsSettingChange}
        />
      )}
    </Space>
  );
};

export default ToolBar;
