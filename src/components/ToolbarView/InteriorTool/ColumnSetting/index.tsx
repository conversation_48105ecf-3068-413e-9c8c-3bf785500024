import { CloseOutlined, SettingOutlined, UndoOutlined } from '@ant-design/icons';
import { DndContext, DragOverlay } from '@dnd-kit/core';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Button, Checkbox, Divider, Dropdown, Tooltip } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
// import { ToolbarActionConfig } from '@/types';
import clx from 'classnames';
import { ToolbarActionConfig } from '../../types';
import './index.less';
import Item from './item';
import { cancelFixed, fixItem, Setting } from './utils';

const prefix = 'tr-toolbar-column-setting';
const getColumnKey = (column: any, fallback: any) => {
  if (column.key) return String(column.key);
  if (column.dataIndex) return String(column.dataIndex);
  if (column.title && typeof column.title === 'string') return column.title;
  console.warn('[Table Render]: column must have a key or dataIndex or title');
  return String(fallback);
};

const ColumnSetting: React.FC<
  Pick<ToolbarActionConfig, 'columnsSettingValue' | 'onColumnsSettingChange' | 'columns'>
> = ({ columnsSettingValue, onColumnsSettingChange, columns }) => {
  const [columnsSetting, setColumnsSetting] = useState<any>([]);

  const [open, setOpen] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);

  const inited = useRef(false);

  const handleChange = (setting: Setting) => {
    setColumnsSetting(setting);
    onColumnsSettingChange?.(setting as any);
  };

  const init = () => {
    const initSetting = columns?.map((i: any, index: number) => ({
      key: getColumnKey(i, index),
      hidden: false,
    }));
    handleChange(initSetting);
  };

  useEffect(() => {
    if (columnsSettingValue) {
      setColumnsSetting(columnsSettingValue);
    }
  }, [columnsSettingValue]);

  useEffect(() => {
    if (open && columns?.length > 0 && !inited.current && columnsSetting?.length === 0) {
      init();
      inited.current = true;
      return;
    }
  }, [open, columns, columnsSetting]);

  const findIndex = (key: any) => {
    return columnsSetting.findIndex((i: any) => i.key === key);
  };

  /** 固定某一列 */
  const onFixItem = (key: string) => {
    const fixedSetting = fixItem(columnsSetting, key);
    const finalSetting = cancelFixed(fixedSetting);
    handleChange(finalSetting);
  };

  /** 取消固定某一列 */
  const onUnfixItem = (key: string) => {
    const canceledSetting = columnsSetting.map((i: any) => ({
      ...i,
      fixed: i.key === key ? undefined : i.fixed,
    }));
    const finalSetting = cancelFixed(canceledSetting);
    handleChange(finalSetting);
  };

  const getItems = (setting: Setting) => {
    if (!setting) return [];
    return setting.map((i) => ({
      className: clx(`${prefix}-item`, {
        [`${prefix}-item-fixed`]: i.fixed,
      }),
      key: i.key,
      label: (
        <Item
          {...i}
          columns={columns}
          columnsSetting={columnsSetting}
          onFixItem={onFixItem}
          onUnfixItem={onUnfixItem}
          columnKey={String(i.key)}
        />
      ),
    }));
  };

  const onReset = () => {
    init();
  };

  /** 列的显示和隐藏 */
  const onColumnsCheckChange = (val: string[]) => {
    const finalSetting = columnsSetting.map((i: any) => ({
      ...i,
      hidden: !val.includes(String(i.key)),
    }));
    handleChange(finalSetting);
  };

  /** 移动某一列 */
  const onDragEnd = (activeId: any, overId: any) => {
    const newSetting: any = arrayMove(columnsSetting, findIndex(activeId), findIndex(overId));
    const activeItem: any = newSetting.find((i: any) => i.key === activeId);

    if (activeItem.fixed) {
      const fixedSetting = fixItem(newSetting, activeId);
      const finalSetting = cancelFixed(fixedSetting);
      handleChange(finalSetting);
    } else {
      const finalSetting = cancelFixed(newSetting);
      handleChange(finalSetting);
    }
  };

  const items = useMemo(() => getItems(columnsSetting), [columnsSetting, columns]);
  const activeItem = useMemo(() => columnsSetting.find((i: any) => i.key === activeId), [columnsSetting, activeId]);
  const keyList = useMemo(() => columnsSetting.map((i: any) => i.key), [columnsSetting]);
  const value = useMemo(() => columnsSetting.filter((i: any) => !i.hidden).map((i: any) => i.key), [columnsSetting]);

  return (
    <Dropdown
      menu={{ items }}
      open={open}
      trigger={['click']}
      onOpenChange={(open, info) => info.source === 'trigger' && setOpen(open)}
      getPopupContainer={(node) => (node.parentNode as HTMLElement) || document.body}
      dropdownRender={(menu) => {
        return (
          <div className={prefix}>
            <div className={`${prefix}-header`}>
              <CloseOutlined
                onClick={() => setOpen(false)}
                style={{
                  fontSize: 16,
                  color: '#999',
                  cursor: 'pointer',
                }}
              />
              <Button icon={<UndoOutlined />} type="primary" size="small" onClick={onReset}>
                {'重置'}
              </Button>
            </div>
            <Divider style={{ margin: 0 }} />
            <DndContext
              onDragEnd={({ over, active }) => {
                if (over) {
                  onDragEnd(active.id, over.id);
                }
              }}
              onDragStart={({ active }) => {
                setActiveId(String(active.id));
              }}
            >
              <SortableContext items={keyList} strategy={verticalListSortingStrategy}>
                <Checkbox.Group value={value} onChange={onColumnsCheckChange}>
                  {menu}
                </Checkbox.Group>
                <DragOverlay>
                  {activeId && (
                    <Item
                      columns={columns}
                      columnsSetting={columnsSetting}
                      {...activeItem}
                      isOverlay
                      isChecked={value.includes(activeId)}
                      columnKey={activeId}
                    />
                  )}
                </DragOverlay>
              </SortableContext>
            </DndContext>
          </div>
        );
      }}
    >
      <Tooltip title={'列设置'}>
        <SettingOutlined style={{ cursor: 'pointer' }} onClick={() => setOpen(true)} />
      </Tooltip>
    </Dropdown>
  );
};

export default ColumnSetting;
