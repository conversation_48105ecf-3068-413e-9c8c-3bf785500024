import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { message, Tooltip } from 'antd';
import React, { useState } from 'react';

const FullScreenIcon: React.FC<{
  fullScreen: () => Promise<void>;
}> = (props) => {
  const [isFullScreen, setFullScreen] = useState(false);
  const { fullScreen } = props;

  return isFullScreen ? (
    <Tooltip title={'退出全屏'}>
      <FullscreenExitOutlined
        onClick={() => {
          document.exitFullscreen();
          setFullScreen(false);
        }}
      />
    </Tooltip>
  ) : (
    <Tooltip title={'全屏'}>
      <FullscreenOutlined
        onClick={() => {
          if (!document.fullscreenEnabled) {
            message.warning('无法全屏');
            return;
          }
          if (!document.fullscreenElement) {
            setFullScreen(true);
            fullScreen().catch((err: any) => message.error(err.message));
          }
        }}
      />
    </Tooltip>
  );
};

export default FullScreenIcon;
