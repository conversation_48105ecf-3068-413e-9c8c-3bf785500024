import { ColumnHeightOutlined } from '@ant-design/icons';
import { Dropdown, Tooltip } from 'antd';
import React, { useRef } from 'react';

export type DensitySize = 'middle' | 'small' | 'default' | undefined;

const DesityIcon = (props: { size?: string; updateSize?: (size: string) => void }) => {
  const dropRef = useRef<any>(); // class组件用 React.createRef()
  const { size, updateSize } = props;
  const tableSize = size;

  const items = [
    {
      key: 'large',
      label: '默认',
    },
    {
      key: 'middle',
      label: '中等',
    },
    {
      key: 'small',
      label: '紧凑',
    },
  ];

  return (
    <div ref={dropRef}>
      <Dropdown
        getPopupContainer={() => dropRef.current}
        menu={{
          selectedKeys: [tableSize || 'large'],
          onClick: ({ key }) => {
            updateSize?.(key);
          },
          style: { width: 80 },
          items,
        }}
        trigger={['click']}
      >
        <Tooltip title={'表格密度'}>
          <ColumnHeightOutlined />
        </Tooltip>
      </Dropdown>
    </div>
  );
};

export default DesityIcon;
