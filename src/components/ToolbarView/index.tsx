import { Space } from 'antd';
import classNames from 'classnames';
import { isArray, isFunction } from 'lodash';
import React from 'react';
import './index.less';
import InteriorTool from './InteriorTool';
import TitleView from './TitleView';

const ToolbarView = (props: any) => {
  const {
    setState,
    title,
    toolbarRender,
    size,
    updateSize,
    toolbarAction = false,
    request,
    refresh,
    fullScreen,
    currentTab,
    columns,
  } = props;

  const content = isFunction(toolbarRender) ? toolbarRender() : toolbarRender || [];
  const isTopHead = title || (!!content && content?.length !== 0) || (isArray(request) && request.length > 1);

  return (
    <div className={classNames('tr-toolbar', 'tr-toolbar-v2', { 'tr-toolbar-nohead': !isTopHead && !toolbarAction })}>
      <div className="tr-toolbar-left">
        <TitleView title={title} setState={setState} request={request} currentTab={currentTab} />
      </div>
      <div className="tr-toolbar-right">
        <Space>
          <Space>{content}</Space>
          <InteriorTool
            columns={columns}
            size={size}
            updateSize={updateSize}
            toolbarAction={toolbarAction}
            fullScreen={fullScreen}
            refresh={refresh}
          />
        </Space>
      </div>
    </div>
  );
};

export default ToolbarView;
