export type ColumnsSettingValueType = Array<{
  /** 列的 key */
  key: string;
  /** 当前列是否隐藏 */
  hidden: boolean;
  /** 当前列是否固定 */
  fixed?: 'right' | 'left';
}>;

export type ToolbarActionConfig = {
  /** 开启的功能，默认 all，全部开启 */
  enabled?: Array<'refresh' | 'columnsSetting' | 'fullScreen' | 'density'>;
  /** 列设置的状态 */
  columnsSettingValue?: ColumnsSettingValueType;
  /** 列设置状态改变时的回调 */
  onColumnsSettingChange?: (val: ColumnsSettingValueType) => void;
  columns?: any;
};
