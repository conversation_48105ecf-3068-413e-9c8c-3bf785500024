import { CloudUploadOutlined } from '@ant-design/icons';
import { Form, Upload as AUpload } from 'antd';
import { last } from 'lodash';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import styles from './index.scss';

interface IProps {
  name?: string;
  multiple?: boolean;
  hint?: string;
  onFileChange?: (fileList: any[]) => void;
  formProps?: any;
}
const { Dragger } = AUpload;
const Upload = React.forwardRef((props: IProps, ref: any) => {
  const { name = 'file', multiple = false, hint, onFileChange, formProps } = props;
  const [fileList, setFileList] = useState<any[]>([]);
  const [fileUrlList, setFileUrlList] = useState<any[]>([]);
  const urlListRef = useRef<any[]>([]);

  const uploadProps = useMemo(
    () => ({
      name,
      multiple,
      action: '',
      beforeUpload: (file: any) => {
        return false;
      },
      onChange(info: any) {
        if (multiple) {
          setFileList(info.fileList);
        } else {
          setFileList([last(info.fileList)]);
        }
      },
      onDrop(e: any) {},
    }),
    [name, multiple]
  );

  useEffect(() => {
    const arr: any = [];
    fileList.forEach((file) => {
      arr.push({
        name: file.name,
        url: URL.createObjectURL(file.originFileObj),
      });
    });
    setFileUrlList(arr);
  }, [fileList]);

  useEffect(() => {
    onFileChange && onFileChange(fileList);
  }, [fileList, onFileChange]);

  useEffect(() => {
    urlListRef.current.map((item) => {
      URL.revokeObjectURL(item.url);
    });
    urlListRef.current = fileUrlList;
  }, [fileUrlList]);

  useEffect(() => {
    return () => {
      urlListRef.current.map((item) => {
        URL.revokeObjectURL(item.url);
      });
    };
  }, []);

  useImperativeHandle(ref, () => ({
    fileList,
    setFileList,
  }));

  return (
    <div className={styles.container}>
      {formProps ? (
        <Form.Item style={{ marginBottom: 0 }} {...formProps}>
          <Dragger {...uploadProps} fileList={fileList}>
            <p className="ant-upload-text">
              <CloudUploadOutlined style={{ marginRight: 8 }} />
              将文档拖拽到此处，或 <a>本地上传</a>
            </p>
            <p className="ant-upload-hint">{hint}</p>
          </Dragger>
        </Form.Item>
      ) : (
        <Dragger {...uploadProps} fileList={fileList}>
          <p className="ant-upload-text">
            <CloudUploadOutlined style={{ marginRight: 8 }} />
            将文档拖拽到此处，或 <a>本地上传</a>
          </p>
          <p className="ant-upload-hint">{hint}</p>
        </Dragger>
      )}
      <div className={styles.fileList}>
        {fileUrlList.map((item) => (
          <div className={styles.fileItem} key={item.url}>
            <a href={item.url}>{item.name}</a>
          </div>
        ))}
      </div>
    </div>
  );
});

export default Upload;
