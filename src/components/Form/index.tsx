import RangeTimePicker from '@/components/RangeTime';
import { RANGE_PRESETS } from '@/utils/constants';
import { DownOutlined } from '@ant-design/icons';
import { Button, Col, DatePicker, Form as AntdForm, FormProps, Input, Row, Select, TreeSelect, Typography } from 'antd';
import classnames from 'classnames';
import React, { FC, useCallback, useMemo, useState } from 'react';
import styles from './index.scss';

const { RangePicker } = DatePicker;

const { Item, useForm, useWatch } = AntdForm;

export { Item, useForm, useWatch };

const MAX_COLUMN_LENGTH = 8;

interface IProps extends FormProps {
  columns: {
    label: React.ReactNode;
    name: string | string[];
    type?: string;
    [key: string]: any;
  }[];
  loading?: boolean;
  btnRender?: React.ReactNode;
  onQuery?: (...args: any[]) => void;
  onReset?: (...args: any[]) => void;
}

const Form: FC<IProps> = ({
  columns,
  className,
  form: formInstance,
  onQuery,
  onReset,
  colon = false,
  initialValues,
  btnRender,
  loading,
  ...extraProps
}) => {
  const [form] = useForm(formInstance);
  const [expand, setExpand] = useState(false);

  const footerColNum = useMemo(
    () => (columns?.length > MAX_COLUMN_LENGTH && !expand ? 8 : (3 - (columns.length % 3)) * 8),
    [columns, expand]
  );

  const onSubmit = useCallback(async () => {
    const values = await form.validateFields();
    onQuery?.(values);
  }, [form, onQuery]);

  const onClear = useCallback(() => {
    form.resetFields();
    onReset?.(form.getFieldsValue());
  }, [form, onReset, initialValues]);

  return (
    <AntdForm
      {...extraProps}
      initialValues={initialValues}
      colon={colon}
      form={form}
      className={classnames(styles.form, className)}
    >
      <Row gutter={24}>
        {columns?.map(({ label, name, type = 'input', ...extra }, index: number) => {
          return (
            <Col
              span={8}
              key={JSON.stringify(name)}
              style={
                !expand && index > MAX_COLUMN_LENGTH - 1
                  ? {
                      visibility: 'hidden',
                      position: 'absolute',
                      left: '-9999px',
                      zIndex: -1000,
                    }
                  : {}
              }
            >
              <Item
                label={
                  typeof label === 'string' ? (
                    <Typography.Paragraph style={{ margin: 0 }} ellipsis={{ rows: 1, tooltip: label }}>
                      {label}
                    </Typography.Paragraph>
                  ) : (
                    label
                  )
                }
                name={name}
                className={classnames({ [styles.item]: typeof label !== 'string' })}
                {...(label !== 'string' ? { htmlFor: '' } : {})}
              >
                {type === 'input' && <Input placeholder={`请输入${label || ''}`} {...extra} />}
                {type === 'select' && <Select placeholder={`请选择${label || ''}`} {...extra} />}
                {type === 'treeSelect' && <TreeSelect placeholder={`请选择${label || ''}`} {...extra} />}
                {type === 'rangeTimePicker' && <RangeTimePicker style={{ width: '100%' }} {...extra} />}
                {type === 'rangePicker' && <RangePicker style={{ width: '100%' }} presets={RANGE_PRESETS} {...extra} />}
              </Item>
            </Col>
          );
        })}
        {!!columns && (
          <Col span={footerColNum} className={styles.footer}>
            <Item>
              {btnRender}
              <Button type="primary" onClick={onSubmit} loading={loading}>
                查询
              </Button>
              <Button style={{ marginLeft: 16 }} onClick={onClear} loading={loading}>
                重置
              </Button>
              {columns?.length > MAX_COLUMN_LENGTH && (
                <Button className={styles.expand} type="link" onClick={() => setExpand((preState) => !preState)}>
                  {expand ? '收起' : '展开'}
                  <DownOutlined rotate={expand ? 180 : 0} />
                </Button>
              )}
            </Item>
          </Col>
        )}
      </Row>
    </AntdForm>
  );
};

export default Form;
