.form {
  position: relative;
  .footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
  :global {
    .ant-form-item-no-colon {
      width: 98px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      white-space: wrap;
    }
  }
  .item {
    :global {
      .ant-form-item-no-colon {
        width: auto;
        &::after {
          display: none;
        }
        .ant-form-item {
          margin-bottom: 0;
          width: 100%;
          .ant-select {
            .ant-select-selector {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
              border-right: none;
              background: #f5f7fa;
            }
          }
        }
      }
      .ant-form-item-control {
        .ant-picker {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
      }
    }
  }
  .expand {
    margin-right: -15px;
  }
}
