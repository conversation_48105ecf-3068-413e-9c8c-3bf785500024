import emprtImg from '@/assets/empty.png';
import { Empty as AntdEmpty, EmptyProps } from 'antd';
import classNames from 'classnames';
import React, { FC } from 'react';
import styles from './index.scss';

interface IProps extends EmptyProps {}

const Empty: FC<IProps> = ({ className, ...props }) => {
  return <AntdEmpty image={emprtImg} {...props} className={classNames(styles.empty, className)} />;
};

export default Empty;
