import Table from '@/components/Table';
import { TableProps } from 'antd';
import React, { useCallback, useEffect, useImperativeHandle, useState } from 'react';

export type Pagination = {
  current: number;
  pageSize: number;
};
interface IProps {
  columns: TableProps['columns'];
  disabledPagination?: boolean;
  request: (pagination: Pagination) => Promise<{ total: number; data: any[] }> | { total: number; data: any[] };
  method?: 'post' | 'get';
  loading?: boolean;
  scroll?: TableProps['scroll'];
  style?: React.CSSProperties;
  rowKey?: any;
}

const EasyTable = React.forwardRef((props: IProps, ref: any) => {
  const { columns, disabledPagination = false, request, loading = false, scroll, ...extraProps } = props;
  const [data, setData] = useState<any>([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<Pagination>({
    current: 1,
    pageSize: 10,
  });

  const fetchData = useCallback(async () => {
    const { total, data } = await request(pagination);
    setTotal(total);
    setData(data);
  }, [request, pagination]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useImperativeHandle(ref, () => ({
    fetchData,
  }));

  return (
    <Table
      loading={loading}
      columns={columns}
      dataSource={data}
      onChange={(newPagination) => {
        if (pagination.pageSize === newPagination.pageSize && pagination.current === newPagination.current) {
          return;
        }
        const obj = {
          current: pagination.pageSize !== newPagination.pageSize ? 1 : newPagination.current || 1,
          pageSize: newPagination.pageSize || 10,
        };
        setPagination(obj);
      }}
      scroll={scroll}
      pagination={
        disabledPagination
          ? false
          : {
              total,
            }
      }
      {...extraProps}
    />
  );
});

export default EasyTable;
