import { Table as AntdTable, TableProps } from 'antd';
import React, { FC } from 'react';

interface IProps extends TableProps {}

const Table: FC<IProps> = ({ pagination, ...extraProps }) => {
  return (
    <AntdTable
      pagination={
        typeof pagination === 'object' && pagination !== null
          ? {
              size: 'small',
              showSizeChanger: true,
              showTotal: (total) => `共${total}条`,
              ...(pagination || {}),
            }
          : pagination
      }
      rowKey={'id'}
      scroll={{ x: 'max-content' }}
      {...extraProps}
    />
  );
};

export default Table;
