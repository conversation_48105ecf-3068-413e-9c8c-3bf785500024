import classnames from 'classnames';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/shell/shell';
import 'codemirror/theme/idea.css';
import React, { FC, memo, useEffect, useRef } from 'react';
import { Controlled } from 'react-codemirror2';
import styles from './index.scss';

const CodeMirror: FC<any> = ({ options, className, value, isScroll = true, ...extraProps }) => {
  const editorRef = useRef<any>(null);

  useEffect(() => {
    if (!isScroll) return;
    let timer: any;
    if (editorRef.current) {
      const instance = editorRef.current;
      instance?.setSize(null, 'auto');
      timer = setTimeout(() => {
        // 获取实际内容高度
        const height = instance?.getScrollInfo()?.height;
        // 设置编辑器高度为内容高度
        instance?.setSize(null, height);
      }, 0);
    }

    return () => {
      clearTimeout(timer);
    };
  }, [value, isScroll]);

  return (
    <Controlled
      className={classnames(styles.codeMirror, className)}
      options={{
        mode: 'text/x-sh',
        theme: 'default',
        lineNumbers: true,
        readOnly: true,
        lineWrapping: true,
        ...(options || {}),
      }}
      onBeforeChange={(editor, data, value) => {}}
      editorDidMount={(editor) => {
        editorRef.current = editor;
        setTimeout(() => {
          editor.refresh();
        }, 0);
      }}
      value={value}
      {...extraProps}
    />
  );
};

export default memo(CodeMirror);
