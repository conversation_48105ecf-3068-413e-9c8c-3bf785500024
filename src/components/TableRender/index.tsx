import RangeTimePicker from '@/components/RangeTime';
import ColumnSetting from '@/components/ToolbarView/InteriorTool/ColumnSetting';
import { Button } from 'antd';
import classnames from 'classnames';
import React, { forwardRef, memo, useMemo } from 'react';
import TRender, { TableRenderProps } from 'table-render';
import styles from './index.scss';

interface IProps extends TableRenderProps {
  onSubmit?: () => void;
  onClear?: () => void;
  setPageData?: React.Dispatch<
    React.SetStateAction<{
      current: number;
      pageSize: number;
    }>
  >;
  pageData?: { current: number; pageSize: number; [key: string]: any };
}

const TableRender = forwardRef<any, IProps>(
  (
    {
      search,
      scroll,
      toolbarAction,
      className,
      pageData,
      setPageData,
      onChange,
      onSubmit,
      onClear,
      columns,
      toolbarRender,
      ...extraProps
    },
    ref
  ) => {
    const toolbarActionProps = useMemo(() => {
      return toolbarAction === false
        ? toolbarAction
        : {
            ...((toolbarAction || {}) as any),
            enabled: [
              // 'density',
              // 'columnsSetting',
            ],
          };
    }, [toolbarAction]);

    const columnsData = useMemo(() => {
      const { columnsSettingValue } = (toolbarAction || {}) as any;
      if (columnsSettingValue?.length) {
        return columns
          ?.map((item) => {
            const data =
              columnsSettingValue.find((v: any) => v.dataIndex === item.dataIndex || v.key === item.key) || {};
            const index = columnsSettingValue.findIndex(
              (v: any) => v.dataIndex === item.dataIndex || v.key === item.key
            );
            return {
              ...item,
              ...data,
              sort: index + 1,
            };
          })
          .sort((a: any, b: any) => a.sort - b.sort);
      }
      return columns;
    }, [toolbarAction, columns]);

    return (
      <TRender
        ref={ref}
        rowKey={'id'}
        search={
          search?.schema
            ? {
                colon: false,
                className: classnames(styles.formRender, search?.className),
                widgets: { RangeTimePicker },
                searchBtnRender(submit, clearSearch, { loading }) {
                  return [
                    <Button
                      key="submit"
                      type="primary"
                      loading={loading}
                      onClick={() => {
                        submit();
                        onSubmit?.();
                      }}
                    >
                      查询
                    </Button>,
                    <Button
                      key="clear"
                      loading={loading}
                      style={{ marginLeft: 8 }}
                      onClick={() => {
                        onClear?.();
                        setTimeout(() => {
                          clearSearch();
                        });
                      }}
                    >
                      重置
                    </Button>,
                  ];
                },
                ...(search || {}),
              }
            : undefined
        }
        scroll={{ x: 'max-content', ...(scroll || {}) }}
        className={classnames(styles.tableRender, className)}
        pagination={{
          defaultCurrent: pageData?.current,
          showSizeChanger: true,
          showTotal: (total) => `共${total}条`,
          current: pageData?.current,
          pageSize: pageData?.pageSize,
          ...(pageData || {}),
        }}
        onChange={(newPagination, ...args) => {
          const newCurrent = newPagination.pageSize !== pageData?.pageSize ? 1 : newPagination.current;
          setPageData?.({
            current: newCurrent as number,
            pageSize: newPagination.pageSize as number,
          });
          onChange?.(newPagination, ...args);
        }}
        toolbarRender={
          <>
            {typeof toolbarRender === 'function' ? toolbarRender() : toolbarRender}
            {(toolbarAction !== false || (toolbarAction as any)?.enabled?.includes('columnsSetting')) && (
              <ColumnSetting
                columns={columns}
                columnsSettingValue={(toolbarAction as any)?.columnsSettingValue}
                onColumnsSettingChange={(toolbarAction as any)?.onColumnsSettingChange}
              />
            )}
          </>
        }
        toolbarAction={toolbarActionProps}
        columns={columnsData}
        {...extraProps}
      />
    );
  }
);

export default memo(TableRender);
