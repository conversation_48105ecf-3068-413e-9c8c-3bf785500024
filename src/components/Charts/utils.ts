export const defaultConfig: any = {
  xAxis: {
    line: {
      style: {
        stroke: '#BABEC4',
      },
    },
    tickLine: {
      style: {
        lineWidth: 1,
        stroke: '#BABEC4',
      },
    },
    label: {
      autoRotate: true,
      autoHide: false,
      style: {
        fill: '#4E5969',
        fontSize: 12,
      },
    },
  },
  yAxis: {
    label: {
      style: {
        fill: '#4E5969',
        fontSize: 12,
      },
    },
    grid: {
      line: {
        style: {
          stroke: '#E5E6EB',
          lineWidth: 1,
          lineDash: [4, 2],
        },
      },
    },
  },
  lineStyle: {
    lineWidth: 1,
  },
  tooltip: {
    domStyles: {
      'g2-tooltip': {
        fontSize: '12px',
        boxShadow: '0px 4px 20px 0px #0000001A',
      },
      'g2-tooltip-title': {
        fontWeight: 'bold',
        color: '#4E5969',
      },
      'g2-tooltip-name': {
        color: '#4E5969',
      },
      'g2-tooltip-value': {
        fontWeight: 'bold',
        color: '#1D2129',
      },
    },
  },
  legend: {
    layout: 'horizontal',
    position: 'bottom',
    offsetY: 10,
    itemSpacing: 20,
    itemMarginBottom: 20,
    itemName: {
      style: {
        fill: '#4E5969',
        fontSize: 12,
      },
    },
    marker: (_name: string, _index: number, item: any) => {
      const { stroke } = item?.style || {};
      return {
        symbol: 'circle',
        style: {
          fill: stroke,
          r: 3,
        },
      };
    },
  },
  data: [],
};
