import { Mix as AntdMix, MixConfig } from '@ant-design/plots';
import dayjs from 'dayjs';
import React, { FC, useCallback, useMemo, useState } from 'react';
import styles from './index.scss';
import { color, isDateFormat } from './Line';
import { defaultConfig } from './utils';

interface IProps {
  tooltip?: any;
  columnConfig?: any;
  lineConfig?: any;
  [key: string]: any;
}

const Mix: FC<IProps> = (props) => {
  const { descName, slider = false, legend = false } = props;
  const [visibleSeries, setVisibleSeries] = useState<{ [key: string]: boolean | undefined }>({});

  const processedData = useMemo(() => {
    const _props = (props as any)?.lineConfig || {};
    if (!isDateFormat(_props.data?.[0]?.[(_props as any)?.xField])) {
      return {
        data: _props.data,
      };
    }
    const data: any[] = _props.data
      ?.map((item: any) => ({
        ...item,
        oldDate: item[(_props as any).xField],
        [(_props as any).xField]: dayjs(item[(_props as any).xField])
          .valueOf()
          ?.toString(),
      }))
      .sort((a: any, b: any) => a[(_props as any).xField] - b[(_props as any).xField]);
    const getName = (name?: string) => data?.find((item) => item?.[(_props as any).xField] === name)?.oldDate || name;
    return {
      data,
      slider: {
        formatter: (val: string) => getName(val),
      },
      xAxis: {
        label: {
          formatter: (text: string) => getName(text),
        },
      },
      tooltip: {
        title: (title: string) => getName(title),
      },
    };
  }, [JSON.stringify(props)]);

  const config = useMemo<MixConfig>(() => {
    const { tooltip, columnConfig, lineConfig, slider = false, legend = false, ...otherProps } = props;
    const { tooltip: defaultTooltip, ...extraDefaultConfig } = defaultConfig;
    const { legend: columnLegend, xAxis: columnXAxis, meta: columnMeta, ...extraColumnProps } = columnConfig || {};
    const { legend: lineLegend, xAxis: lineXAxis, meta: lineMeta, ...extraLineProps } = lineConfig || {};
    return {
      syncViewPadding: true,
      tooltip: {
        ...defaultTooltip,
        ...(tooltip || {}),
        ...(processedData?.tooltip || {}),
        domStyles: {
          ...defaultTooltip.domStyles,
          ...((tooltip as any)?.domStyles || {}),
        },
        shared: true,
      },
      padding: slider ? [5, 20, 60, 20] : 'auto',
      // legend: slider === false && legend !== false ? { ...defaultConfig.legend, ...(legend || {}) } : false,
      legend: false,
      plots: [
        {
          type: 'column',
          options: {
            ...extraDefaultConfig,
            xAxis: false,
            maxColumnWidth: 50,
            color: extraLineProps.seriesField ? color : color[0],
            meta: {
              [extraColumnProps.xField]: {
                sync: true,
              },
              ...columnMeta,
            },
            ...extraColumnProps,
            yAxis: {
              ...extraDefaultConfig.yAxis,
            },
          },
        },
        {
          type: 'line',
          options: {
            slider: slider
              ? {
                  start: 0,
                  end: 100,
                  minLimit: 0,
                  maxLimit: 100,
                  height: 12,
                  offsetY: 60,
                  backgroundStyle: {
                    fill: '#f0f0f0',
                    fillOpacity: 0.3,
                    padding: [10, 0, 0, 0],
                  },
                  foregroundStyle: {
                    padding: [10, 0, 0, 0],
                  },
                  trendCfg: {
                    smooth: true,
                    isArea: false,
                    backgroundStyle: { fill: '#E5E6EB' },
                  },
                  handlerStyle: {
                    width: 12,
                    height: 20,
                    fill: '#E5E6EB',
                    stroke: '#BABEC4',
                    radius: 2,
                  },
                  ...(slider || {}),
                  ...(processedData?.slider || {}),
                }
              : false,
            color: extraLineProps.seriesField ? color : color[0],
            point: {
              size: 4,
            },
            ...extraDefaultConfig,
            ...extraLineProps,
            data: processedData?.data,
            meta: {
              [extraLineProps.xField]: {
                sync: extraLineProps.xField,
              },
              ...lineMeta,
            },
            xAxis: {
              ...defaultConfig.xAxis,
              label: {
                ...defaultConfig.xAxis?.label,
                ...processedData?.xAxis?.label,
                ...((lineXAxis as any)?.label || (columnXAxis as any)?.label || {}),
              },
            },
            yAxis: {
              ...extraDefaultConfig.yAxis,
              line: null,
              position: 'right',
            },
          },
        },
      ],
      ...otherProps,
    };
  }, [props, processedData]);

  const legendItems = useMemo(() => {
    const { data, color, seriesField, legend } = config?.plots?.find((v) => v.type === 'line')?.options || ({} as any);
    if (!seriesField || legend === false) return [];
    const seriesFieldSet: Set<string> = new Set();
    const visibleObj: any = {};
    data?.forEach((item: any) => {
      if (item?.[seriesField as string]) {
        seriesFieldSet.add(item?.[seriesField as string]);
        visibleObj[item?.[seriesField as string]] = true;
      }
    });
    const dataFields = [...seriesFieldSet].map((name, index) => ({
      name,
      desc: descName && data?.find((v: any) => v?.[seriesField as string] === name)?.[descName],
      color: (color as string[])?.[index],
    }));
    setVisibleSeries(visibleObj);
    return dataFields;
  }, [config, descName]);

  const filteredData = useMemo(() => {
    const { seriesField, legend, data, color } = config?.plots?.find((v) => v.type === 'line')?.options || {};
    if (!seriesField || legend === false) return {};
    const showColorItems = legendItems?.filter((item) => visibleSeries[item.name]);
    return {
      data: [...(data || [])].filter((item) => visibleSeries[item[seriesField as string]]),
      color: (color as string[])?.filter((val) => showColorItems?.find((item) => item.color === val)),
    };
  }, [config, visibleSeries, legendItems]);

  const newConfig = useMemo<any>(() => {
    return {
      ...config,
      plots: config?.plots?.map((item) => ({
        type: item.type,
        options: {
          ...(config?.plots?.find((v) => v.type === item.type)?.options || {}),
          ...filteredData,
        },
      })),
    };
  }, [config, filteredData]);

  const handleLegendChange = useCallback((type: string) => {
    setVisibleSeries((prev) => ({ ...prev, [type]: !prev[type] }));
  }, []);

  return (
    <div style={{ position: 'relative' }}>
      <AntdMix {...newConfig} />
      {legend !== false && (
        <div
          className={styles.legend}
          style={
            slider
              ? { width: 'calc(100% - 200px)', left: 0, bottom: '25px', position: 'absolute' }
              : { marginTop: '20px' }
          }
        >
          {legendItems?.map((item) => (
            <div className={styles.legendItems} key={item.name} onClick={() => handleLegendChange(item.name)}>
              <span
                className={styles.point}
                style={{
                  background: visibleSeries[item.name] ? item.color : '#D8D8D8',
                }}
              ></span>
              <span
                style={{
                  color: visibleSeries[item.name] ? '#4E5969' : '#D8D8D8',
                }}
              >
                {item.desc || item.name}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Mix;
