import { Line as AntdLine, LineConfig } from '@ant-design/plots';
import dayjs from 'dayjs';
import React, { FC, useCallback, useMemo, useState } from 'react';
import styles from './index.scss';
import { defaultConfig } from './utils';

export const color = [
  '#17B26A',
  '#2E90FA',
  '#7A5AF8',
  '#9AB10A',
  '#EF6820',
  '#E278F3',
  '#EDA073',
  '#A98BB5',
  '#E6D420',
  '#00ADA8',
  '#98546A',
  '#E37668',
  '#506AFF',
  '#26CFED',
  '#F15696',
];

export const isDateFormat = (str: string) => {
  // 这个正则表达式匹配 YYYY-MM-DD 格式
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  return dateRegex.test(str);
};

interface IProps extends LineConfig {
  descName?: string;
}

const Line: FC<IProps> = (props) => {
  const [visibleSeries, setVisibleSeries] = useState<{ [key: string]: boolean | undefined }>({});

  const processedData = useMemo(() => {
    if (!isDateFormat(props.data?.[0]?.[(props as any).xField])) {
      return {
        data: props.data,
      };
    }
    const data: any[] = props.data
      ?.map((item) => ({
        ...item,
        oldDate: item[(props as any).xField],
        [(props as any).xField]: dayjs(item[(props as any).xField])
          .valueOf()
          ?.toString(),
      }))
      .sort((a: any, b: any) => a[(props as any).xField] - b[(props as any).xField]);
    const getName = (name?: string) => data?.find((item) => item?.[(props as any).xField] === name)?.oldDate || name;
    return {
      data,
      slider: {
        formatter: (val: string) => getName(val),
      },
      xAxis: {
        label: {
          formatter: (text: string) => getName(text),
        },
      },
      tooltip: {
        title: (title: string) => getName(title),
      },
    };
  }, [JSON.stringify(props)]);

  const config = useMemo<LineConfig>(() => {
    const { slider, xAxis, data, tooltip } = processedData;
    return {
      slider: {
        start: 0,
        end: 100,
        minLimit: 0,
        maxLimit: 100,
        height: 12,
        trendCfg: {
          smooth: true,
          isArea: false,
          backgroundStyle: { fill: '#E5E6EB' },
        },
        handlerStyle: {
          width: 12,
          height: 20,
          fill: '#E5E6EB',
          stroke: '#BABEC4',
          radius: 2,
        },
        ...(slider || {}),
      },
      color,
      point: {
        size: 4,
      },
      ...defaultConfig,
      ...props,
      data,
      legend:
        props.slider === false && props.legend !== false ? { ...defaultConfig.legend, ...(props.legend || {}) } : false,
      xAxis: {
        ...props.xAxis,
        label: {
          ...defaultConfig.xAxis.label,
          ...xAxis?.label,
          ...((props.xAxis as any)?.label || {}),
        },
      },
      tooltip: {
        ...defaultConfig.xAxis.tooltip,
        ...tooltip,
        ...(props.tooltip || {}),
      },
    };
  }, [props, processedData]);

  const legendItems = useMemo(() => {
    const { data, color, seriesField, slider, descName } = config as any;
    if (!seriesField || slider === false) return [];
    const seriesFieldSet: Set<string> = new Set();
    const visibleObj: any = {};
    data?.forEach((item: any) => {
      if (item?.[seriesField as string]) {
        seriesFieldSet.add(item?.[seriesField as string]);
        visibleObj[item?.[seriesField as string]] = true;
      }
    });
    const dataFields = [...seriesFieldSet].map((name, index) => ({
      name,
      desc: descName && data?.find((v: any) => v?.[seriesField as string] === name)?.[descName],
      color: (color as string[])?.[index],
    }));
    setVisibleSeries(visibleObj);
    return dataFields;
  }, [config]);

  const filteredData = useMemo(() => {
    const { seriesField, slider, data, color } = config;
    if (!seriesField || slider === false) return {};
    const showColorItems = legendItems?.filter((item) => visibleSeries[item.name]);
    return {
      data: [...(data || [])].filter((item) => visibleSeries[item[seriesField as string]]),
      color: (color as string[])?.filter((val) => showColorItems?.find((item) => item.color === val)),
    };
  }, [config, visibleSeries, legendItems]);

  const handleLegendChange = useCallback((type: string) => {
    setVisibleSeries((prev) => ({ ...prev, [type]: !prev[type] }));
  }, []);

  return (
    <>
      <AntdLine {...config} {...filteredData} />
      {props.slider !== false && props.legend !== false && (
        <div className={styles.legend}>
          {legendItems?.map((item) => (
            <div className={styles.legendItems} key={item.name} onClick={() => handleLegendChange(item.name)}>
              <span
                className={styles.point}
                style={{
                  background: visibleSeries[item.name] ? item.color : '#D8D8D8',
                }}
              ></span>
              <span
                style={{
                  color: visibleSeries[item.name] ? '#4E5969' : '#D8D8D8',
                }}
              >
                {item.desc || item.name}
              </span>
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default Line;
