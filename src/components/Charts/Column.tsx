import { Column as AntdColumn, ColumnConfig } from '@ant-design/plots';
import React, { FC, useEffect, useMemo, useState } from 'react';
import { defaultConfig } from './utils';

interface IProps extends ColumnConfig {}

const Column: FC<IProps> = ({ tooltip, legend, xAxis, ...extraProps }) => {
  const [key, setKey] = useState(0);

  const config = useMemo(
    () => ({
      ...defaultConfig,
      tooltip: {
        ...defaultConfig.tooltip,
        ...(tooltip || {}),
        domStyles: {
          ...defaultConfig.tooltip.domStyles,
          ...((tooltip as any)?.domStyles || {}),
        },
      },
      legend: {
        ...defaultConfig.legend,
        ...(legend || {}),
      },
      xAxis: {
        ...defaultConfig.xAxis,
        label: {
          ...defaultConfig.xAxis?.label,
          ...((xAxis as any)?.label || {}),
        },
      },
      minColumnWidth: 10,
      maxColumnWidth: 50,
      ...extraProps,
    }),
    [extraProps, tooltip]
  );

  useEffect(() => {
    setKey((prevKey) => prevKey + 1);
  }, [extraProps.height]);

  return <AntdColumn key={key} {...config} />;
};

export default Column;
