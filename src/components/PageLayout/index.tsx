import { headerHeight } from '@/utils/constants';
import React, { CSSProperties } from 'react';
import styles from './index.scss';

interface IPageLayout {
  style?: CSSProperties;
  children: React.ReactNode;
  withSubtitle?: boolean;
}
const PageLayout = (props: IPageLayout) => {
  return (
    <div
      className={styles.pageLayout}
      style={{
        minHeight: `calc(100vh - ${headerHeight} - 32px ${props.withSubtitle ? '- 32px' : ''})`,
        ...(props.style || {}),
      }}
    >
      <div
        className={`${styles.scrollWrapper} scroll-wrapper`}
        style={{ height: `calc(100vh - ${headerHeight} - 32px ${props.withSubtitle ? '- 32px' : ''})` }}
      >
        {props.children}
      </div>
    </div>
  );
};

export default PageLayout;
