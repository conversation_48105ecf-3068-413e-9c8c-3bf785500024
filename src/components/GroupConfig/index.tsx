import Table from '@/components/Table';
import { createStrategyGroup, getStrategyGroupList } from '@/services/ce';
import { useDispatch, useLocation, useSelector } from '@magi/magi';
import { Button, Input, message, Modal } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';

const GroupConfig = () => {
  const location: any = useLocation();
  const { groupList, currentTenantNo: _currentTenantNo } = useSelector((state: { global: any }) => state.global);
  const currentTenantNo = _currentTenantNo || location?.query?.tenantNo;

  const [visible, setVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    if (editVisible) {
      setName('');
    }
  }, [editVisible]);

  const columns = useMemo<any[]>(() => {
    return [
      {
        title: '分组名称',
        dataIndex: 'groupName',
        width: 200,
        key: 'groupName',
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) {
            return '';
          }
          return text;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreated',
        key: 'gmtCreated',
        sorter: (a: any, b: any) => new Date(a.gmtCreated).getTime() - new Date(b.gmtCreated).getTime(),
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) {
            return '';
          }
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '更新人',
        dataIndex: 'modifier',
        key: 'modifier',
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) {
            return '';
          }

          return text;
        },
      },
      {
        title: '更新时间',
        dataIndex: 'gmtModified',
        key: 'gmtModified',
        sorter: (a: any, b: any) => new Date(a.gmtModified).getTime() - new Date(b.gmtModified).getTime(),
        render: (text: string, record: IFieldConfig) => {
          if (record.editing) {
            return '';
          }
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
    ];
  }, []);

  const handleOk = async () => {
    if (!name) {
      message.error('分组名不可为空');
      return;
    }
    setLoading(true);
    const res = await createStrategyGroup({
      groupName: name,
      tenantNo: currentTenantNo,
    });
    if (res.data.success) {
      const groupRes = await getStrategyGroupList({ tenantNo: currentTenantNo });
      if (groupRes.data.success) {
        const data = groupRes.data.value.map((item: any) => ({ label: item.groupName, value: item.id, ...item })) || [];
        dispatch({
          type: 'global/save',
          payload: {
            groupList: data,
          },
        });
      }
      setVisible(false);
    }
    setEditVisible(false);
    setLoading(false);
  };

  return (
    <>
      <Button onClick={() => setVisible(true)} type="primary">
        管理分组
      </Button>
      <Modal onCancel={() => setVisible(false)} footer={null} width={1000} title="管理分组" open={visible}>
        <div>
          <Button style={{ margin: 10 }} onClick={() => setEditVisible(true)} type="primary">
            新建分组
          </Button>
          <Table style={{ margin: 10 }} columns={columns} dataSource={groupList} pagination={false} />
        </div>
      </Modal>
      <Modal
        confirmLoading={loading}
        onOk={handleOk}
        onCancel={() => setEditVisible(false)}
        open={editVisible}
        title="新建分组"
      >
        <Input
          maxLength={32}
          showCount
          placeholder="输入分组名称"
          value={name}
          onInput={(e: any) => setName(e.target.value)}
        />
      </Modal>
    </>
  );
};

export default GroupConfig;
