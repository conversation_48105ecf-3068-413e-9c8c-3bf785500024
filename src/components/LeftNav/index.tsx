import { ReactComponent as Caozu<PERSON>I<PERSON> } from '@/assets/caozuoshouce.svg';
import female from '@/assets/female.png';
import male from '@/assets/male.png';
import siderBg from '@/assets/sider-bg.svg';
import { ReactComponent as TuichuIcon } from '@/assets/tuichu.svg';
import useRobotData from '@/hooks/useRobotData';
import { SSOLogOut } from '@/utils/sso';
import { history, useDispatch, useLocation, useSelector } from '@magi/magi';
import { Layout, Menu, MenuProps, Popover } from 'antd';
import classNames from 'classnames';
import React, { useCallback, useMemo, useRef } from 'react';
import BigLogo from '../../assets/logo.png';
import styles from './index.scss';
import { interiorMenu, items, MENU_KEYS } from './menu';

const LEFT_NAV = 'left-nav';

export const getFirstPage = (data: { isRedLine?: string; tenantNo?: string; isStrategy?: boolean }) =>
  `/${data?.isRedLine === '1' ? (data?.isStrategy ? 'strategyTemplate' : 'redLineStrategyConfig') : 'strategyConfig'}?tenantNo=${data?.tenantNo}`;

const whiteMenuKeys = [MENU_KEYS._TEMPLATE_MANAGE, MENU_KEYS.STRATEGY_TEMPLATE, MENU_KEYS.AI_TEMPLATE];
const noRedLineMenuKeys = [
  MENU_KEYS.STRATEGY_CONFIG,
  MENU_KEYS.DAILY_REPORTS,
  MENU_KEYS.TENDENCY,
  MENU_KEYS._SEARCH,
  MENU_KEYS.BASE_CONFIG,
  MENU_KEYS.BATCH_TEST,
  MENU_KEYS.RULE_OPTIMIZE,
];
const redLineMenuKeys = [
  MENU_KEYS._RED_LINE_RULE,
  MENU_KEYS.RED_LINE_STRATEGY_CONFIG,
  MENU_KEYS.RED_LINE_DATA_OVERVIEW,
];
const realTimeMenuKeys = [MENU_KEYS.REAL_HIT_RESULT];
const smallModelKeys = [MENU_KEYS.AB_TEST_DATA];

const LeftNav = () => {
  const {
    userInfo = {},
    currentTenantNo: _currentTenantNo,
    isStrategy,
    whitelistPermission,
  } = useSelector((state: { global: any }) => state.global);
  const location: any = useLocation();
  const dispatch = useDispatch();
  const avaterDom = useRef<any>(null);
  const { isRedLineRobot } = useRobotData();

  const onClick: MenuProps['onClick'] = useCallback(
    (e) => {
      const tenantNo = location?.query?.tenantNo;
      history.push(tenantNo ? `/${e.key}?tenantNo=${tenantNo}` : `/${e.key}`);
    },
    [history, location?.query?.tenantNo]
  );

  const selectedKeys = useMemo(() => {
    const pathname = location.pathname;
    let res = pathname.substring(1);
    Object.entries(interiorMenu).forEach(([key, value]) => {
      if (value.find((v) => v.key === res)) {
        res = key;
      }
    });
    return [res];
  }, [location.pathname]);

  const menuItems = useMemo(() => {
    // 白名单判断显示菜单
    let newItems: any = isStrategy
      ? items
      : items?.filter((item) => !whiteMenuKeys.includes((item as { key: string }).key));
    // 判断是否是红线机器人
    newItems = isRedLineRobot
      ? newItems
          .filter((item: any) => !noRedLineMenuKeys.includes((item as { key: string }).key))
          .map((item: any) => ({
            ...item,
            children: item?.children?.filter((v: any) => !noRedLineMenuKeys.includes(v.key)),
          }))
      : newItems.filter((item: any) => !redLineMenuKeys.includes((item as { key: string }).key));
    // 判断是否支持了实时质检/小模型
    const { realTimeQuality, smallModel } = whitelistPermission || {};
    newItems = newItems.map((item: any) => ({
      ...item,
      children: item?.children?.filter(
        (v: any) =>
          (realTimeQuality?.includes(_currentTenantNo) ? true : !realTimeMenuKeys.includes(v.key)) &&
          (smallModel?.includes(_currentTenantNo) ? true : !smallModelKeys.includes(v.key))
      ),
    }));
    return newItems;
  }, [isStrategy, isRedLineRobot, whitelistPermission, _currentTenantNo]);

  return (
    <Layout.Sider
      collapsible
      collapsed
      trigger={null}
      className={styles.sider}
      collapsedWidth={'var(--menu-width)'}
      style={{ flexShrink: 0, background: `url(${siderBg}) no-repeat 100% 100%`, backgroundSize: 'cover' }}
    >
      <div
        className={styles.container}
        onClick={() => {
          dispatch({
            type: 'global/save',
            payload: {
              currentTenantNo: '',
            },
          });
          history.push('/');
        }}
      >
        <img src={BigLogo} width={32} height={32} />
      </div>
      <Menu
        className={classNames(LEFT_NAV, styles.menu)}
        selectedKeys={selectedKeys}
        onClick={onClick}
        style={{ width: '100%', flex: 1, overflowY: 'auto', background: 'transparent' }}
        mode="inline"
        theme="dark"
        items={menuItems}
      />
      <div className={styles.meauAvater} ref={avaterDom}>
        <Popover
          placement="rightTop"
          overlayClassName={styles.meauAvaterPopCon}
          trigger={'hover'}
          getPopupContainer={() => avaterDom.current}
          content={
            <>
              <div className={styles.top}>
                {userInfo?.sex ? (
                  <img src={userInfo.sex === '男' ? male : female} alt="" />
                ) : (
                  <div className={styles.avatar}>{userInfo.name?.substring(0, 1)}</div>
                )}
                <div className={styles.name}>
                  <h3>{userInfo?.name || '未知用户'}</h3>
                  <div className={styles.email}>{userInfo?.email || '未知邮箱'}</div>
                </div>
              </div>
              <div className={styles.btn}>
                <div
                  className={styles.btnItem}
                  onClick={() => {
                    window.open(
                      'https://doc.weixin.qq.com/doc/w3_AGQAlwaHALg0ls3c1uETkCuTqWVTH?scode=AO8A8gepAA4JISGdyyAGQAlwaHALg'
                    );
                  }}
                >
                  <CaozuoIcon />
                  <p>操作手册</p>
                </div>
                <div className={styles.btnItem} onClick={SSOLogOut}>
                  <TuichuIcon />
                  <p>退出登录</p>
                </div>
              </div>
            </>
          }
        >
          {userInfo?.sex ? (
            <img src={userInfo.sex === '男' ? male : female} alt="" />
          ) : (
            <div className={styles.avatar}>{userInfo.name?.substring(0, 1)}</div>
          )}
        </Popover>
      </div>
    </Layout.Sider>
  );
};

export default LeftNav;
