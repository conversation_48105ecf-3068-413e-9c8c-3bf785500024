.container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--header-height);
  margin-bottom: 4px;
  cursor: pointer;
}

.sider {
  :global {
    .ant-layout-sider-children {
      display: flex;
      flex-direction: column;
    }
  }
}

.menu {
  :global {
    .ant-menu-item,
    .ant-menu-submenu {
      margin: 4px 12px !important;
      border-radius: 12px !important;
      &:hover {
        .ant-menu-submenu-title {
          color: #fff !important;
        }
      }
    }
    .ant-menu-item,
    .ant-menu-submenu .ant-menu-submenu-title {
      width: initial !important;
      height: initial !important;
      display: flex !important;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px 0;
      svg {
        width: 24px;
        height: 24px;
      }
      .ant-menu-title-content {
        font-size: 12px;
        line-height: 12px;
        opacity: 1 !important;
        margin: 4px 0 0 !important;
      }
    }
  }
}

.meauAvater {
  margin: 24px auto;
  text-align: center;
  width: 40px;
  height: 40px;
  img {
    width: 100%;
  }
}

.meauAvaterPopCon {
  padding: 20px 0;
  background: linear-gradient(0deg, #fff 61.64%, #e9f0ff 100%);
  border-radius: 12px;
  border: 1px solid #f9fafb;
  box-sizing: border-box;
  box-shadow: 0px 4px 12px 0px rgba(24, 27, 37, 0.08);
  :global {
    .ant-popover-inner {
      background: transparent !important;
      padding: 20px 0 !important;
    }
  }
  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0 20px;
    img {
      width: 40px;
      height: 40px;
    }
    .name {
      margin-left: 16px;
      h3 {
        margin: 0;
        color: #0e121b;
        font-size: 16px;
        font-weight: 500;
      }
    }
    .email {
      color: #99a0ae;
      font-weight: 400;
      font-size: 12px;
    }
  }
  .btn {
    display: flex;
    flex-direction: row;
    .btnItem {
      flex: 1;
      margin: 20px 10px 0;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #525866;
      min-width: 112px;
      &:hover {
        color: var(--primary-color);
        p {
          color: var(--primary-color);
        }
      }
      svg {
        width: 24px;
        height: 24px;
      }
      p {
        color: #0e121b;
        margin: 4px 0 10px;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}
.avatar {
  font-size: 14px;
  font-weight: 500;
  background-color: #a28ff5;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 40px;
}

.popup {
  :global {
    .ant-menu {
      background: linear-gradient(0deg, #fff 61.64%, #e9f0ff 100%) !important;
      border: 1px solid #f9fafb;
      box-sizing: border-box;
      padding: 10px !important;
      border-radius: 12px !important;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      min-width: initial !important;
      max-width: 285px;
      box-shadow: 0 4px 12px 0 rgba(24, 27, 37, 0.08) !important;
      .ant-menu-item {
        width: 68px !important;
        height: initial !important;
        display: flex !important;
        flex-direction: column;
        align-items: center;
        padding: 10px 0 !important;
        margin: 10px !important;
        border-radius: 12px !important;
        svg {
          width: 24px;
          height: 24px;
        }
        .ant-menu-title-content {
          font-size: 12px;
          margin: 4px 0 0 !important;
          white-space: pre-wrap;
          word-break: break-all;
          padding: 0 10px;
          text-align: center;
          line-height: 18px;
        }
        &:not(.ant-menu-item-selected) {
          &:hover {
            svg {
              color: var(--primary-color) !important;
            }
            .ant-menu-title-content {
              color: var(--primary-color);
            }
          }
          svg {
            color: #4e5969 !important;
          }
          .ant-menu-title-content {
            color: #0e121b;
          }
        }
      }
    }
  }
}
