import { ReactComponent as AbTestIcon } from '@/assets/menu-icons/abTest.svg';
import { ReactComponent as AiTemplateIcon } from '@/assets/menu-icons/aiTemplate.svg';
import { ReactComponent as BaseConfigIcon } from '@/assets/menu-icons/baseConfig.svg';
import { ReactComponent as BatchTestIcon } from '@/assets/menu-icons/batchTest.svg';
import { ReactComponent as DailyReportsIcon } from '@/assets/menu-icons/dailyReports.svg';
import { ReactComponent as HitResultIcon } from '@/assets/menu-icons/hitResult.svg';
import { ReactComponent as IntelligentRuleOptimizationIcon } from '@/assets/menu-icons/intelligentRuleOptimization.svg';
import { ReactComponent as OtherIcon } from '@/assets/menu-icons/other.svg';
import { ReactComponent as QualityControlSkillIcon } from '@/assets/menu-icons/qualityControlSkill.svg';
import { ReactComponent as RealHitResultIcon } from '@/assets/menu-icons/realHitResult.svg';
import { ReactComponent as RedLineDataOverviewIcon } from '@/assets/menu-icons/redLineDataOverview.svg';
import { ReactComponent as RedLineDataStatementIcon } from '@/assets/menu-icons/redLineDataStatement.svg';
import { ReactComponent as RedLineRuleIcon } from '@/assets/menu-icons/redLineRule.svg';
import { ReactComponent as RedLineStrategyConfigIcon } from '@/assets/menu-icons/redLineStrategyConfig.svg';
import { ReactComponent as RedLineTrendIcon } from '@/assets/menu-icons/redLineTrend.svg';
import { ReactComponent as ResultAnalysisIcon } from '@/assets/menu-icons/resultAnalysis.svg';
import { ReactComponent as SearchIcon } from '@/assets/menu-icons/search.svg';
import { ReactComponent as StrategyConfigIcon } from '@/assets/menu-icons/strategyConfig.svg';
import { ReactComponent as StrategyTemplateIcon } from '@/assets/menu-icons/strategyTemplate.svg';
import { ReactComponent as TemplateManagementIcon } from '@/assets/menu-icons/templateManagement.svg';
import { ReactComponent as TendencyIcon } from '@/assets/menu-icons/tendency.svg';
import { ReactComponent as TestSetManageIcon } from '@/assets/menu-icons/testSetManage.svg';
import { ReactComponent as QualityTagIcon } from '@/assets/qualityTag.svg';
import { ReactComponent as ServiceFieldIcon } from '@/assets/serviceField.svg';
import { MenuProps } from 'antd';
import React from 'react';
import styles from './index.scss';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type,
    popupClassName: styles.popup,
  } as MenuItem;
}

/**
 * 菜单key
 * _开头的为自定义，否则对于页面pathname
 */
export const MENU_KEYS = {
  STRATEGY_CONFIG: 'strategyConfig', // 策略管理
  DAILY_REPORTS: 'dailyReports', // 日常报表
  TENDENCY: 'tendency', // 趋势监控
  _SEARCH: '_search', // 查询
  RESULT_ANALYSIS: 'resultAnalysis', // 会话查询
  HIT_RESULT: 'hitResult', // 质检结果查询
  TEST_SET_MANAGE: 'testSetManage', // 结果标注查询
  REAL_HIT_RESULT: 'realHitResult', // 实时质检结果查询
  AB_TEST_DATA: 'abTestData', // AB测试数据
  _TEMPLATE_MANAGE: '_templateManage', // 模版管理
  STRATEGY_TEMPLATE: 'strategyTemplate', // 策略模版
  AI_TEMPLATE: 'aiTemplate', // AI规则模版
  _RED_LINE_RULE: '_redLineRule', // 红线规则
  RED_LINE_STRATEGY_CONFIG: 'redLineStrategyConfig', // 红线策略配置
  RED_LINE_DATA_OVERVIEW: 'redLineDataOverview', // 红线数据总览
  RED_LINE_TENDENCY: 'redLineTendency', // 红线趋势'
  _OTHER: '_other', // 其他
  BASE_CONFIG: 'baseConfig', // 基础配置
  QUALITY_CONTROL_SKILL: 'qualityControlSkill', // 质检技能
  BATCH_TEST: 'batchTest', // 批量测试
  RULE_OPTIMIZE: 'ruleOptimize', // 智能规则优化
  QUALITY_TAG: 'qualityTag', // 质检标签
};

export const items: MenuProps['items'] = [
  getItem('策略管理', MENU_KEYS.STRATEGY_CONFIG, <StrategyConfigIcon />),
  getItem('日常报表', MENU_KEYS.DAILY_REPORTS, <DailyReportsIcon />),
  getItem('趋势监控', MENU_KEYS.TENDENCY, <TendencyIcon />),
  getItem('查询', MENU_KEYS._SEARCH, <SearchIcon />, [
    {
      key: MENU_KEYS.RESULT_ANALYSIS,
      label: '会话查询',
      icon: <ResultAnalysisIcon />,
    },
    {
      key: MENU_KEYS.HIT_RESULT,
      label: '质检结果查询',
      icon: <HitResultIcon />,
    },
    {
      key: MENU_KEYS.TEST_SET_MANAGE,
      label: '结果标注查询',
      icon: <TestSetManageIcon />,
    },
    {
      key: MENU_KEYS.REAL_HIT_RESULT,
      label: '实时质检结果',
      icon: <RealHitResultIcon />,
    },
    {
      key: MENU_KEYS.AB_TEST_DATA,
      label: 'AB测试数据',
      icon: <AbTestIcon />,
    },
  ]),
  getItem('模版管理', MENU_KEYS._TEMPLATE_MANAGE, <TemplateManagementIcon />, [
    {
      key: MENU_KEYS.STRATEGY_TEMPLATE,
      label: '策略模版',
      icon: <StrategyTemplateIcon />,
    },
    {
      key: MENU_KEYS.AI_TEMPLATE,
      label: 'AI规则模版',
      icon: <AiTemplateIcon />,
    },
  ]),
  getItem('红线规则', MENU_KEYS._RED_LINE_RULE, <RedLineRuleIcon />, [
    {
      key: MENU_KEYS.RED_LINE_STRATEGY_CONFIG,
      label: '策略配置',
      icon: <RedLineStrategyConfigIcon />,
    },
    {
      key: MENU_KEYS.RED_LINE_DATA_OVERVIEW,
      label: '数据报表',
      icon: <RedLineDataStatementIcon />,
    },
  ]),
  getItem('其他', MENU_KEYS._OTHER, <OtherIcon />, [
    {
      key: MENU_KEYS.BASE_CONFIG,
      label: '基础配置',
      icon: <BaseConfigIcon />,
    },
    {
      key: MENU_KEYS.QUALITY_CONTROL_SKILL,
      label: '质检技能',
      icon: <QualityControlSkillIcon />,
    },
    {
      key: MENU_KEYS.BATCH_TEST,
      label: '批量执行',
      icon: <BatchTestIcon />,
    },
    {
      key: MENU_KEYS.RULE_OPTIMIZE,
      label: '智能规则优化',
      icon: <IntelligentRuleOptimizationIcon />,
    },
  ]),
];

export const interiorMenu = {
  [MENU_KEYS.BASE_CONFIG]: [
    {
      label: '业务字段',
      key: MENU_KEYS.BASE_CONFIG,
      icon: <ServiceFieldIcon />,
    },
    {
      label: '质检标签',
      key: MENU_KEYS.QUALITY_TAG,
      icon: <QualityTagIcon />,
    },
  ],
  [MENU_KEYS.RED_LINE_DATA_OVERVIEW]: [
    {
      label: '数据总览',
      key: MENU_KEYS.RED_LINE_DATA_OVERVIEW,
      icon: <RedLineDataOverviewIcon />,
    },
    {
      label: '红线趋势',
      key: MENU_KEYS.RED_LINE_TENDENCY,
      icon: <RedLineTrendIcon />,
    },
  ],
};

export const getPageData = (pathname?: string) => {
  let data;
  for (const item of items) {
    if ((item as any).children?.length) {
      const res = (item as any).children.find((v: any) => v.key === pathname);
      if (res) {
        data = res;
        break;
      }
    } else if ((item as any).key === pathname) {
      data = item;
      break;
    }
  }
  return data;
};
