import { DatePicker, DatePickerProps } from 'antd';
import React, { FC, useCallback, useEffect, useState } from 'react';
import { RANGE_PRESETS } from '../../utils/constants';

const { RangePicker } = DatePicker;

interface IProps extends DatePickerProps {
  rangeDays?: any;
  onChange?: any;
}

const RangeTimePicker: FC<IProps> = ({ onChange, defaultValue, rangeDays, ...restProps }) => {
  const [dates, setDates] = useState<any>(defaultValue);
  const [value, setValue] = useState<any>(defaultValue);

  const disabledDate = useCallback(
    (current: any) => {
      if (!dates) return false;
      if (!rangeDays) return false;
      const tooLate = dates[0] && current.diff(dates[0], 'days') >= rangeDays;
      const tooEarly = dates[1] && dates[1].diff(current, 'days') >= rangeDays;
      return !!tooEarly || !!tooLate;
    },
    [dates, rangeDays]
  );

  const onOpenChange = useCallback(
    (open) => {
      if (open) {
        setDates(defaultValue);
      } else {
        setDates(null);
      }
    },
    [defaultValue]
  );

  useEffect(() => {
    onChange(defaultValue);
  }, [defaultValue]);

  return (
    <RangePicker
      format={{
        format: 'YYYY-MM-DD HH:mm:ss',
        type: 'mask',
      }}
      presets={RANGE_PRESETS}
      value={dates || value}
      disabledDate={disabledDate}
      onCalendarChange={(val) => {
        setDates(val);
      }}
      onChange={(val) => {
        setValue(val);
        onChange(val);
      }}
      onOpenChange={onOpenChange}
      changeOnBlur
      {...restProps}
    />
  );
};

export default RangeTimePicker;
