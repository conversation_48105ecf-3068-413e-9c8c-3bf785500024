// 主题变量定义 - 根据设计稿
:root {
  // 主色调
  --primary-color: #5865F2; // 主色 - 蓝紫色
  --primary-hover: #4752C4; // 主色悬停
  --primary-active: #3C45A5; // 主色按下

  // 中性色
  --text-primary: #1D2129; // 主文本色
  --text-secondary: #4E5969; // 次要文本色
  --text-tertiary: #86909C; // 辅助文本色
  --text-quaternary: #C9CDD4; // 占位符文本色

  // 背景色
  --bg-primary: #FFFFFF; // 主背景色
  --bg-secondary: #F5F5F7; // 次要背景色（页面背景）
  --bg-tertiary: #F2F3F5; // 三级背景色
  --bg-hover: #F7F8FA; // 悬停背景色

  // 边框色
  --border-primary: #E5E6EB; // 主边框色
  --border-secondary: #F2F3F5; // 次要边框色

  // 功能色
  --success-color: #00B42A;
  --warning-color: #FF7D00;
  --error-color: #F53F3F;
  --info-color: #0FC6C2;

  // 其他
  --radius-sm: 4px;
  --radius-base: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  // 阴影
  --shadow-sm: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  --shadow-base: 0px 4px 8px -2px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0px 8px 16px -4px rgba(0, 0, 0, 0.08);
}

// Antd 主题覆盖
.ant-btn {
  font-weight: 500;
  border-radius: var(--radius-base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(88, 101, 242, 0.2);

    &:hover {
      background-color: var(--primary-hover) !important;
      border-color: var(--primary-hover) !important;
      box-shadow: 0 4px 8px rgba(88, 101, 242, 0.3);
      transform: translateY(-1px);
    }

    &:active {
      background-color: var(--primary-active) !important;
      border-color: var(--primary-active) !important;
      transform: translateY(0);
    }
  }

  &-default,
  &-ghost {
    color: var(--text-primary);
    border-color: var(--border-primary);

    &:hover {
      color: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      background-color: rgba(88, 101, 242, 0.04);
    }
  }

  &-text {
    &:hover {
      background-color: var(--bg-hover);
    }
  }
}

// 选择框主题
.ant-select {
  &:not(.ant-select-disabled):hover .ant-select-selector {
    border-color: var(--primary-color) !important;
  }

  &.ant-select-focused:not(.ant-select-disabled) .ant-select-selector {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.1);
  }

  .ant-select-selector {
    border-radius: var(--radius-base);
  }
}

// 输入框主题
.ant-input,
.ant-input-affix-wrapper {
  border-radius: var(--radius-base);

  &:hover {
    border-color: var(--primary-color) !important;
  }

  &:focus,
  &-focused {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.1);
  }
}

// 开关主题
.ant-switch {
  &-checked {
    background-color: var(--primary-color) !important;

    &:hover:not(.ant-switch-disabled) {
      background-color: var(--primary-hover) !important;
    }
  }
}

// 复选框主题
.ant-checkbox {

  &-wrapper:hover .ant-checkbox-inner,
  &:hover .ant-checkbox-inner,
  &-input:focus+.ant-checkbox-inner {
    border-color: var(--primary-color);
  }

  &-checked .ant-checkbox-inner {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }
}

// 单选框主题
.ant-radio {

  &-wrapper:hover .ant-radio-inner,
  &:hover .ant-radio-inner,
  &-input:focus+.ant-radio-inner {
    border-color: var(--primary-color);
  }

  &-checked .ant-radio-inner {
    border-color: var(--primary-color);

    &::after {
      background-color: var(--primary-color);
    }
  }
}

// 标签页主题
.ant-tabs {
  &-tab {
    &:hover {
      color: var(--primary-color);
    }

    &-active {
      color: var(--primary-color);
    }
  }

  &-ink-bar {
    background-color: var(--primary-color);
  }
}

// 分页主题
.ant-pagination {
  &-item {
    border-radius: var(--radius-base);

    &:hover {
      border-color: var(--primary-color);

      a {
        color: var(--primary-color);
      }
    }

    &-active {
      background-color: var(--primary-color);
      border-color: var(--primary-color);

      a {
        color: white;
      }
    }
  }

  &-prev,
  &-next {
    .ant-pagination-item-link {
      border-radius: var(--radius-base);
    }
  }
}

// 表格主题
.ant-table {
  border-radius: var(--radius-base);
  overflow: hidden;

  &-thead>tr>th {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 1px solid var(--border-primary);
  }

  &-tbody>tr {
    &:hover>td {
      background-color: var(--bg-hover);
    }

    &:last-child>td {
      border-bottom: none;
    }
  }

  &-cell {
    border-bottom: 1px solid var(--border-secondary);
  }
}

// 树形控件主题
.ant-tree {
  &-node-content-wrapper {
    border-radius: var(--radius-base);
    transition: all 0.2s;

    &:hover {
      background-color: var(--bg-hover);
    }

    &.ant-tree-node-selected {
      background-color: var(--primary-color) !important;
      color: white !important;
    }
  }
}

// 模态框主题
.ant-modal {
  .ant-modal-content {
    border-radius: var(--radius-xl);
    overflow: hidden;
  }

  .ant-modal-header {
    border-bottom: 1px solid var(--border-primary);
  }

  .ant-modal-footer {
    border-top: 1px solid var(--border-primary);
  }
}

// 下拉菜单主题
.ant-dropdown {
  .ant-dropdown-menu {
    border-radius: var(--radius-base);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-primary);

    .ant-dropdown-menu-item {
      &:hover {
        background-color: var(--bg-hover);
      }
    }
  }
}