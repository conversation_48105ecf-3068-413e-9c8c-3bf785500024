FROM base-registry.zhonganinfo.com/env/node:16.20.0 


ENV NPM_CONFIG_LOGLEVEL warn

ENV NPM_CONFIG_REGISTRY http://npm.zhonganinfo.com

ENV SASS_BINARY_SITE https://npmmirror.com/mirrors/node-sass

ENV SENTRYCLI_CDNURL https://npmmirror.com/mirrors/sentry-cli

WORKDIR /www


COPY package*.json /www/

RUN npm install --no-optional

COPY . /www
RUN echo '[magi]: 开始构建所有环境静态资源 ======>'

# RUN npm run build:test
# RUN npm run build:pre
# RUN npm run build:prd
RUN npm run build


RUN echo '[magi]: 完成所有环境静态资源构建 <======'

EXPOSE 8080

CMD ["node", "server.js"]
