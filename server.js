const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const axios = require('axios');

const app = express();
const envDir = process.env.DEPLOY_ENV || process.env.ENV || 'unknown';
const isDev = process.env.PROJECT_ID && envDir === 'test';
const serverUrl = process.env.SERVER_URL;

// 国际oss
const intlOssServiceUrl = {
  uat: 'https://oss-cn-hongkong-internal.aliyuncs.com',
  prd: 'https://oss-cn-hongkong-internal.aliyuncs.com',
};

const ossServiceUrl =
  process.env.OSS_SERVICE || intlOssServiceUrl[envDir] || 'http://za-ark-ce-test.oss-cn-hzjbp-b-internal.aliyuncs.com';
app.use(
  cors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204,
  })
);

app.use(morgan('tiny'));

app.use(express.static(path.join(__dirname, 'dist')));

const proxy = require('http-proxy-middleware').createProxyMiddleware;
app.use(
  '/',
  // proxy([`/wechat`], {
  //   target: `http://open-prd.oss-cn-hzfinance.aliyuncs.com/wechat`,
  //   changeOrigin: true,
  //   secure: false,
  //   pathRewrite: {
  //     '^/wechat': '/',
  //   },
  // })
  // oss代理
  proxy(['/oss-proxy'], {
    target: ossServiceUrl,
    changeOrigin: true,
    pathRewrite: {
      '^/oss-proxy': '',
    },
  })
);

isDev &&
  app.use(
    '/',
    proxy([`/ce`], {
      target: serverUrl || 'http://za-ark-ce.test.za.biz',
      changeOrigin: true,
      pathRewrite: {
        '^/ce': '',
      },
    })
  );

//三方域名的oss获取文件
app.get('/tripartite-oss-proxy', async (req, res) => {
  const targetUrl = req.query.url;
  console.log('tripartite oss proxy targetUrl: ', targetUrl);
  try {
    const response = await axios({
      method: 'get',
      url: targetUrl,
      responseType: 'stream',
    });
    // 设置适当的响应头
    res.setHeader('Content-Type', response.headers['content-type']);
    res.setHeader('Content-Disposition', response.headers['content-disposition'] || 'attachment');
    // 将文件流直接pipe到响应中
    response.data.pipe(res);
  } catch (error) {
    console.error('tripartite oss proxy error:', JSON.stringify(error || {}));
    res.status(500).json({
      error: 'tripartiteOSS interface Failed to fetch data: ' + JSON.stringify(error || {})
    });
  }
});

app.get('/', function (req, res) {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.get('/health', function (req, res) {
  res.send('ok');
});

app.listen(8080, () => {
  console.log(`[ossServiceUrl::${ossServiceUrl}]`);
  console.log(`[env::${envDir}]za-ark-micro-services-static listening on port 8080`);
});