{"name": "@magi/template-magi", "version": "0.0.45", "author": "wangming", "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider DEPLOY_ENV=dev SASS_SILENCE_DEPRECATIONS=legacy-js-api,global-builtin,color-functions,slash-div magi dev", "start:za": "cross-env NODE_OPTIONS=--openssl-legacy-provider DEPLOY_ENV=dev MODE=za SASS_SILENCE_DEPRECATIONS=legacy-js-api,global-builtin,color-functions,slash-div magi dev", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider SASS_SILENCE_DEPRECATIONS=legacy-js-api,global-builtin,color-functions,slash-div magi build", "build:prd": "cross-env NODE_OPTIONS=--openssl-legacy-provider DEPLOY_ENV=prd SASS_SILENCE_DEPRECATIONS=legacy-js-api,global-builtin,color-functions,slash-div magi build", "build:test": "cross-env NODE_OPTIONS=--openssl-legacy-provider DEPLOY_ENV=test SASS_SILENCE_DEPRECATIONS=legacy-js-api,global-builtin,color-functions,slash-div magi build", "build:pre": "cross-env NODE_OPTIONS=--openssl-legacy-provider DEPLOY_ENV=pre SASS_SILENCE_DEPRECATIONS=legacy-js-api,global-builtin,color-functions,slash-div magi build", "server": "magi deploy", "lint": "magi lint", "lint:report": "magi lint --report", "magi": "magi", "precommit": "magi lint"}, "dependencies": {"@ant-design/plots": "^1.2.5", "@magi/magi": "^0.0.39", "ahooks": "^2.10.14", "antd": "^5.15.1", "big.js": "^6.2.1", "codemirror": "^5.65.17", "dayjs": "^1.11.10", "http-proxy-middleware": "^2.0.6", "lodash": "^4.17.21", "react": "^17.0.0", "react-codemirror2": "^8.0.0", "react-countup": "^6.5.3", "react-dom": "^17.0.0", "react-fast-marquee": "^1.6.4", "react-highlight-words": "^0.20.0", "react-markdown": "^8.0.7", "remark-gfm": "^3.0.1", "styled-components": "^6.1.8", "table-render": "^2.1.4"}, "devDependencies": {"@magi/cli": "^0.0.39", "@types/big.js": "^6.2.2", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-highlight-words": "^0.20.0", "cors": "^2.8.5", "cross-env": "^7.0.2", "events": "^3.3.0", "express": "^4.18.2", "morgan": "^1.10.0", "prettier-plugin-organize-imports": "^4.0.0", "typescript": "^4.7.4"}, "repository": {"type": "git", "url": "git+https://git.zhonganinfo.com/zafe/magi-framework.git"}, "publishConfig": {"access": "public", "registry": "http://npm.zhonganinfo.com"}, "engines": {"node": ">=10.0.0"}, "gitHead": "2144d63d0d2acfcde2158d1dfa93522298e60769"}