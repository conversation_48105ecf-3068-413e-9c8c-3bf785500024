import { defineConfig } from '@magi/magi';
import proxy from './proxy';

/**
 * 通用配置项
 */
export default defineConfig({
  strict: {
    rules: {
      CONFIG_NO_PLUGIN_DISABLE: false,
      CODE_NO_TERNARY_EXPRESSION_EMBED: false,
      DEP_LIMITED_VISION_LIBS: false,
      CODE_COMPONENT_COMPLEX: false,
      CODE_PREFER_USE_CSS_MODULES: false,
      CODE_PREFER_USE_AT: false,
      CODE_NO_UNSAFE_SYNTAX: false,
      FILES_ALL_SCSS: false,
    },
  },
  title: '灵眸AI质检',
  proxy,
  history: {
    type: 'hash',
  },
  favicon: '/favicon.ico',
  define: {
    'process.env.MODE': process.env.MODE,
  },
  // 开发环境优化
  devtool: process.env.NODE_ENV === 'development' ? 'eval-cheap-module-source-map' : false,
  // 代码分割优化
  dynamicImport: {
    loading: '@/components/Loading',
  },
  // 注意：Sass 警告已通过环境变量 SASS_SILENCE_DEPRECATIONS 在 package.json 中处理
});
