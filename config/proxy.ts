const proxy: any = {
  // 示例，代理后端接口
  dev: {
    '/nsso': {
      target: 'http://hfe-bops-gateway.test.za.biz',
      logLevel: 'debug',
      changeOrigin: true,
      xfwd: true,
    },
    '/ce': {
      target:
        process.env.MODE === 'za'
          ? 'http://zaip-imp-se-service.uat-az4.if.za' // 国际uat环境后端服务
          : 'http://4277999-xk-xqc-intelligent-mics.xline-dev.test.xinke.biz ',
      logLevel: 'debug',
      changeOrigin: true,
      xfwd: true,
      pathRewrite: {
        '^/ce': '/',
      },
    },
    '/wechat': {
      target: `http://open-prd.oss-cn-hzfinance.aliyuncs.com/wechat`,
      changeOrigin: true,
      pathRewrite: {
        '^/wechat': '/',
      },
    },
    '/oss-proxy': {
      target: 'http://za-ark-ce-test.oss-cn-hzjbp-b-internal.aliyuncs.com',
      changeOrigin: true,
      logLevel: 'debug',
      pathRewrite: {
        '^/oss-proxy': '',
      },
    },
  },
  test: {
    '/nsso': {
      target: 'http://hfe-bops-gateway.test.za.biz',
      logLevel: 'debug',
      changeOrigin: true,
      xfwd: true,
    },
    '/api': {
      target: 'http://za-ark-ce.test.za.biz/api',
      logLevel: 'debug',
      changeOrigin: true,
      xfwd: true,
      pathRewrite: {
        '^/api': '/',
      },
    },
  },
  pre: {
    '/nsso': {
      target: 'http://hfe-bops-gateway.pre.za.biz',
      logLevel: 'debug',
      changeOrigin: true,
      xfwd: true,
    },
  },
  prd: {
    '/nsso': {
      target: 'http://hfe-bops-gateway.prd.za.biz',
      logLevel: 'debug',
      changeOrigin: true,
      xfwd: true,
    },
  },
};

export default proxy;
